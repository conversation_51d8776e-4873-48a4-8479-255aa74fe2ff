// Quick test script to verify login API works
async function testLogin() {
  console.log('Testing login API...');
  
  try {
    // Test direct auth service first
    console.log('1. Testing direct auth service...');
    const directResponse = await fetch('http://localhost:4010/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'demo' })
    });
    
    if (directResponse.ok) {
      const directData = await directResponse.json();
      console.log('✅ Direct auth service works:', directData.accessToken ? 'Token received' : 'No token');
    } else {
      console.log('❌ Direct auth service failed:', directResponse.status);
    }
  } catch (error) {
    console.log('❌ Direct auth service error:', error.message);
  }
  
  try {
    // Test gateway
    console.log('2. Testing gateway...');
    const gatewayResponse = await fetch('http://localhost:4000/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'demo' }),
      signal: AbortSignal.timeout(5000) // 5 second timeout
    });
    
    if (gatewayResponse.ok) {
      const gatewayData = await gatewayResponse.json();
      console.log('✅ Gateway works:', gatewayData.accessToken ? 'Token received' : 'No token');
    } else {
      console.log('❌ Gateway failed:', gatewayResponse.status);
    }
  } catch (error) {
    console.log('❌ Gateway error:', error.message);
  }
  
  console.log('Test complete!');
}

testLogin();
