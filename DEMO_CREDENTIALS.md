# A-ReALM Demo Credentials & Authentication Guide

This document provides comprehensive information about the demo authentication system, available test accounts, and how to use them.

## 🎯 Quick Start

**Want to test immediately?**

1. **Open the app**: [http://localhost:5173](http://localhost:5173)
2. **Click "Login"** in the top navigation
3. **Click "Demo User"** or **"Admin User"** button
4. **Click "Sign In"** - you're logged in!

## Authentication System Overview

A‑ReALM supports two modes:

- **Production mode (secure)**
  - ✅ Email + password required (bcrypt-verified against DB)
  - ✅ Roles loaded from database (RBAC)
  - ✅ Tokens include `sub` = user id, `roles`, `orgId`

- **Development/Demo mode (username-only for convenience)**
  - ✅ Username-only login supported for quick demos
  - ✅ Role assignment based on username patterns
  - ⚠️ For demo only. Not used in production.

## 🔑 Demo Accounts

### 👤 Regular User Account
- **Email**: `<EMAIL>`
- **Password**: `user123`
- **Role**: `user`
- **Access**:
  - ✅ View agreements
  - ✅ View closure information
  - ✅ Access basic dashboard features
  - ❌ No admin features

### 👑 Administrator Account
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Role**: `admin`
- **Access**:
  - ✅ All user permissions
  - ✅ Access billing management
  - ✅ Manage fleet operations
  - ✅ Configure integrations
  - ✅ Manage Salik settings
  - ✅ Handle fines administration

### 🆕 Custom Accounts (via Signup)
- **Create any username** through the registration form
- **Choose role** during signup (Regular User or Administrator)
- **Full validation** - proper email format, strong password requirements
- **Instant login** after successful registration

## 🚀 How to Login

### Method 1: Quick Access (Recommended)
1. **Navigate to**: [http://localhost:5173/login](http://localhost:5173/login)
2. **Click**: "Demo User" or "Admin User" button
3. **Click**: "Sign In"
4. **Done!** You're logged in instantly

### Method 2: Manual Login (Production)
1. **Navigate to**: [http://localhost:5173/login](http://localhost:5173/login)
2. **Enter email**: `<EMAIL>` or `<EMAIL>`
3. **Password**: Enter the matching password
4. **Click**: "Sign In"

### Method 3: Registration Flow
1. **Navigate to**: [http://localhost:5173/register](http://localhost:5173/register)
2. **Fill out the form**:
   - Username: Any valid username (3+ chars, letters/numbers/hyphens/underscores)
   - Email: Valid email format required
   - Password: Must contain uppercase, lowercase, and number
   - Confirm Password: Must match
   - Account Type: Choose "Regular User" or "Administrator"
   - Terms: Must agree to terms
3. **Click**: "Create Account"
4. **Automatic login**: You'll be logged in immediately after registration

### Method 4: Direct API Access
```bash
# Gateway (production-style: email + password)
curl -X POST http://localhost:4000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

## API Endpoints

### Login Endpoint
```bash
# Direct to auth service
curl -X POST http://localhost:4010/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "demo"}'

# Through gateway
curl -X POST http://localhost:4000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin"}'
```

### Response Format
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

## Username Patterns (Demo Mode)

In demo mode, the mock authentication assigns roles based on username patterns:

- **Admin Role**: Username contains "admin" (case-insensitive)
  - Examples: `admin`, `administrator`, `admin123`, `test-admin`
- **User Role**: Any other username
  - Examples: `demo`, `user`, `test`, `john.doe`

## Testing Different Scenarios

### Test Regular User Access
```bash
# These usernames will get 'user' role
curl -X POST http://localhost:4000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "demo"}'

curl -X POST http://localhost:4000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser"}'
```

### Test Admin Access
```bash
# These usernames will get 'admin' role
curl -X POST http://localhost:4000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin"}'

curl -X POST http://localhost:4000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "superadmin"}'
```

## JWT Token Information

The JWT tokens contain the following claims:
- `sub`: Username
- `roles`: Array of roles (`["user"]` or `["admin"]`)
- `orgId`: Organization ID (default: "00000000-0000-0000-0000-000000000001")
- `iat`: Issued at timestamp
- `exp`: Expiration timestamp

## Security Notes

⚠️ **Important**:

- Production requires email + password and verifies against the database
- Demo username-only login is enabled only in non-production environments
- Rate limiting is enabled on auth endpoints

## Production Considerations

For production deployment, you would need to:

1. **Implement proper user management**:
   - User registration with email verification
   - Secure password hashing (bcrypt)
   - User profile management

2. **Add database persistence**:
   - User accounts stored in PostgreSQL
   - Role and permission management
   - Session management

3. **Enhance security**:
   - Strong password requirements
   - Rate limiting on auth endpoints
   - Account lockout policies
   - Two-factor authentication

4. **Implement proper signup**:
   - Email verification workflow
   - Admin approval for admin accounts
   - Organization management

## Troubleshooting

### Login Not Working
1. **Check services are running**:
   ```bash
   # Check auth service
   curl http://localhost:4010/healthz
   
   # Check gateway
   curl http://localhost:4000/api/status
   ```

2. **Check browser console** for JavaScript errors

3. **Verify network requests** in browser dev tools

4. **Restart services** if needed:
   ```bash
   # Restart auth service
   pnpm dev --filter @a-realm/auth-service
   
   # Restart gateway
   pnpm dev --filter @a-realm/gateway
   ```

### 504 Gateway Timeout
- Ensure auth service is running on port 4010
- Check gateway proxy configuration
- Restart both gateway and auth service

### Token Issues
- Check JWT secret configuration
- Verify token expiration times
- Clear browser localStorage if needed

## Support

For issues with the demo system:
1. Check the console logs in browser dev tools
2. Verify all services are running with `pnpm dev`
3. Check the terminal output for error messages
4. Restart the development environment if needed
