# 🚗 A-ReALM: Advanced Rental and Leasing Management System

A comprehensive, enterprise-grade vehicle rental and leasing management platform built with modern microservices architecture.

## 📋 Table of Contents

- [Project Overview](#-project-overview)
- [Prerequisites](#-prerequisites)
- [Installation & Setup](#-installation--setup)
- [Running Locally (Development)](#-running-locally-development)
- [Running in Production](#-running-in-production)
- [Usage Instructions](#-usage-instructions)
- [Testing & Quality](#-testing--quality)
- [Troubleshooting & FAQs](#-troubleshooting--faqs)
- [Contributing Guide](#-contributing-guide)
- [Support & Maintenance](#-support--maintenance)

## 🎯 Project Overview

### Purpose
A-ReALM is a modern vehicle rental and leasing management system designed for businesses that need to manage:
- Vehicle fleets and inventory
- Customer relationships and agreements
- Rental reservations and scheduling
- Billing and payment processing
- Operations and maintenance tracking

### Key Features
- **Multi-tenant Architecture**: Support for multiple organizations
- **Role-based Access Control**: Admin and user roles with granular permissions
- **Real-time Monitoring**: Health checks, metrics, and observability
- **Secure Authentication**: JWT-based auth with rate limiting
- **API Documentation**: Interactive Swagger UI for all endpoints
- **Comprehensive Testing**: Unit, integration, and security tests

### Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │   API Gateway   │    │   Auth Service  │
│   (React/Vite)  │◄──►│   (Express)     │◄──►│   (Express)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                    ┌───────────┼───────────┐
                    │           │           │
            ┌───────▼───┐ ┌─────▼─────┐ ┌───▼───────┐
            │Sales      │ │Fleet      │ │Billing    │
            │Service    │ │Service    │ │Service    │
            └───────────┘ └───────────┘ └───────────┘
                    │           │           │
                    └───────────┼───────────┘
                                │
                    ┌───────────▼───────────┐
                    │    PostgreSQL DB      │
                    │    Redis Cache        │
                    │    RabbitMQ Queue     │
                    └───────────────────────┘
```

**Components:**
- **Frontend**: React-based web application with Material-UI
- **API Gateway**: Central routing and load balancing
- **Microservices**: Independent services for different business domains
- **Database**: PostgreSQL with Prisma ORM
- **Cache**: Redis for session management and rate limiting
- **Message Queue**: RabbitMQ for async communication
- **Monitoring**: Prometheus metrics and OpenTelemetry tracing

## 🔧 Prerequisites

### Required Tools
- **Node.js**: Version 18.17.0 or higher
- **pnpm**: Version 8.15.4 (package manager)
- **Docker**: Version 20.0 or higher
- **Docker Compose**: Version 2.0 or higher
- **Git**: For version control

### Optional Tools
- **PostgreSQL**: Version 15+ (if not using Docker)
- **Redis**: Version 7+ (if not using Docker)
- **RabbitMQ**: Version 3+ (if not using Docker)

Note: Messaging is optional. When `RABBITMQ_URL` is not configured or RabbitMQ is unavailable, domain events are skipped gracefully with logs; core flows continue to work.

### Environment Variables
The following environment variables must be configured:

```bash
# Database
POSTGRES_URL="postgresql://postgres:postgres@localhost:5432/a_realm"

# Authentication (Generate with: pnpm generate:secrets)
JWT_SECRET="your-super-secure-jwt-secret-32-chars-min"
JWT_REFRESH_SECRET="your-super-secure-refresh-secret-32-chars-min"

# Cache & Messaging (Optional)
REDIS_URL="redis://localhost:6379"
RABBITMQ_URL="amqp://localhost:5672"

# CORS Configuration
ALLOWED_ORIGINS="http://localhost:5173,http://localhost:3000"

# Service Configuration
NODE_ENV="development"
LOG_LEVEL="info"
PORT="4010"
```

## 🚀 Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd A-ReALM-V1
```

### 2. Install Dependencies
```bash
# Install pnpm if not already installed
npm install -g pnpm@8.15.4

# Install all dependencies
pnpm install
```

### 3. Generate Secure Secrets
```bash
# Generate JWT secrets automatically
pnpm generate:secrets
```

### 4. Configure Environment Variables
```bash
# Copy the example environment file
cp .env.example .env

# Edit the .env file with your configuration
nano .env
```

**Example `.env` file:**
```bash
# Database
POSTGRES_URL="postgresql://postgres:postgres@localhost:5432/a_realm"

# Authentication (Use generated secrets from step 3)
JWT_SECRET="generated-jwt-secret-from-previous-step"
JWT_REFRESH_SECRET="generated-refresh-secret-from-previous-step"

# Optional Services
REDIS_URL="redis://localhost:6379"
RABBITMQ_URL="amqp://localhost:5672"

# CORS
ALLOWED_ORIGINS="http://localhost:5173"

# Environment
NODE_ENV="development"
LOG_LEVEL="info"
```

### 5. Start Supporting Services
```bash
# Start PostgreSQL, Redis, and RabbitMQ with Docker
cd infra
docker-compose up -d

# Verify services are running
docker-compose ps
```

### 6. Set Up Database
```bash
# Generate Prisma client
pnpm --filter @a-realm/db build

# Run database migrations
pnpm --filter @a-realm/db migrate:dev

# Seed initial data (optional)
pnpm seed
```

## 🏃 Running Locally (Development)

### Option 1: Run All Services with Turbo
```bash
# Start all services in development mode
pnpm dev
```

This will start:
- **Web Frontend**: http://localhost:5173
- **API Gateway**: http://localhost:4000
- **Auth Service**: http://localhost:4010
- **Sales Service**: http://localhost:4020
- **Fleet Service**: http://localhost:4030
- **Billing Service**: http://localhost:4040
- **Ops Service**: http://localhost:4050
- **Integrations Service**: http://localhost:4060

### Option 2: Run Services Individually
```bash
# Terminal 1: Start the web frontend
pnpm --filter @a-realm/web dev

# Terminal 2: Start the API gateway
pnpm --filter @a-realm/gateway dev

# Terminal 3: Start auth service
pnpm --filter @a-realm/auth-service dev

# Terminal 4: Start other services as needed
pnpm --filter @a-realm/sales-service dev
```

### Option 3: Use Development Scripts
```bash
# Start all development services
pnpm dev:up

# Stop all development services
pnpm dev:down
```

### Accessing the Application
- **Web Application**: http://localhost:5173
- **API Gateway**: http://localhost:4000
- **API Documentation**: http://localhost:4010/docs
- **Health Checks**: http://localhost:4010/healthz
- **Metrics**: http://localhost:4010/metrics

### Default Login Credentials
After seeding the database:
- **Admin User**: `<EMAIL>` / `admin123`
- **Regular User**: `<EMAIL>` / `user123`

## 🌐 Running in Production

### 1. Build Docker Images
```bash
# Build all services
pnpm build

# Build Docker images
docker build -t a-realm/gateway:latest apps/gateway
docker build -t a-realm/auth-service:latest services/auth-service
docker build -t a-realm/sales-service:latest services/sales-service
docker build -t a-realm/fleet-service:latest services/fleet-service
docker build -t a-realm/billing-service:latest services/billing-service
docker build -t a-realm/ops-service:latest services/ops-service
docker build -t a-realm/web:latest apps/web
```

### 2. Production Environment Setup
```bash
# Create production environment file
cp .env.example .env.production

# Configure production values
nano .env.production
```

**Production `.env` example:**
```bash
NODE_ENV="production"
POSTGRES_URL="***************************************/a_realm"
JWT_SECRET="production-jwt-secret-64-chars-minimum"
JWT_REFRESH_SECRET="production-refresh-secret-64-chars-minimum"
REDIS_URL="redis://prod-redis:6379"
ALLOWED_ORIGINS="https://yourdomain.com"
LOG_LEVEL="warn"
```

### 3. Database Migrations in Production
```bash
# Run migrations (ensure database is accessible)
pnpm --filter @a-realm/db migrate:deploy
```

### 4. Deploy with Docker Compose
```bash
# Create production docker-compose file
cp infra/docker-compose.yml docker-compose.prod.yml

# Edit for production settings
nano docker-compose.prod.yml

# Deploy
docker-compose -f docker-compose.prod.yml up -d
```

### 5. Configure Reverse Proxy (Nginx Example)
```nginx
server {
    listen 80;
    server_name yourdomain.com;

    location / {
        proxy_pass http://localhost:5173;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /api/ {
        proxy_pass http://localhost:4000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 6. Enable HTTPS with Let's Encrypt
```bash
# Install certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📖 Usage Instructions

### Logging In
1. Navigate to http://localhost:5173
2. Use default credentials or create a new account
3. Admin users have access to all features
4. Regular users have limited permissions

### Main Features

#### 1. Customer Management
- **Create Customers**: Add new customer profiles
- **View Customer List**: Browse all customers with search/filter
- **Customer Details**: View complete customer information
- **Edit Customers**: Update customer information

#### 2. Vehicle Fleet Management
- **Add Vehicles**: Register new vehicles in the fleet
- **Vehicle Inventory**: View all available vehicles
- **Vehicle Status**: Track availability, maintenance, etc.
- **Vehicle Details**: Comprehensive vehicle information

#### 3. Reservations & Agreements
- **Create Reservations**: Book vehicles for customers
- **Rental Agreements**: Generate and manage rental contracts
- **Reservation Calendar**: Visual scheduling interface
- **Agreement Templates**: Standardized contract templates

#### 4. Billing & Payments
- **Generate Invoices**: Create bills for rentals
- **Payment Tracking**: Monitor payment status
- **Receipt Management**: Handle payment confirmations
- **Billing Reports**: Financial reporting and analytics

#### 5. Operations Management
- **Maintenance Scheduling**: Plan vehicle maintenance
- **Operational Reports**: Business intelligence dashboards
- **User Management**: Admin functions for user accounts
- **System Configuration**: Application settings

### API Usage
Access interactive API documentation at:
- **Auth Service**: http://localhost:4010/docs
- **Sales Service**: http://localhost:4020/docs
- **Fleet Service**: http://localhost:4030/docs
- **Integrations Service**: http://localhost:4060/docs

Example API calls:
```bash
# Login to get JWT token
curl -X POST http://localhost:4010/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"<EMAIL>","password":"admin123"}'

# Use token for authenticated requests
curl -X GET http://localhost:4020/customers \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🧪 Testing & Quality

### Running Tests
```bash
# Run all tests
pnpm test

# Run tests with coverage
pnpm test:coverage

# Run tests in watch mode
pnpm test:watch

# Run CI tests
pnpm test:ci
```

### Code Quality Checks
```bash
# Run linting
pnpm lint

# Fix linting issues
pnpm lint --fix

# Format code
pnpm format

# Type checking
pnpm typecheck
```

### Security Testing
```bash
# Test CORS configuration
pnpm test:cors

# Test error handling
pnpm test:errors

# Security audit
pnpm audit
```

### CI/CD Pipeline
The project includes GitHub Actions workflows for:
- **Code Quality**: Linting, formatting, type checking
- **Security**: Dependency audits, container scanning
- **Testing**: Unit and integration tests
- **Build**: Docker image creation
- **Deployment**: Automated deployment (when configured)

## 🔧 Troubleshooting & FAQs

### Common Issues

#### Database Connection Errors
```bash
# Check if PostgreSQL is running
docker-compose ps

# Check database logs
docker-compose logs postgres

# Reset database
docker-compose down -v
docker-compose up -d
pnpm --filter @a-realm/db migrate:dev
```

#### CORS Errors
```bash
# Verify ALLOWED_ORIGINS in .env
echo $ALLOWED_ORIGINS

# Test CORS configuration
pnpm test:cors
```

#### Port Already in Use
```bash
# Find process using port
lsof -i :4010

# Kill process
kill -9 <PID>

# Or use different ports in .env
```

#### JWT Token Issues
```bash
# Regenerate secrets
pnpm generate:secrets

# Update .env with new secrets
# Restart services
```

### Checking Logs
```bash
# Service logs (if running with Docker)
docker-compose logs auth-service

# Application logs (if running locally)
# Logs are output to console with structured format
```

### Performance Issues
```bash
# Check system resources
docker stats

# Monitor database performance
# Access pgAdmin or use psql for query analysis

# Check Redis performance
redis-cli info stats
```

### Health Checks
```bash
# Check service health
curl http://localhost:4010/healthz

# Check detailed health status
curl http://localhost:4010/readyz

# View metrics
curl http://localhost:4010/metrics
```

## 🤝 Contributing Guide

### Branching Strategy
- **main**: Production-ready code
- **develop**: Integration branch for features
- **feature/***: Individual feature branches
- **hotfix/***: Critical bug fixes

### Development Workflow
1. **Fork the repository**
2. **Create feature branch**: `git checkout -b feature/your-feature`
3. **Make changes** following code style guidelines
4. **Run tests**: `pnpm test`
5. **Run quality checks**: `pnpm lint && pnpm typecheck`
6. **Commit changes**: Use conventional commit format
7. **Push branch**: `git push origin feature/your-feature`
8. **Create Pull Request**

### Commit Convention
```bash
# Format: type(scope): description
feat(auth): add password reset functionality
fix(billing): resolve invoice calculation bug
docs(readme): update installation instructions
test(sales): add customer service tests
```

### Code Style Guidelines
- **ESLint**: Follow configured rules
- **Prettier**: Auto-format code
- **TypeScript**: Use strict mode
- **Testing**: Maintain >80% coverage
- **Documentation**: Update docs for new features

### Project Structure
```
A-ReALM-V1/
├── apps/                    # Applications
│   ├── gateway/            # API Gateway (Express)
│   └── web/               # React Frontend
├── services/              # Microservices
│   ├── auth-service/      # Authentication
│   ├── sales-service/     # Customer & Sales
│   ├── fleet-service/     # Vehicle Management
│   ├── billing-service/   # Billing & Invoicing
│   └── ops-service/       # Operations
├── packages/              # Shared Libraries
│   ├── config/           # Configuration management
│   ├── db/               # Database & Prisma
│   ├── logger/           # Logging utilities
│   ├── auth-middleware/  # JWT authentication
│   ├── error-handler/    # Error handling
│   ├── rate-limiter/     # Rate limiting
│   ├── input-sanitizer/  # Input validation
│   └── health-checker/   # Health monitoring
├── infra/                # Infrastructure
│   ├── docker-compose.yml
│   └── k8s/             # Kubernetes configs
└── docs/                # Documentation
```

## 📞 Support & Maintenance

### Reporting Issues
1. **Check existing issues** in the repository
2. **Use issue templates** for bug reports and feature requests
3. **Provide detailed information**:
   - Environment details (Node.js version, OS, Docker version)
   - Steps to reproduce the issue
   - Expected vs actual behavior
   - Logs and error messages
   - Screenshots if applicable

### Getting Help
- **Documentation**: Check this README and `/docs` folder
- **API Documentation**: Use Swagger UI at `/docs` endpoints
- **Community**: Join discussions in repository issues
- **Security Issues**: Report privately to maintainers

### Maintenance Schedule
- **Dependencies**: Updated monthly for security patches
- **Security Patches**: Applied immediately when available
- **Feature Releases**: Quarterly major releases
- **LTS Support**: 2 years for major versions

### Monitoring & Alerts
Production deployments should monitor:
- **Health Endpoints**: `/healthz` and `/readyz` for service status
- **Metrics**: Prometheus metrics at `/metrics` endpoints
- **Logs**: Structured JSON logs for centralized analysis
- **Performance**: Response times, error rates, and throughput
- **Security**: Failed authentication attempts, rate limit violations

### Service Endpoints Summary
| Service | Port | Health | Docs | Metrics |
|---------|------|--------|------|---------|
| Gateway | 4000 | `/healthz` | `/docs` | `/metrics` |
| Auth | 4010 | `/healthz` | `/docs` | `/metrics` |
| Sales | 4020 | `/healthz` | `/docs` | `/metrics` |
| Fleet | 4030 | `/healthz` | `/docs` | `/metrics` |
| Billing | 4040 | `/healthz` | `/docs` | `/metrics` |
| Ops | 4050 | `/healthz` | `/docs` | `/metrics` |
| Integrations | 4060 | `/healthz` | `/docs` | `/metrics` |
| Web | 5173 | N/A | N/A | N/A |

### Security Features
- **Authentication**: JWT-based with refresh tokens
- **Authorization**: Role-based access control (RBAC)
- **Rate Limiting**: Configurable per endpoint and user
- **Input Validation**: Comprehensive sanitization and validation
- **CORS Protection**: Configurable allowed origins
- **Security Headers**: Helmet.js for security headers
- **Audit Logging**: Security events and access logs
- **Container Security**: Trivy scanning in CI/CD

### Performance Features
- **Database**: Connection pooling with retry logic
- **Caching**: Redis for session and application caching
- **Monitoring**: Health checks and metrics collection
- **Load Balancing**: API Gateway with service discovery
- **Async Processing**: RabbitMQ for background tasks

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Built with modern TypeScript and Node.js ecosystem**
- **Powered by PostgreSQL, Redis, and RabbitMQ**
- **Secured with enterprise-grade authentication and authorization**
- **Monitored with Prometheus and OpenTelemetry**
- **Tested with Jest and comprehensive test suites**
- **Containerized with Docker for easy deployment**

## 🚀 Quick Start Commands

```bash
# Complete setup from scratch
git clone <repository-url>
cd A-ReALM-V1
npm install -g pnpm@8.15.4
pnpm install
pnpm generate:secrets
cp .env.example .env
# Edit .env with generated secrets
docker-compose -f infra/docker-compose.yml up -d
pnpm --filter @a-realm/db build
pnpm --filter @a-realm/db migrate:dev
pnpm seed
pnpm dev
```

```bash
# Production deployment
pnpm build
docker build -t a-realm/gateway:latest apps/gateway
docker build -t a-realm/auth-service:latest services/auth-service
# ... build other services
docker-compose -f docker-compose.prod.yml up -d
```

```bash
# Development workflow
pnpm dev:up          # Start everything
pnpm test            # Run tests
pnpm lint            # Check code quality
pnpm dev:down        # Stop everything
```

---

## 📊 Project Status

- ✅ **Authentication & Authorization**: Complete with JWT and RBAC
- ✅ **Database Models**: Prisma schema with migrations
- ✅ **API Documentation**: Swagger UI for all services
- ✅ **Security**: Rate limiting, input validation, CORS
- ✅ **Monitoring**: Health checks, metrics, logging
- ✅ **Testing**: Unit and integration tests
- ✅ **CI/CD**: GitHub Actions with security scanning
- ✅ **Docker**: Multi-stage builds and compose files

**🎉 The application is production-ready and enterprise-grade!**

---

**🚀 Ready to get started? Follow the [Installation & Setup](#-installation--setup) guide above!**

For questions or support, please check the [Troubleshooting](#-troubleshooting--faqs) section or create an issue in the repository.
