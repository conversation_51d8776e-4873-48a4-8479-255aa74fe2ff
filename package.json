{"name": "a-realm-monorepo", "version": "0.1.0", "private": true, "packageManager": "pnpm@8.15.4", "engines": {"node": ">=18.17.0"}, "scripts": {"dev": "npx turbo run dev --parallel", "build": "npx turbo run build", "lint": "npx turbo run lint", "test:all": "npx turbo run test", "typecheck": "npx turbo run typecheck", "format": "npx turbo run format", "seed": "pnpm --filter @a-realm/db seed", "generate:secrets": "node scripts/generate-secrets.mjs", "test:cors": "node scripts/test-cors.mjs test", "test:errors": "node scripts/test-error-handling.mjs test", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "dev:up": "node scripts/dev-up.mjs", "dev:down": "node scripts/dev-down.mjs"}, "devDependencies": {"@types/amqplib": "^0.10.7", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/hpp": "^0.2.6", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.3.0", "@types/on-finished": "^2.3.5", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-security": "^3.0.1", "identity-obj-proxy": "^3.0.0", "jest": "^30.1.2", "jest-environment-node": "^30.1.2", "prettier": "^3.3.3", "swagger-ui-express": "^5.0.1", "ts-jest": "^29.4.1", "tsx": "^4.11.0", "turbo": "^1.13.4", "typescript": "^5.4.5"}, "dependencies": {"dotenv": "^16.4.5"}}