// Set test environment FIRST before importing anything
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error';

// Set test JWT secrets (required for auth tests) - must be at least 32 characters
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only-must-be-32-chars-minimum';
process.env.JWT_REFRESH_SECRET = 'test-jwt-refresh-secret-key-for-testing-only-must-be-32-chars-minimum';

// Global test setup - DISABLED database setup for now since psql is not available
// const { TestSetup } = require('./packages/test-utils/dist/src/index.js');

// Set test database URL
process.env.POSTGRES_URL = process.env.POSTGRES_URL || 'postgresql://postgres:postgres@localhost:5432/a_realm_test';

// Global setup and teardown - DISABLED for now
// beforeAll(async () => {
//   await TestSetup.beforeAll();
// }, 60000); // 60 second timeout for database setup

// afterEach(async () => {
//   await TestSetup.afterEach();
// });

// afterAll(async () => {
//   await TestSetup.afterAll();
// }, 30000); // 30 second timeout for cleanup

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Increase timeout for all tests
jest.setTimeout(30000);

// Import testing library extensions for DOM assertions
require('@testing-library/jest-dom');
