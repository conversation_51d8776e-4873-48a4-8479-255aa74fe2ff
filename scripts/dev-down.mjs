#!/usr/bin/env node
import { spawn } from 'node:child_process';

function run(cmd, args, opts = {}) {
  return new Promise((resolve, reject) => {
    const p = spawn(cmd, args, { stdio: 'inherit', shell: false, ...opts });
    p.on('close', (code) => {
      if (code === 0) resolve();
      else reject(new Error(`${cmd} ${args.join(' ')} exited with ${code}`));
    });
  });
}

async function downCompose() {
  try {
    await run('docker', ['compose', '-f', 'infra/docker-compose.yml', 'down', '-v']);
  } catch {
    await run('docker-compose', ['-f', 'infra/docker-compose.yml', 'down', '-v']);
  }
}

downCompose().catch((err) => {
  console.error(err.message || err);
  process.exit(1);
});

