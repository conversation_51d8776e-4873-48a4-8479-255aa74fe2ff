#!/usr/bin/env node
import { spawn } from 'node:child_process';
import { existsSync, cpSync } from 'node:fs';

function run(cmd, args, opts = {}) {
  return new Promise((resolve, reject) => {
    const p = spawn(cmd, args, { stdio: 'inherit', shell: false, ...opts });
    p.on('close', (code) => {
      if (code === 0) resolve();
      else reject(new Error(`${cmd} ${args.join(' ')} exited with ${code}`));
    });
  });
}

async function upCompose() {
  try {
    await run('docker', ['compose', '-f', 'infra/docker-compose.yml', 'up', '-d']);
  } catch {
    // fallback to docker-compose
    await run('docker-compose', ['-f', 'infra/docker-compose.yml', 'up', '-d']);
  }
}

async function main() {
  console.log('Ensuring env files...');
  if (!existsSync('.env') && existsSync('.env.example')) cpSync('.env.example', '.env');
  if (!existsSync('apps/web/.env') && existsSync('apps/web/.env.example')) cpSync('apps/web/.env.example', 'apps/web/.env');

  console.log('Starting local infra (Postgres/Redis/RabbitMQ)...');
  await upCompose();

  console.log('Installing dependencies...');
  await run('pnpm', ['install']);

  console.log('Generating Prisma client...');
  await run('pnpm', ['--filter', '@a-realm/db', 'prisma:generate']);

  console.log('Syncing database schema...');
  await run('pnpm', ['--filter', '@a-realm/db', 'db:push']);

  console.log('Seeding database...');
  await run('pnpm', ['seed']);

  console.log('Starting dev processes...');
  await run('pnpm', ['dev']);
}

main().catch((err) => {
  console.error(err.message || err);
  process.exit(1);
});

