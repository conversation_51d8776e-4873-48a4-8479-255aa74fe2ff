#!/usr/bin/env node

/**
 * CORS Security Test Script
 * 
 * Tests CORS configuration against various origins to ensure security
 */

import { config, createCorsConfig } from '../packages/config/dist/src/index.js';

const corsConfig = createCorsConfig();

/**
 * Test CORS origin validation
 */
function testOrigin(origin, shouldAllow = false) {
  return new Promise((resolve) => {
    corsConfig.origin(origin, (error, allowed) => {
      const result = {
        origin,
        allowed: !error && allowed,
        expected: shouldAllow,
        passed: (!error && allowed) === shouldAllow,
        error: error?.message
      };
      resolve(result);
    });
  });
}

/**
 * Run CORS security tests
 */
async function runCorsTests() {
  console.log('🔒 CORS Security Test Suite');
  console.log('==========================');
  console.log('');
  
  const tests = [
    // Should be allowed
    { origin: 'http://localhost:5173', shouldAllow: true, description: 'Allowed localhost origin' },
    
    // Should be blocked
    { origin: 'http://malicious-site.com', shouldAllow: false, description: 'Malicious external origin' },
    { origin: 'https://evil.com', shouldAllow: false, description: 'HTTPS malicious origin' },
    { origin: 'http://localhost:3000', shouldAllow: false, description: 'Different localhost port' },
    { origin: 'http://127.0.0.1:5173', shouldAllow: false, description: 'IP address instead of localhost' },
    
    // Edge cases
    { origin: null, shouldAllow: config.nodeEnv === 'development', description: 'No origin (mobile/Postman)' },
    { origin: undefined, shouldAllow: config.nodeEnv === 'development', description: 'Undefined origin' },
    { origin: '', shouldAllow: false, description: 'Empty string origin' }
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    const result = await testOrigin(test.origin, test.shouldAllow);
    
    if (result.passed) {
      console.log(`✅ ${test.description}`);
      console.log(`   Origin: ${test.origin === null ? 'null' : test.origin === undefined ? 'undefined' : `"${test.origin}"`} → ${result.allowed ? 'ALLOWED' : 'BLOCKED'}`);
      passed++;
    } else {
      console.log(`❌ ${test.description}`);
      console.log(`   Origin: ${test.origin === null ? 'null' : test.origin === undefined ? 'undefined' : `"${test.origin}"`} → ${result.allowed ? 'ALLOWED' : 'BLOCKED'} (expected ${test.shouldAllow ? 'ALLOWED' : 'BLOCKED'})`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
      failed++;
    }
    console.log('');
  }

  console.log('Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${passed + failed}`);
  
  if (failed > 0) {
    console.log('');
    console.log('⚠️  Some CORS tests failed. Review your configuration.');
    process.exit(1);
  } else {
    console.log('');
    console.log('🎉 All CORS security tests passed!');
  }
}

/**
 * Display current CORS configuration
 */
function displayConfig() {
  console.log('Current CORS Configuration:');
  console.log('==========================');
  console.log(`Environment: ${config.nodeEnv}`);
  console.log(`Allowed Origins: ${config.allowedOrigins.join(', ')}`);
  console.log(`Credentials: ${corsConfig.credentials}`);
  console.log(`Methods: ${corsConfig.methods.join(', ')}`);
  console.log(`Max Age: ${corsConfig.maxAge} seconds`);
  console.log('');
}

// Main execution
const command = process.argv[2];

switch (command) {
  case 'test':
    await runCorsTests();
    break;
  case 'config':
    displayConfig();
    break;
  default:
    console.log('🔒 CORS Security Tester');
    console.log('');
    console.log('Usage:');
    console.log('  node scripts/test-cors.mjs test     # Run security tests');
    console.log('  node scripts/test-cors.mjs config   # Show current config');
    break;
}
