#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

/**
 * Add error handling to all services
 */

const services = [
  'services/sales-service',
  'services/fleet-service', 
  'services/billing-service',
  'services/ops-service',
  'apps/gateway'
];

function updatePackageJson(servicePath) {
  const packagePath = path.join(servicePath, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  // Add error-handler dependency if not present
  if (!packageJson.dependencies['@a-realm/error-handler']) {
    packageJson.dependencies['@a-realm/error-handler'] = 'workspace:*';
    
    // Sort dependencies
    const sortedDeps = {};
    Object.keys(packageJson.dependencies).sort().forEach(key => {
      sortedDeps[key] = packageJson.dependencies[key];
    });
    packageJson.dependencies = sortedDeps;
    
    fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2) + '\n');
    console.log(`✅ Updated ${packagePath}`);
  } else {
    console.log(`⏭️  ${packagePath} already has error-handler dependency`);
  }
}

function updateServiceIndex(servicePath) {
  const indexPath = path.join(servicePath, 'src/index.ts');
  
  if (!fs.existsSync(indexPath)) {
    console.log(`⚠️  ${indexPath} not found, skipping`);
    return;
  }
  
  let content = fs.readFileSync(indexPath, 'utf8');
  
  // Check if already updated
  if (content.includes('@a-realm/error-handler')) {
    console.log(`⏭️  ${indexPath} already has error handling`);
    return;
  }
  
  // Add imports
  const importLine = `import { 
  globalErrorHandler, 
  notFoundHandler, 
  setupProcessHandlers,
  asyncHandler,
  ErrorFactory 
} from '@a-realm/error-handler';`;

  // Find the last import line
  const lines = content.split('\n');
  let lastImportIndex = -1;
  
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].startsWith('import ') || lines[i].startsWith('} from ')) {
      lastImportIndex = i;
    }
  }
  
  if (lastImportIndex >= 0) {
    lines.splice(lastImportIndex + 1, 0, importLine);
  }
  
  // Add setupProcessHandlers after logger creation
  const loggerLineIndex = lines.findIndex(line => line.includes('createLogger(config.logLevel)'));
  if (loggerLineIndex >= 0) {
    lines.splice(loggerLineIndex + 2, 0, '// Setup global error handlers', 'setupProcessHandlers();', '');
  }
  
  // Add error middleware before app.listen
  const listenLineIndex = lines.findIndex(line => line.includes('app.listen('));
  if (listenLineIndex >= 0) {
    lines.splice(listenLineIndex, 0, 
      '// Error handling middleware (must be last)',
      'app.use(notFoundHandler());',
      'app.use(globalErrorHandler({',
      '  includeStack: config.nodeEnv === \'development\',',
      '  includeContext: config.nodeEnv === \'development\'',
      '}));',
      ''
    );
  }
  
  content = lines.join('\n');
  fs.writeFileSync(indexPath, content);
  console.log(`✅ Updated ${indexPath}`);
}

// Main execution
console.log('🔧 Adding error handling to all services...\n');

for (const service of services) {
  console.log(`Processing ${service}:`);
  updatePackageJson(service);
  updateServiceIndex(service);
  console.log('');
}

console.log('✅ Error handling added to all services!');
console.log('');
console.log('Next steps:');
console.log('1. Run: pnpm install');
console.log('2. Run: pnpm build');
console.log('3. Test error handling with: pnpm dev');
