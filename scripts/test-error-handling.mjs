#!/usr/bin/env node

/**
 * Test error handling across all services
 */

import { spawn } from 'child_process';
import { setTimeout } from 'timers/promises';

const services = [
  { name: 'auth-service', port: 4010 },
  { name: 'sales-service', port: 4020 },
  { name: 'fleet-service', port: 4030 },
  { name: 'billing-service', port: 4040 },
  { name: 'ops-service', port: 4050 },
  { name: 'gateway', port: 4000 }
];

/**
 * Test error endpoints
 */
async function testErrorHandling() {
  console.log('🧪 Testing Error Handling\n');

  for (const service of services) {
    console.log(`Testing ${service.name} (port ${service.port}):`);
    
    try {
      // Test 404 error
      const response404 = await fetch(`http://localhost:${service.port}/nonexistent-endpoint`);
      const error404 = await response404.json();
      
      if (response404.status === 404 && error404.error?.code === 'RESOURCE_NOT_FOUND') {
        console.log('  ✅ 404 handling works');
      } else {
        console.log('  ❌ 404 handling failed');
        console.log('    Response:', error404);
      }
      
      // Test validation error (if login endpoint exists)
      if (service.name === 'auth-service') {
        const responseValidation = await fetch(`http://localhost:${service.port}/auth/login`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({}) // Empty body should trigger validation error
        });
        
        const errorValidation = await responseValidation.json();
        
        if (responseValidation.status === 400 && errorValidation.error?.code === 'VALIDATION_ERROR') {
          console.log('  ✅ Validation error handling works');
        } else {
          console.log('  ❌ Validation error handling failed');
          console.log('    Response:', errorValidation);
        }
      }
      
    } catch (error) {
      console.log(`  ❌ Service ${service.name} not responding:`, error.message);
    }
    
    console.log('');
  }
}

/**
 * Check if services are running
 */
async function checkServices() {
  console.log('🔍 Checking if services are running...\n');
  
  const runningServices = [];
  
  for (const service of services) {
    try {
      const response = await fetch(`http://localhost:${service.port}/health`, {
        signal: AbortSignal.timeout(2000)
      });
      
      if (response.ok) {
        console.log(`✅ ${service.name} is running on port ${service.port}`);
        runningServices.push(service);
      } else {
        console.log(`⚠️  ${service.name} responded with status ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ ${service.name} is not running on port ${service.port}`);
    }
  }
  
  console.log('');
  return runningServices;
}

/**
 * Start services for testing
 */
async function startServices() {
  console.log('🚀 Starting services for testing...\n');
  
  const processes = [];
  
  // Start each service
  for (const service of services) {
    const servicePath = service.name === 'gateway' ? 'apps/gateway' : `services/${service.name}`;
    
    console.log(`Starting ${service.name}...`);
    
    const process = spawn('pnpm', ['--filter', `@a-realm/${service.name}`, 'dev'], {
      stdio: 'pipe',
      detached: false
    });
    
    processes.push({ name: service.name, process });
    
    // Give each service time to start
    await setTimeout(2000);
  }
  
  console.log('\n⏳ Waiting for services to fully start...');
  await setTimeout(10000);
  
  return processes;
}

/**
 * Stop all processes
 */
function stopServices(processes) {
  console.log('\n🛑 Stopping services...');
  
  for (const { name, process } of processes) {
    console.log(`Stopping ${name}...`);
    process.kill('SIGTERM');
  }
}

// Main execution
const command = process.argv[2];

switch (command) {
  case 'test':
    const runningServices = await checkServices();
    if (runningServices.length === 0) {
      console.log('❌ No services are running. Start them with: pnpm dev');
      process.exit(1);
    }
    await testErrorHandling();
    break;
    
  case 'start-and-test':
    const processes = await startServices();
    await testErrorHandling();
    stopServices(processes);
    break;
    
  default:
    console.log('🧪 Error Handling Tester');
    console.log('');
    console.log('Usage:');
    console.log('  node scripts/test-error-handling.mjs test           # Test running services');
    console.log('  node scripts/test-error-handling.mjs start-and-test # Start services and test');
    break;
}
