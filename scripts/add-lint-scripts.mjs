#!/usr/bin/env node

import { readFileSync, writeFileSync, readdirSync, statSync } from 'fs';
import { join } from 'path';

const workspaces = ['packages', 'services', 'apps'];

function updatePackageJson(packagePath) {
  const packageJsonPath = join(packagePath, 'package.json');
  
  try {
    const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));
    
    // Add lint and format scripts if they don't exist
    if (!packageJson.scripts) {
      packageJson.scripts = {};
    }
    
    const scriptsToAdd = {
      'lint': 'eslint src --ext .ts,.tsx,.js,.jsx',
      'lint:fix': 'eslint src --ext .ts,.tsx,.js,.jsx --fix',
      'format': 'prettier --write "src/**/*.{ts,tsx,js,jsx,json,md}"',
      'format:check': 'prettier --check "src/**/*.{ts,tsx,js,jsx,json,md}"'
    };
    
    let updated = false;
    for (const [script, command] of Object.entries(scriptsToAdd)) {
      if (!packageJson.scripts[script]) {
        packageJson.scripts[script] = command;
        updated = true;
      }
    }
    
    if (updated) {
      // Sort scripts alphabetically
      const sortedScripts = {};
      Object.keys(packageJson.scripts).sort().forEach(key => {
        sortedScripts[key] = packageJson.scripts[key];
      });
      packageJson.scripts = sortedScripts;
      
      writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
      console.log(`✅ Updated ${packageJsonPath}`);
    } else {
      console.log(`⏭️  ${packageJsonPath} already has lint/format scripts`);
    }
  } catch (error) {
    console.log(`❌ Failed to update ${packageJsonPath}: ${error.message}`);
  }
}

function processWorkspace(workspacePath) {
  try {
    const items = readdirSync(workspacePath);
    
    for (const item of items) {
      const itemPath = join(workspacePath, item);
      const stat = statSync(itemPath);
      
      if (stat.isDirectory()) {
        const packageJsonPath = join(itemPath, 'package.json');
        try {
          statSync(packageJsonPath);
          updatePackageJson(itemPath);
        } catch {
          // No package.json, skip
        }
      }
    }
  } catch (error) {
    console.log(`❌ Failed to process workspace ${workspacePath}: ${error.message}`);
  }
}

console.log('🔧 Adding lint and format scripts to all packages...\n');

for (const workspace of workspaces) {
  console.log(`Processing ${workspace}:`);
  processWorkspace(workspace);
  console.log('');
}

console.log('✅ Lint and format scripts added to all packages!');
console.log('');
console.log('Next steps:');
console.log('1. Run: pnpm run lint to check all packages');
console.log('2. Run: pnpm run format to format all code');
console.log('3. Run: pnpm run lint:fix to auto-fix linting issues');
