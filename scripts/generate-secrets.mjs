#!/usr/bin/env node

import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

/**
 * Generate cryptographically secure secrets for JWT tokens
 */
function generateSecret(length = 64) {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Update .env file with new secrets
 */
function updateEnvFile() {
  const envPath = path.join(process.cwd(), '.env');
  
  if (!fs.existsSync(envPath)) {
    console.error('❌ .env file not found. Please copy .env.example to .env first.');
    process.exit(1);
  }

  const jwtSecret = generateSecret(32);
  const jwtRefreshSecret = generateSecret(32);

  let envContent = fs.readFileSync(envPath, 'utf8');

  // Update JWT secrets
  envContent = envContent.replace(
    /JWT_SECRET=.*/,
    `JWT_SECRET=${jwtSecret}`
  );
  
  envContent = envContent.replace(
    /JWT_REFRESH_SECRET=.*/,
    `JWT_REFRESH_SECRET=${jwtRefreshSecret}`
  );

  fs.writeFileSync(envPath, envContent);

  console.log('✅ JWT secrets updated successfully!');
  console.log('🔐 New JWT_SECRET generated (64 chars)');
  console.log('🔐 New JWT_REFRESH_SECRET generated (64 chars)');
  console.log('');
  console.log('⚠️  IMPORTANT: Keep these secrets secure and never commit them to version control!');
  console.log('💡 For production, use environment variables or a secret management service.');
}

/**
 * Generate secrets for different environments
 */
function generateForEnvironment(env) {
  const jwtSecret = generateSecret(32);
  const jwtRefreshSecret = generateSecret(32);

  console.log(`\n🔑 Secrets for ${env.toUpperCase()} environment:`);
  console.log(`JWT_SECRET=${jwtSecret}`);
  console.log(`JWT_REFRESH_SECRET=${jwtRefreshSecret}`);
}

// Main execution
const command = process.argv[2];

switch (command) {
  case 'update':
    updateEnvFile();
    break;
  case 'generate':
    const env = process.argv[3] || 'production';
    generateForEnvironment(env);
    break;
  default:
    console.log('🔐 JWT Secret Generator');
    console.log('');
    console.log('Usage:');
    console.log('  node scripts/generate-secrets.mjs update     # Update .env file');
    console.log('  node scripts/generate-secrets.mjs generate   # Generate for production');
    console.log('  node scripts/generate-secrets.mjs generate staging  # Generate for staging');
    break;
}
