image:
  repository: your-registry/a-realm-fleet-service
  tag: latest
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 80
  targetPort: 4030

env:
  - name: NODE_ENV
    value: production
  - name: POSTGRES_URL
    valueFrom:
      secretKeyRef:
        name: db-secrets
        key: POSTGRES_URL
  - name: RABBITMQ_URL
    valueFrom:
      secretKeyRef:
        name: mq-secrets
        key: RABBITMQ_URL

resources: {}
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70

monitoring:
  enabled: true
  labels:
    release: prometheus
