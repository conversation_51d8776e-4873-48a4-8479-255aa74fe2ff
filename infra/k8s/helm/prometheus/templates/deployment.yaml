apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "prometheus.fullname" . }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ include "prometheus.name" . }}
  template:
    metadata:
      labels:
        app: {{ include "prometheus.name" . }}
    spec:
      containers:
        - name: prometheus
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          args: ["--config.file=/etc/prometheus/prometheus.yml", "--storage.tsdb.path=/prometheus"]
          ports:
            - containerPort: {{ .Values.service.port }}
          volumeMounts:
            - name: config
              mountPath: /etc/prometheus
      volumes:
        - name: config
          configMap:
            name: {{ include "prometheus.fullname" . }}-config

