image:
  repository: your-registry/a-realm-ops-service
  tag: latest
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 80
  targetPort: 4050

env:
  - name: NODE_ENV
    value: production
  - name: POSTGRES_URL
    valueFrom:
      secretKeyRef:
        name: db-secrets
        key: POSTGRES_URL

resources: {}
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70

monitoring:
  enabled: true
  labels:
    release: prometheus
