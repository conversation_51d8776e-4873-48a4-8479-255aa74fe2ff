apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "grafana.fullname" . }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ include "grafana.name" . }}
  template:
    metadata:
      labels:
        app: {{ include "grafana.name" . }}
    spec:
      containers:
        - name: grafana
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          ports:
            - containerPort: {{ .Values.service.port }}
          volumeMounts:
            - name: datasources
              mountPath: /etc/grafana/provisioning/datasources
      volumes:
        - name: datasources
          configMap:
            name: {{ include "grafana.fullname" . }}-datasource

