apiVersion: v1
kind: Service
metadata:
  name: {{ include "auth.fullname" . }}
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "{{ .Values.service.port }}"
    prometheus.io/path: "/metrics"
spec:
  selector:
    app: {{ include "auth.name" . }}
  ports:
    - name: http
      protocol: TCP
      port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
