image:
  repository: your-registry/a-realm-auth-service
  tag: latest
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 80
  targetPort: 4010

env:
  - name: NODE_ENV
    value: production
  - name: JWT_SECRET
    valueFrom:
      secretKeyRef:
        name: jwt-secrets
        key: JWT_SECRET
  - name: JWT_REFRESH_SECRET
    valueFrom:
      secretKeyRef:
        name: jwt-secrets
        key: JWT_REFRESH_SECRET

resources: {}
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70

monitoring:
  enabled: true
  labels:
    release: prometheus
