apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "otel-collector.fullname" . }}-config
data:
  config.yaml: |
    receivers:
      otlp:
        protocols:
          http:
            endpoint: 0.0.0.0:{{ .Values.service.port }}
    processors:
      batch: {}
    exporters:
{{- if .Values.exporters.logging }}
      logging: {}
{{- end }}
    service:
      pipelines:
        traces:
          receivers: [otlp]
          processors: [batch]
          exporters: [{{- if .Values.exporters.logging }} logging {{- end }}]

