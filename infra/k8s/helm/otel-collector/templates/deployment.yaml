apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "otel-collector.fullname" . }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ include "otel-collector.name" . }}
  template:
    metadata:
      labels:
        app: {{ include "otel-collector.name" . }}
    spec:
      containers:
        - name: otelcol
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          args: ["--config=/etc/otelcol/config.yaml"]
          ports:
            - containerPort: {{ .Values.service.port }}
              name: otlp-http
          volumeMounts:
            - name: config
              mountPath: /etc/otelcol
      volumes:
        - name: config
          configMap:
            name: {{ include "otel-collector.fullname" . }}-config

