image:
  repository: your-registry/a-realm-gateway
  tag: latest
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 80
  targetPort: 4000

env:
  - name: NODE_ENV
    value: production

monitoring:
  enabled: true
  labels:
    release: prometheus

ingress:
  enabled: false
  className: nginx
  hosts:
    - host: gateway.local
      paths:
        - path: /
          pathType: Prefix

resources: {}

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
