apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "gateway.fullname" . }}
spec:
  replicas: 2
  selector:
    matchLabels:
      app: {{ include "gateway.name" . }}
  template:
    metadata:
      labels:
        app: {{ include "gateway.name" . }}
    spec:
      containers:
        - name: gateway
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - containerPort: {{ .Values.service.targetPort }}
          env:
            {{- toYaml .Values.env | nindent 12 }}
          readinessProbe:
            httpGet:
              path: /readyz
              port: {{ .Values.service.targetPort }}
          livenessProbe:
            httpGet:
              path: /healthz
              port: {{ .Values.service.targetPort }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}

