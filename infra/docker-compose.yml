version: '3.9'
services:
  postgres:
    image: postgres:15-alpine
    container_name: a-realm-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: a_realm
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    container_name: a-realm-redis
    ports:
      - "6379:6379"

  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: a-realm-rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"

volumes:
  pgdata:

