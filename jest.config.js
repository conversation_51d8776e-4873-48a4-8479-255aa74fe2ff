/** @type {import('jest').Config} */
module.exports = {
  // Use ts-jest preset for TypeScript support
  preset: 'ts-jest',
  
  // Test environment
  testEnvironment: 'node',
  
  // Root directories for tests
  roots: ['<rootDir>/services', '<rootDir>/packages', '<rootDir>/apps'],
  
  // Test file patterns
  testMatch: [
    '**/__tests__/**/*.ts',
    '**/?(*.)+(spec|test).ts'
  ],
  
  // Transform TypeScript files
  transform: {
    '^.+\\.ts$': ['ts-jest', {
      tsconfig: {
        module: 'commonjs',
        target: 'es2020',
        lib: ['es2020'],
        types: ['jest', 'node'],
        moduleResolution: 'node',
        allowSyntheticDefaultImports: true,
        esModuleInterop: true,
        skipLibCheck: true,
        strict: false
      }
    }]
  },
  
  // Module file extensions
  moduleFileExtensions: ['ts', 'js', 'json'],
  
  // Module name mapping for workspace packages
  moduleNameMapper: {
    '^@a-realm/(.*)$': '<rootDir>/packages/$1/src'
  },
  
  // Setup files
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],

  // Project-specific configurations
  projects: [
    {
      displayName: 'auth-service',
      preset: 'ts-jest',
      testEnvironment: 'node',
      testMatch: ['<rootDir>/services/auth-service/**/*.test.ts'],
      setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
      moduleNameMapper: {
        '^@a-realm/(.*)$': '<rootDir>/packages/$1/src'
      },
      transform: {
        '^.+\\.ts$': ['ts-jest', {
          tsconfig: {
            module: 'commonjs',
            target: 'es2020',
            lib: ['es2020'],
            types: ['jest', 'node'],
            moduleResolution: 'node',
            allowSyntheticDefaultImports: true,
            esModuleInterop: true,
            skipLibCheck: true,
            strict: false
          }
        }]
      },
    },
    {
      displayName: 'sales-service',
      preset: 'ts-jest',
      testEnvironment: 'node',
      testMatch: ['<rootDir>/services/sales-service/**/*.test.ts'],
      setupFilesAfterEnv: [
        '<rootDir>/jest.setup.js',
        '<rootDir>/services/sales-service/__tests__/setup.ts'
      ],
      moduleNameMapper: {
        '^@a-realm/(.*)$': '<rootDir>/packages/$1/src'
      },
      transform: {
        '^.+\\.ts$': ['ts-jest', {
          tsconfig: {
            module: 'commonjs',
            target: 'es2020',
            lib: ['es2020'],
            types: ['jest', 'node'],
            moduleResolution: 'node',
            allowSyntheticDefaultImports: true,
            esModuleInterop: true,
            skipLibCheck: true,
            strict: false
          }
        }]
      },
    },
    {
      displayName: 'packages',
      preset: 'ts-jest',
      testEnvironment: 'node',
      testMatch: ['<rootDir>/packages/**/*.test.ts'],
      setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
      moduleNameMapper: {
        '^@a-realm/(.*)$': '<rootDir>/packages/$1/src'
      },
      transform: {
        '^.+\\.ts$': ['ts-jest', {
          tsconfig: {
            module: 'commonjs',
            target: 'es2020',
            lib: ['es2020'],
            types: ['jest', 'node'],
            moduleResolution: 'node',
            allowSyntheticDefaultImports: true,
            esModuleInterop: true,
            skipLibCheck: true,
            strict: false
          }
        }]
      },
    },
    {
      displayName: 'apps',
      preset: 'ts-jest',
      testEnvironment: 'jsdom',
      testMatch: ['<rootDir>/apps/**/*.test.ts', '<rootDir>/apps/**/*.test.tsx'],
      setupFilesAfterEnv: [
        '<rootDir>/jest.setup.js',
        '<rootDir>/apps/web/src/__tests__/setup.ts'
      ],
      moduleNameMapper: {
        '^@a-realm/(.*)$': '<rootDir>/packages/$1/src',
        '\\.(css|less|scss|sass)$': 'identity-obj-proxy'
      },
      transform: {
        '^.+\\.(ts|tsx)$': ['ts-jest', {
          tsconfig: {
            module: 'esnext',
            target: 'es2020',
            lib: ['es2020', 'dom'],
            types: ['jest', 'node', '@testing-library/jest-dom'],
            moduleResolution: 'node',
            allowSyntheticDefaultImports: true,
            esModuleInterop: true,
            skipLibCheck: true,
            strict: false,
            jsx: 'react-jsx'
          }
        }]
      },
    },
  ],
  
  // Coverage configuration - DISABLED for now to avoid TS compilation issues
  collectCoverage: false,
  
  // Test timeout
  testTimeout: 30000,
  
  // Verbose output
  verbose: true,
  
  // Clear mocks between tests
  clearMocks: true,
  
  // Restore mocks after each test
  restoreMocks: true,
  
  // Global variables for ts-jest - REMOVED (deprecated, moved to transform config)
  
  // Ignore patterns
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/.turbo/'
  ],
  
  // Module paths
  modulePaths: ['<rootDir>'],
  
  // Force exit after tests complete
  forceExit: true,
  
  // Detect open handles
  detectOpenHandles: true
};
