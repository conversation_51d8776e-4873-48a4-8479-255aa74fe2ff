import { z } from 'zod';

export const CustomerCreateSchema = z.object({
  type: z.enum(['CASH', 'CREDIT']).default('CASH'),
  name: z.string().min(1),
  email: z.string().email().optional(),
  phone: z.string().optional()
});

export const ReservationCreateSchema = z.object({
  customerId: z.string().uuid(),
  vehicleId: z.string().uuid().optional(),
  fromDate: z.coerce.date(),
  toDate: z.coerce.date(),
  notes: z.string().optional()
});

export const AgreementStrCreateSchema = z.object({
  customerId: z.string().uuid(),
  vehicleId: z.string().uuid(),
  reservationId: z.string().uuid().optional(),
  startDate: z.coerce.date(),
  endDate: z.coerce.date().optional(),
  total: z.coerce.number().optional(),
  currency: z.string().optional()
});

export type CustomerCreate = z.infer<typeof CustomerCreateSchema>;
export type ReservationCreate = z.infer<typeof ReservationCreateSchema>;
export type AgreementStrCreate = z.infer<typeof AgreementStrCreateSchema>;

// Lease quotation
export const LeaseQuoteCreateSchema = z.object({
  customerId: z.string().uuid(),
  vehicleId: z.string().uuid().optional(),
  startDate: z.coerce.date(),
  endDate: z.coerce.date().optional(),
  months: z.coerce.number().int().positive().optional(),
  monthlyAmount: z.coerce.number().positive(),
  currency: z.string().default('AED')
});

export type LeaseQuoteCreate = z.infer<typeof LeaseQuoteCreateSchema>;
