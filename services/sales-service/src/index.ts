import { startTracing } from '@a-realm/otel';
import { createLogger } from '@a-realm/logger';
import { config } from '@a-realm/config';
import { createApp } from './app';

startTracing('sales-service');
const logger = createLogger(config.logLevel);

const app = createApp();
const port = Number(process.env.SALES_SERVICE_PORT || config.port || 4020);
app.listen(port, () => logger.info({ msg: 'sales-service:started', port }));
