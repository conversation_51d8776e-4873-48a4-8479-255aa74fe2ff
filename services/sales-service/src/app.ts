import express, { Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import client from 'prom-client';
import { getPrisma, checkDatabaseHealth } from '@a-realm/db';
import { publishEvent } from '@a-realm/messaging';
import { config, createCorsConfig, validateCorsConfig } from '@a-realm/config';
import { createLogger, requestIdMiddleware, requestLogger } from '@a-realm/logger';
import { CustomerCreateSchema, ReservationCreateSchema, AgreementStrCreateSchema, LeaseQuoteCreateSchema } from './schemas';
import { requireAuth, requireAnyRole } from '@a-realm/auth-middleware';
import { mountSwagger } from './swagger';
import { 
  globalErrorHandler, 
  notFoundHandler, 
  setupProcessHandlers,
  asyncHandler,
  ErrorFactory 
} from '@a-realm/error-handler';
import {
  ReservationManager,
  AgreementManager,
  BillingManager,
  BusinessContext
} from '@a-realm/business-logic';

/**
 * Create Express application with all middleware and routes
 */
export function createApp(): express.Express {
  const logger = createLogger(config.logLevel);

  // Setup global error handlers
  setupProcessHandlers();

  // Validate CORS configuration on startup
  validateCorsConfig();

  const app = express();

  app.disable('x-powered-by');
  app.set('trust proxy', 1);
  app.use(helmet());
  app.use(cors(createCorsConfig()));
  app.use(express.json({
    limit: '1mb',
    strict: true,
    type: 'application/json'
  }));
  app.use(express.urlencoded({
    limit: '1mb',
    extended: false,
    parameterLimit: 100
  }));
  app.use(requestIdMiddleware);
  app.use(requestLogger(logger));
  // (Optional) Input sanitizer/HPP can be added here when available

  // Health endpoints
  app.get('/healthz', (_req, res) => res.status(200).json({ status: 'ok' }));
  app.get('/readyz', async (_req, res) => {
    try {
      const db = await checkDatabaseHealth();
      if (!db.healthy) return res.status(503).json({ status: 'not_ready', db });
      return res.status(200).json({ status: 'ready' });
    } catch {
      return res.status(503).json({ status: 'not_ready' });
    }
  });

  // Metrics
  const register = new client.Registry();
  client.collectDefaultMetrics({ register });
  app.get('/metrics', async (_req, res) => {
    res.set('Content-Type', register.contentType);
    res.end(await register.metrics());
  });

  mountSwagger(app);

  // Require auth for APIs after docs/metrics
  app.use(requireAuth());

  // Helpers
  function orgId(req: express.Request) {
    const userOrg = (req as any).user?.orgId;
    const hdr = req.headers['x-org-id'];
    const headerOrg = Array.isArray(hdr) ? hdr[0] : hdr;
    if (!userOrg) return undefined as any;
    if (headerOrg && headerOrg !== userOrg) return 'MISMATCH' as any;
    return userOrg;
  }

  function validate<T>(schema: { parse: (x: any) => T }) {
    return (req: express.Request, res: express.Response, next: express.NextFunction) => {
      try {
        req.body = schema.parse(req.body);
        next();
      } catch (e: any) {
        return res.status(400).json({ message: 'validation error', details: e.errors || e.message });
      }
    };
  }

  // Domain endpoints
  app.post('/customers', requireAnyRole(['user', 'admin']), validate(CustomerCreateSchema), async (req, res) => {
    try {
      const oid = orgId(req);
      if (!oid) return res.status(400).json({ message: 'orgId required' });
      if (oid === 'MISMATCH') return res.status(403).json({ message: 'org mismatch' });
      const { type, name, email, phone } = req.body as any;
      if (!name) return res.status(400).json({ message: 'name required' });
      const prisma: any = getPrisma();
      const customer = await prisma.customer.create({
        data: { orgId: oid, type, name, email, phone }
      });
      await publishEvent('customer.created', {
        type: 'CustomerCreated',
        timestamp: new Date().toISOString(),
        data: { customerId: customer.id, orgId: oid }
      });
      res.status(201).json(customer);
    } catch (e: any) {
      logger.error({ msg: 'customer creation failed', error: e.message });
      res.status(500).json({ message: 'internal error' });
    }
  });

  app.get('/customers', requireAnyRole(['user', 'admin']), async (req, res) => {
    try {
      const oid = orgId(req);
      if (!oid) return res.status(400).json({ message: 'orgId required' });
      if (oid === 'MISMATCH') return res.status(403).json({ message: 'org mismatch' });
      const prisma: any = getPrisma();
      const customers = await prisma.customer.findMany({ where: { orgId: oid } });
      res.json(customers);
    } catch (e: any) {
      logger.error({ msg: 'customer fetch failed', error: e.message });
      res.status(500).json({ message: 'internal error' });
    }
  });

  // Initialize business logic managers
  const reservationManager = new ReservationManager();
  const agreementManager = new AgreementManager();
  const billingManager = new BillingManager();

  app.post('/reservations', requireAnyRole(['user', 'admin']), validate(ReservationCreateSchema), asyncHandler(async (req: Request, res: Response) => {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'orgId required' });
    if (oid === 'MISMATCH') return res.status(403).json({ message: 'org mismatch' });

    const { customerId, vehicleId, fromDate, toDate, notes, requireVehicleType } = req.body as any;
    if (!customerId || !fromDate || !toDate) {
      return res.status(400).json({ message: 'customerId, fromDate, toDate required' });
    }

    const context: BusinessContext = {
      orgId: oid,
      userId: (req as any).user?.sub || 'unknown',
      userRole: (req as any).user?.role || 'user',
      timestamp: new Date()
    };

    const reservation = await reservationManager.createReservation(context, {
      customerId,
      vehicleId,
      fromDate: new Date(fromDate),
      toDate: new Date(toDate),
      notes,
      requireVehicleType
    });

    res.status(201).json(reservation);
  }));

  app.post('/agreements/from-reservation', requireAnyRole(['user', 'admin']), asyncHandler(async (req: Request, res: Response) => {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'orgId required' });
    if (oid === 'MISMATCH') return res.status(403).json({ message: 'org mismatch' });

    const { reservationId, agreementType, securityDeposit, insuranceRequired, customTerms } = req.body as any;
    if (!reservationId) return res.status(400).json({ message: 'reservationId required' });

    const context: BusinessContext = {
      orgId: oid,
      userId: (req as any).user?.sub || 'unknown',
      userRole: (req as any).user?.role || 'user',
      timestamp: new Date()
    };

    const result = await agreementManager.createAgreementFromReservation(reservationId, context, {
      agreementType,
      securityDeposit,
      insuranceRequired,
      customTerms
    });

    res.status(201).json(result);
  }));

  // Advanced business logic endpoints

  // Confirm reservation
  app.post('/reservations/:id/confirm', requireAnyRole(['user', 'admin']), asyncHandler(async (req: Request, res: Response) => {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'orgId required' });
    if (oid === 'MISMATCH') return res.status(403).json({ message: 'org mismatch' });

    const context: BusinessContext = {
      orgId: oid,
      userId: (req as any).user?.sub || 'unknown',
      userRole: (req as any).user?.role || 'user',
      timestamp: new Date()
    };

    const reservation = await reservationManager.confirmReservation(req.params.id, context);
    res.json(reservation);
  }));

  // Cancel reservation
  app.post('/reservations/:id/cancel', requireAnyRole(['user', 'admin']), asyncHandler(async (req: Request, res: Response) => {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'x-org-id required' });

    const { reason } = req.body || {};
    const context: BusinessContext = {
      orgId: oid,
      userId: (req as any).user?.sub || 'unknown',
      userRole: (req as any).user?.role || 'user',
      timestamp: new Date()
    };

    const result = await reservationManager.cancelReservation(req.params.id, context, reason);
    res.json(result);
  }));

  // Vehicle handover
  app.post('/agreements/:id/handover', requireAnyRole(['user', 'admin']), asyncHandler(async (req: Request, res: Response) => {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'x-org-id required' });

    const {
      vehicleId,
      customerId,
      handoverDate,
      odometerReading,
      fuelLevel,
      vehicleCondition,
      documentsProvided,
      handoverNotes
    } = req.body;

    const context: BusinessContext = {
      orgId: oid,
      userId: (req as any).user?.sub || 'unknown',
      userRole: (req as any).user?.role || 'user',
      timestamp: new Date()
    };

    const result = await agreementManager.processVehicleHandover(req.params.id, {
      vehicleId,
      customerId,
      handoverDate: new Date(handoverDate),
      odometerReading,
      fuelLevel,
      vehicleCondition,
      documentsProvided,
      handoverNotes,
      completedBy: context.userId
    }, context);

    res.json(result);
  }));

  // Vehicle return
  app.post('/agreements/:id/return', requireAnyRole(['user', 'admin']), asyncHandler(async (req: Request, res: Response) => {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'x-org-id required' });

    const {
      vehicleId,
      returnDate,
      odometerReading,
      fuelLevel,
      vehicleCondition,
      damageReport,
      returnNotes
    } = req.body;

    const context: BusinessContext = {
      orgId: oid,
      userId: (req as any).user?.sub || 'unknown',
      userRole: (req as any).user?.role || 'user',
      timestamp: new Date()
    };

    const result = await agreementManager.processVehicleReturn(req.params.id, {
      vehicleId,
      returnDate: new Date(returnDate),
      odometerReading,
      fuelLevel,
      vehicleCondition,
      damageReport: damageReport || [],
      additionalCharges: [],
      returnNotes,
      processedBy: context.userId
    }, context);

    res.json(result);
  }));

  // Extend agreement
  app.post('/agreements/:id/extend', requireAnyRole(['user', 'admin']), asyncHandler(async (req: Request, res: Response) => {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'x-org-id required' });

    const { newEndDate } = req.body;
    if (!newEndDate) return res.status(400).json({ message: 'newEndDate required' });

    const context: BusinessContext = {
      orgId: oid,
      userId: (req as any).user?.sub || 'unknown',
      userRole: (req as any).user?.role || 'user',
      timestamp: new Date()
    };

    const result = await agreementManager.extendAgreement(req.params.id, new Date(newEndDate), context);
    res.json(result);
  }));

  // Check vehicle availability
  app.get('/vehicles/:id/availability', requireAnyRole(['user', 'admin']), asyncHandler(async (req: Request, res: Response) => {
    const { startDate, endDate } = req.query;
    if (!startDate || !endDate) {
      return res.status(400).json({ message: 'startDate and endDate query parameters required' });
    }

    const availability = await reservationManager.checkVehicleAvailability(
      req.params.id,
      new Date(startDate as string),
      new Date(endDate as string)
    );

    res.json(availability);
  }));

  // Find available vehicles
  app.get('/vehicles/available', requireAnyRole(['user', 'admin']), asyncHandler(async (req: Request, res: Response) => {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'x-org-id required' });

    const { startDate, endDate, businessType, location, minSeats, maxPrice } = req.query;
    if (!startDate || !endDate) {
      return res.status(400).json({ message: 'startDate and endDate query parameters required' });
    }

    const availableVehicles = await reservationManager.findAvailableVehicles(
      oid,
      new Date(startDate as string),
      new Date(endDate as string),
      {
        businessType: businessType as string,
        location: location as string,
        minSeats: minSeats ? parseInt(minSeats as string) : undefined,
        maxPrice: maxPrice ? parseFloat(maxPrice as string) : undefined
      }
    );

    res.json(availableVehicles);
  }));

  // Generate invoice
  app.post('/agreements/:id/invoice', requireAnyRole(['user', 'admin']), asyncHandler(async (req: Request, res: Response) => {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'x-org-id required' });

    const { dueDate, includeDeposit, paymentTerms, notes } = req.body || {};

    const context: BusinessContext = {
      orgId: oid,
      userId: (req as any).user?.sub || 'unknown',
      userRole: (req as any).user?.role || 'user',
      timestamp: new Date()
    };

    const result = await billingManager.generateAgreementInvoice(req.params.id, context, {
      dueDate: dueDate ? new Date(dueDate) : undefined,
      includeDeposit,
      paymentTerms,
      notes
    });

    res.json(result);
  }));

  // Search reservations
  app.get('/reservations', requireAnyRole(['user', 'admin']), asyncHandler(async (req: Request, res: Response) => {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'x-org-id required' });

    const {
      query,
      customerId,
      vehicleId,
      status,
      fromDate,
      toDate,
      sortBy,
      sortOrder,
      page,
      limit
    } = req.query;

    const searchResult = await reservationManager.searchReservations(oid, {
      query: query as string,
      filters: {
        customerId: customerId as string,
        vehicleId: vehicleId as string,
        status: status as string,
        fromDate: fromDate as string,
        toDate: toDate as string
      },
      sortBy: sortBy as string,
      sortOrder: sortOrder as 'ASC' | 'DESC',
      page: page ? parseInt(page as string) : undefined,
      limit: limit ? parseInt(limit as string) : undefined
    });

    res.json(searchResult);
  }));

  // Lease quotations
  app.post('/lease/quotes', requireAnyRole(['user','admin']), validate(LeaseQuoteCreateSchema), asyncHandler(async (req: Request, res: Response) => {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'orgId required' });
    if (oid === 'MISMATCH') return res.status(403).json({ message: 'org mismatch' });
    const prisma: any = getPrisma();
    const { customerId, vehicleId, startDate, endDate, months, monthlyAmount, currency } = req.body as any;
    const q = await prisma.leaseQuotation.create({ data: { orgId: oid, customerId, vehicleId, startDate: new Date(startDate), endDate: endDate ? new Date(endDate) : null, months, monthlyAmount, currency, status: 'DRAFT' } });
    res.status(201).json(q);
  }));

  app.get('/lease/quotes', requireAnyRole(['user','admin']), asyncHandler(async (req: Request, res: Response) => {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'orgId required' });
    if (oid === 'MISMATCH') return res.status(403).json({ message: 'org mismatch' });
    const prisma: any = getPrisma();
    const list = await prisma.leaseQuotation.findMany({ where: { orgId: oid }, orderBy: { createdAt: 'desc' }, take: 100 });
    res.json(list);
  }));

  app.post('/lease/quotes/:id/approve', requireAnyRole(['admin']), asyncHandler(async (req: Request, res: Response) => {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'orgId required' });
    if (oid === 'MISMATCH') return res.status(403).json({ message: 'org mismatch' });
    const prisma: any = getPrisma();
    const q = await prisma.leaseQuotation.update({ where: { id: req.params.id }, data: { status: 'APPROVED' } });
    res.json(q);
  }));

  app.post('/lease/quotes/:id/convert', requireAnyRole(['admin']), asyncHandler(async (req: Request, res: Response) => {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'orgId required' });
    if (oid === 'MISMATCH') return res.status(403).json({ message: 'org mismatch' });
    const prisma: any = getPrisma();
    const q = await prisma.leaseQuotation.findUnique({ where: { id: req.params.id } });
    if (!q) return res.status(404).json({ message: 'quote not found' });
    if (q.orgId !== oid) return res.status(403).json({ message: 'forbidden' });
    if (q.status !== 'APPROVED') return res.status(400).json({ message: 'quote must be APPROVED' });
    // compute endDate if months provided
    const start = new Date(q.startDate);
    let end = q.endDate ? new Date(q.endDate as any) : undefined;
    if (!end && q.months && q.months > 0) {
      const d = new Date(start.getTime());
      d.setMonth(d.getMonth() + q.months);
      end = d;
    }
    if (!q.vehicleId) return res.status(400).json({ message: 'quote missing vehicleId' });
    // create agreement LTR
    const agreement = await prisma.agreement.create({ data: {
      orgId: oid,
      type: 'LTR',
      customerId: q.customerId,
      vehicleId: q.vehicleId!,
      reservationId: null,
      status: 'CREATED',
      startDate: start,
      endDate: end,
      total: null,
      currency: q.currency
    }});
    // create schedules by months if provided
    const schedules: any[] = [];
    if (q.months && q.months > 0) {
      for (let i = 0; i < q.months; i++) {
        const due = new Date(start.getTime());
        due.setMonth(due.getMonth() + i + 1);
        const sch = await prisma.agreementSchedule.create({ data: { agreementId: agreement.id, dueDate: due, amount: q.monthlyAmount, currency: q.currency } });
        schedules.push(sch);
      }
    }
    // mark converted
    await prisma.leaseQuotation.update({ where: { id: q.id }, data: { status: 'CONVERTED' } });
    res.status(201).json({ agreement, schedules });
  }));

  // Error handling middleware (must be last)
  app.use(notFoundHandler());
  app.use(globalErrorHandler({
    includeStack: config.nodeEnv === 'development',
    includeContext: config.nodeEnv === 'development'
  }));

  return app;
}
