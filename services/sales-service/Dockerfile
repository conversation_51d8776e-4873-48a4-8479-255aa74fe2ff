FROM node:18-alpine AS base
WORKDIR /app
RUN corepack enable && corepack prepare pnpm@8.15.4 --activate

COPY package.json pnpm-workspace.yaml turbo.json ./
COPY packages ./packages
COPY services/sales-service ./services/sales-service

RUN pnpm install --filter @a-realm/sales-service... --prod --no-optional
RUN pnpm --filter @a-realm/sales-service build

FROM node:18-alpine
WORKDIR /app
ENV NODE_ENV=production
COPY --from=base /app/services/sales-service/dist ./dist
COPY --from=base /app/services/sales-service/package.json ./package.json
COPY --from=base /app/node_modules ./node_modules
EXPOSE 4020
CMD ["node", "dist/index.js"]

