/**
 * Test setup file for sales service
 * Mocks database and external dependencies
 */

// Mock the database module before any imports
jest.mock('@a-realm/db', () => ({
  getPrisma: jest.fn(() => ({
    $connect: jest.fn().mockResolvedValue(undefined),
    $disconnect: jest.fn().mockResolvedValue(undefined),
    $queryRaw: jest.fn().mockResolvedValue([{ result: 1 }]),
    customer: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    vehicle: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    agreement: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    reservation: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    invoice: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    receipt: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
  })),
  checkDatabaseHealth: jest.fn().mockResolvedValue({ healthy: true }),
  initializeDatabase: jest.fn().mockResolvedValue(undefined),
  disconnectDatabase: jest.fn().mockResolvedValue(undefined),
  isDatabaseConnected: jest.fn().mockReturnValue(true),
  withRetry: jest.fn().mockImplementation((operation) => operation()),
}));

// Mock messaging module
jest.mock('@a-realm/messaging', () => ({
  publishEvent: jest.fn().mockResolvedValue(undefined),
  consumeEvents: jest.fn().mockResolvedValue(false),
}));

// Mock logger to reduce test noise
jest.mock('@a-realm/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  })),
  requestIdMiddleware: jest.fn((req, res, next) => {
    req.id = 'test-request-id';
    next();
  }),
  requestLogger: jest.fn(() => (req: any, res: any, next: any) => next()),
}));

// Mock business logic modules
jest.mock('@a-realm/business-logic', () => ({
  ReservationManager: jest.fn().mockImplementation(() => ({
    createReservation: jest.fn(),
    getReservations: jest.fn(),
    updateReservation: jest.fn(),
    cancelReservation: jest.fn(),
  })),
  AgreementManager: jest.fn().mockImplementation(() => ({
    createAgreement: jest.fn(),
    getAgreements: jest.fn(),
    updateAgreement: jest.fn(),
    closeAgreement: jest.fn(),
  })),
  BillingManager: jest.fn().mockImplementation(() => ({
    createInvoice: jest.fn(),
    processPayment: jest.fn(),
    generateReceipt: jest.fn(),
  })),
  BusinessContext: jest.fn().mockImplementation(() => ({
    orgId: 'test-org-id',
    userId: 'test-user-id',
    userRoles: ['admin'],
  })),
}));

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error';
process.env.POSTGRES_URL = 'postgresql://test:test@localhost:5432/test';
process.env.JWT_SECRET = 'test-secret';
process.env.CORS_ORIGINS = 'http://localhost:5173';

// Global test setup
beforeAll(async () => {
  // Any global setup can go here
});

afterAll(async () => {
  // Any global cleanup can go here
});

// Reset mocks between tests
beforeEach(() => {
  jest.clearAllMocks();
});
