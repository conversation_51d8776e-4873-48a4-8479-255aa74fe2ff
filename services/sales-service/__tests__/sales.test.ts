import express from 'express';
import request from 'supertest';
import { createApp } from '../src/app';

describe('Sales Service', () => {
  let app: express.Express;

  beforeAll(async () => {
    app = createApp();
  });

  describe('Health Checks', () => {
    it('should return health status', async () => {
      const response = await request(app).get('/healthz');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'ok');
    });

    it('should return readiness status', async () => {
      const response = await request(app).get('/readyz');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'ready');
    });

    it('should return metrics', async () => {
      const response = await request(app).get('/metrics');
      
      expect(response.status).toBe(200);
      expect(response.text).toContain('# HELP');
    });
  });

  describe('Error Handling', () => {
    it('should return 401 for protected endpoints without auth', async () => {
      const response = await request(app).get('/customers');

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('message', 'missing token');
    });
  });

  describe('CORS', () => {
    it('should handle CORS preflight requests', async () => {
      const response = await request(app)
        .options('/healthz')
        .set('Origin', 'http://localhost:5173')
        .set('Access-Control-Request-Method', 'GET');
      
      expect(response.status).toBe(204);
      expect(response.headers['access-control-allow-origin']).toBe('http://localhost:5173');
      expect(response.headers['access-control-allow-credentials']).toBe('true');
    });
  });
});
