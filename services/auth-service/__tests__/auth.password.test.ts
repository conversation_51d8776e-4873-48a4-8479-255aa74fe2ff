import express from 'express';
import request from 'supertest';
import bcrypt from 'bcryptjs';

// Mock DB to simulate real user lookup + roles
jest.mock('@a-realm/db', () => ({
  getPrisma: jest.fn(() => ({
    user: {
      findUnique: jest.fn(({ where }: any) => {
        if (where.email === '<EMAIL>') {
          return Promise.resolve({ id: 'u-1', orgId: '00000000-0000-0000-0000-000000000001', email: '<EMAIL>', passwordHash: bcrypt.hashSync('admin123', 10) });
        }
        return Promise.resolve(null);
      })
    },
    userRole: {
      findMany: jest.fn(({ where }: any) => {
        if (where.userId === 'u-1') {
          return Promise.resolve([{ role: { name: 'admin' } }]);
        }
        return Promise.resolve([]);
      })
    }
  }))
}));

import { createApp } from '../src/app';

describe('Auth Service - password login', () => {
  let app: express.Express;

  beforeAll(() => {
    process.env.NODE_ENV = 'test';
    app = createApp();
  });

  it('logs in with email + password via DB', async () => {
    const response = await request(app)
      .post('/auth/login')
      .send({ email: '<EMAIL>', password: 'admin123' });

    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty('accessToken');
    expect(response.body).toHaveProperty('refreshToken');
  });

  it('fails with invalid credentials', async () => {
    const response = await request(app)
      .post('/auth/login')
      .send({ email: '<EMAIL>', password: 'wrong' });

    expect(response.status).toBe(401);
    expect(response.body.error?.code).toBe('UNAUTHORIZED');
  });
});

