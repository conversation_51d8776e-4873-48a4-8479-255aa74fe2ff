{"name": "@a-realm/auth-service", "version": "0.1.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p tsconfig.json", "dev": "tsx watch src/index.ts", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "start": "node dist/index.js", "typecheck": "tsc -p tsconfig.json"}, "dependencies": {"@a-realm/config": "workspace:*", "@a-realm/db": "workspace:*", "@a-realm/error-handler": "workspace:*", "@a-realm/input-sanitizer": "workspace:*", "@a-realm/logger": "workspace:*", "@a-realm/otel": "workspace:*", "@a-realm/rate-limiter": "workspace:*", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "express": "^4.19.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "prom-client": "^15.1.3", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@a-realm/test-utils": "workspace:*", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.10", "@types/supertest": "^6.0.2", "supertest": "^7.0.0", "tsx": "^4.11.0", "typescript": "^5.4.5"}}