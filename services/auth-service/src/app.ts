import express, { Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import jwt from 'jsonwebtoken';
import client from 'prom-client';

import { config, createCorsConfig, validateCorsConfig } from '@a-realm/config';
import { createLogger, requestIdMiddleware, requestLogger } from '@a-realm/logger';
import {
  globalErrorHandler,
  notFoundHandler,
  setupProcessHandlers,
  asyncHandler,
  ErrorFactory
} from '@a-realm/error-handler';
import {
  authRateLimiter,
  apiRateLimiter,
  initializeRateLimiter
} from '@a-realm/rate-limiter';
import {
  strictInputSanitizer,
  hppProtection
} from '@a-realm/input-sanitizer';
import { mountSwagger } from './swagger';
import { getPrisma } from '@a-realm/db';
import bcrypt from 'bcryptjs';

/**
 * Create Express application with all middleware and routes
 */
export function createApp(): express.Express {
  const logger = createLogger(config.logLevel);

  // Setup global error handlers
  setupProcessHandlers();

  // Validate CORS configuration on startup
  validateCorsConfig();

  // Initialize rate limiter
  initializeRateLimiter().catch(err => {
    logger.warn('Failed to initialize rate limiter', { error: err.message });
  });

  const app = express();

  app.disable('x-powered-by');
  app.set('trust proxy', 1);
  app.use(helmet());
  app.use(cors(createCorsConfig()));
  app.use(express.json({ limit: '1mb' })); // Add request size limit
  app.use(requestIdMiddleware);
  app.use(requestLogger(logger));

  // Apply security middleware
  app.use(hppProtection); // HTTP Parameter Pollution protection
  app.use(strictInputSanitizer as any); // Input sanitization
  app.use(apiRateLimiter); // Rate limiting

  // Health endpoints
  app.get('/healthz', (_req, res) => res.status(200).json({ status: 'ok' }));
  app.get('/readyz', (_req, res) => res.status(200).json({ status: 'ready' }));

  // Metrics
  const register = new client.Registry();
  client.collectDefaultMetrics({ register });
  app.get('/metrics', async (_req, res) => {
    res.set('Content-Type', register.contentType);
    res.end(await register.metrics());
  });

  // API Documentation
  mountSwagger(app);

  // Auth endpoints with strict rate limiting
  app.post('/auth/login', authRateLimiter, asyncHandler(async (req: Request, res: Response) => {
    const { username, email, password } = req.body || {};

    const identifier: string | undefined = (email || username)?.toString();
    if (!identifier) {
      throw ErrorFactory.validation('username or email required');
    }

    // In production, enforce credential verification against DB
    const isProd = config.nodeEnv === 'production';

    if (isProd || password) {
      const prisma: any = getPrisma();
      // Find user by email (identifier)
      const user = await prisma.user.findUnique({ where: { email: identifier } });
      if (!user) {
        throw ErrorFactory.unauthorized('Invalid credentials');
      }
      const ok = password ? await bcrypt.compare(password, user.passwordHash) : false;
      if (!ok) {
        throw ErrorFactory.unauthorized('Invalid credentials');
      }

      // Load roles
      const userRoles = await prisma.userRole.findMany({
        where: { userId: user.id },
        include: { role: true }
      });
      const roles: string[] = (userRoles || []).map((r: any) => r.role?.name).filter(Boolean);
      const orgId = user.orgId;

      const token = jwt.sign({ sub: user.id, roles, orgId }, config.jwtSecret, { expiresIn: config.tokenTtlSeconds });
      const refresh = jwt.sign({ sub: user.id, orgId }, config.jwtRefreshSecret, { expiresIn: config.refreshTtlSeconds });
      return res.json({ accessToken: token, refreshToken: refresh, user: { id: user.id, email: user.email, roles } });
    }

    // Development/Test fallback: username-only demo login
    const roles = identifier.toLowerCase().includes('admin') ? ['admin'] : ['user'];
    const orgId = config.defaultOrgId;
    const token = jwt.sign({ sub: identifier, roles, orgId }, config.jwtSecret, { expiresIn: config.tokenTtlSeconds });
    const refresh = jwt.sign({ sub: identifier, orgId }, config.jwtRefreshSecret, { expiresIn: config.refreshTtlSeconds });
    return res.json({ accessToken: token, refreshToken: refresh });
  }));

  app.post('/auth/refresh', authRateLimiter, asyncHandler(async (req: Request, res: Response) => {
    const { refreshToken } = req.body || {};
    if (!refreshToken) {
      throw ErrorFactory.validation('refreshToken required');
    }

    try {
      const payload = jwt.verify(refreshToken, config.jwtRefreshSecret) as any;
      let roles: string[] = [];
      let orgId = payload.orgId || config.defaultOrgId;

      // Attempt to load roles if sub looks like a UUID (user id)
      const maybeId = String(payload.sub || '');
      if (maybeId && maybeId.includes('-')) {
        try {
          const prisma: any = getPrisma();
          const user = await prisma.user.findUnique({ where: { id: maybeId } });
          if (user) {
            orgId = user.orgId;
            const userRoles = await prisma.userRole.findMany({ where: { userId: user.id }, include: { role: true } });
            roles = (userRoles || []).map((r: any) => r.role?.name).filter(Boolean);
          }
        } catch {
          // ignore DB errors on refresh; fallback to empty roles
        }
      }

      const token = jwt.sign({ sub: payload.sub, roles, orgId }, config.jwtSecret, { expiresIn: config.tokenTtlSeconds });
      return res.json({ accessToken: token });
    } catch (e) {
      throw ErrorFactory.unauthorized('Invalid refresh token');
    }
  }));

  // Error handling middleware (must be last)
  app.use(notFoundHandler());
  app.use(globalErrorHandler({
    includeStack: config.nodeEnv === 'development',
    includeContext: config.nodeEnv === 'development'
  }));

  return app;
}
