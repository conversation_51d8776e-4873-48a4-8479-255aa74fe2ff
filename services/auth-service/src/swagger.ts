import { Express } from 'express';
import swaggerUi from 'swagger-ui-express';
import { config } from '@a-realm/config';

/**
 * OpenAPI 3.0 specification for Auth Service
 */
const swaggerSpec = {
  openapi: '3.0.0',
  info: {
    title: 'A-ReALM Auth Service API',
    version: '1.0.0',
    description: 'Authentication and authorization service for A-ReALM platform',
    contact: {
      name: 'A-ReALM Team',
      email: '<EMAIL>'
    },
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT'
    }
  },
  servers: [
    {
      url: `http://localhost:${config.port || 4010}`,
      description: 'Development server'
    },
    {
      url: 'https://api.a-realm.com/auth',
      description: 'Production server'
    }
  ],
  paths: {
    '/healthz': {
      get: {
        summary: 'Health check endpoint',
        description: 'Returns the health status of the service',
        tags: ['Health'],
        responses: {
          '200': {
            description: 'Service is healthy',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: { type: 'string', example: 'ok' }
                  }
                }
              }
            }
          }
        }
      }
    },
    '/readyz': {
      get: {
        summary: 'Readiness check endpoint',
        description: 'Returns the readiness status of the service',
        tags: ['Health'],
        responses: {
          '200': {
            description: 'Service is ready',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: { type: 'string', example: 'ready' }
                  }
                }
              }
            }
          }
        }
      }
    },
    '/metrics': {
      get: {
        summary: 'Prometheus metrics endpoint',
        description: 'Returns Prometheus metrics for monitoring',
        tags: ['Monitoring'],
        responses: {
          '200': {
            description: 'Prometheus metrics',
            content: {
              'text/plain': {
                schema: {
                  type: 'string'
                }
              }
            }
          }
        }
      }
    },
    '/auth/login': {
      post: {
        summary: 'User login',
        description: 'Authenticate user and return JWT tokens',
        tags: ['Authentication'],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                required: ['username', 'password'],
                properties: {
                  username: {
                    type: 'string',
                    example: '<EMAIL>',
                    description: 'User email or username'
                  },
                  password: {
                    type: 'string',
                    example: 'securePassword123',
                    description: 'User password'
                  }
                }
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Login successful',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    accessToken: {
                      type: 'string',
                      description: 'JWT access token'
                    },
                    refreshToken: {
                      type: 'string',
                      description: 'JWT refresh token'
                    },
                    user: {
                      type: 'object',
                      properties: {
                        id: { type: 'string' },
                        username: { type: 'string' },
                        role: { type: 'string', enum: ['admin', 'user'] }
                      }
                    }
                  }
                }
              }
            }
          },
          '400': {
            description: 'Invalid request',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error'
                }
              }
            }
          },
          '401': {
            description: 'Invalid credentials',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error'
                }
              }
            }
          },
          '429': {
            description: 'Rate limit exceeded',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/RateLimitError'
                }
              }
            }
          }
        }
      }
    },
    '/auth/refresh': {
      post: {
        summary: 'Refresh access token',
        description: 'Get new access token using refresh token',
        tags: ['Authentication'],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                required: ['refreshToken'],
                properties: {
                  refreshToken: {
                    type: 'string',
                    description: 'Valid refresh token'
                  }
                }
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Token refreshed successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    accessToken: {
                      type: 'string',
                      description: 'New JWT access token'
                    }
                  }
                }
              }
            }
          },
          '401': {
            description: 'Invalid refresh token',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error'
                }
              }
            }
          },
          '429': {
            description: 'Rate limit exceeded',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/RateLimitError'
                }
              }
            }
          }
        }
      }
    }
  },
  components: {
    schemas: {
      Error: {
        type: 'object',
        properties: {
          error: {
            type: 'object',
            properties: {
              code: { type: 'string' },
              message: { type: 'string' },
              timestamp: { type: 'string', format: 'date-time' }
            }
          }
        }
      },
      RateLimitError: {
        type: 'object',
        properties: {
          error: {
            type: 'object',
            properties: {
              code: { type: 'string', example: 'RATE_LIMIT_EXCEEDED' },
              message: { type: 'string' },
              retryAfter: { type: 'number', description: 'Seconds to wait before retry' },
              timestamp: { type: 'string', format: 'date-time' }
            }
          }
        }
      }
    },
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT'
      }
    }
  },
  tags: [
    {
      name: 'Health',
      description: 'Health and readiness endpoints'
    },
    {
      name: 'Monitoring',
      description: 'Monitoring and metrics endpoints'
    },
    {
      name: 'Authentication',
      description: 'User authentication and token management'
    }
  ]
};

/**
 * Mount Swagger UI documentation
 */
export function mountSwagger(app: Express): void {
  const swaggerOptions = {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'A-ReALM Auth Service API Documentation'
  };

  app.use('/docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, swaggerOptions));
  app.get('/docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(swaggerSpec);
  });
}
