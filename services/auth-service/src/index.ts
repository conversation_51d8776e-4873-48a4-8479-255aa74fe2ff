// Temporarily simplified for debugging
import express from 'express';
import cors from 'cors';
import jwt from 'jsonwebtoken';

const app = express();

// Basic middleware
app.use(cors({
  origin: ['http://localhost:5173'],
  credentials: true
}));
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', service: 'auth-service' });
});

// Basic auth endpoint for testing
app.post('/auth/login', (req, res) => {
  const { username, password } = req.body;

  // Demo credentials - accept username only (like the original API) or username/password
  if (username === 'admin' || username === 'demo' || (username === 'admin' && password === 'demo')) {
    // Determine user role and create proper JWT
    const userRole = username === 'admin' ? 'admin' : 'user';
    const roles = userRole === 'admin' ? ['admin', 'user'] : ['user']; // Admin has both roles

    // JWT secret (in production, this should be from environment)
    const jwtSecret = process.env.JWT_SECRET || 'demo-secret-key-change-in-production';

    // Create proper JWT tokens with role claims
    const accessToken = jwt.sign(
      {
        sub: username,
        roles: roles,
        orgId: '00000000-0000-0000-0000-000000000001',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (15 * 60) // 15 minutes
      },
      jwtSecret
    );

    const refreshToken = jwt.sign(
      {
        sub: username,
        orgId: '00000000-0000-0000-0000-000000000001',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7 days
      },
      jwtSecret
    );

    res.json({
      accessToken,
      refreshToken,
      user: { id: 1, username: username, role: userRole, roles: roles }
    });
  } else {
    res.status(401).json({ success: false, message: 'Invalid credentials' });
  }
});

const port = Number(process.env.AUTH_SERVICE_PORT || 4010);
app.listen(port, () => {
  console.log(`✅ auth-service started on port ${port}`);
});
