"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const prom_client_1 = __importDefault(require("prom-client"));
const config_1 = require("@a-realm/config");
const logger_1 = require("@a-realm/logger");
const otel_1 = require("@a-realm/otel");
(0, otel_1.startTracing)('auth-service');
const logger = (0, logger_1.createLogger)(config_1.config.logLevel);
const app = (0, express_1.default)();
app.disable('x-powered-by');
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({ origin: true, credentials: true }));
app.use(express_1.default.json());
app.use(logger_1.requestIdMiddleware);
app.use((0, logger_1.requestLogger)(logger));
// Health endpoints
app.get('/healthz', (_req, res) => res.status(200).json({ status: 'ok' }));
app.get('/readyz', (_req, res) => res.status(200).json({ status: 'ready' }));
// Metrics
const register = new prom_client_1.default.Registry();
prom_client_1.default.collectDefaultMetrics({ register });
app.get('/metrics', async (_req, res) => {
    res.set('Content-Type', register.contentType);
    res.end(await register.metrics());
});
// Auth endpoints (placeholder; wire to DB/Redis later)
app.post('/auth/login', (req, res) => {
    const { username } = req.body || {};
    if (!username)
        return res.status(400).json({ message: 'username required' });
    const roles = username.toLowerCase().includes('admin') ? ['admin'] : ['user'];
    const token = jsonwebtoken_1.default.sign({ sub: username, roles }, config_1.config.jwtSecret, { expiresIn: config_1.config.tokenTtlSeconds });
    const refresh = jsonwebtoken_1.default.sign({ sub: username }, config_1.config.jwtRefreshSecret, { expiresIn: config_1.config.refreshTtlSeconds });
    return res.json({ accessToken: token, refreshToken: refresh });
});
app.post('/auth/refresh', (req, res) => {
    const { refreshToken } = req.body || {};
    if (!refreshToken)
        return res.status(400).json({ message: 'refreshToken required' });
    try {
        const payload = jsonwebtoken_1.default.verify(refreshToken, config_1.config.jwtRefreshSecret);
        const token = jsonwebtoken_1.default.sign({ sub: payload.sub, roles: ['user'] }, config_1.config.jwtSecret, { expiresIn: config_1.config.tokenTtlSeconds });
        return res.json({ accessToken: token });
    }
    catch (e) {
        return res.status(401).json({ message: 'invalid refresh token' });
    }
});
const port = Number(process.env.AUTH_SERVICE_PORT || config_1.config.port || 4010);
app.listen(port, () => logger.info({ msg: 'auth-service:started', port }));
//# sourceMappingURL=index.js.map