import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import client from 'prom-client';
import { getPrisma, checkDatabaseHealth } from '@a-realm/db';
import { config, createCorsConfig, validateCorsConfig } from '@a-realm/config';
import { createLogger, requestIdMiddleware, requestLogger } from '@a-realm/logger';
import { OdometerSchema, FuelSchema, ReplacementSchema } from './schemas';
import { requireAuth, requireAnyRole } from '@a-realm/auth-middleware';
import { mountSwagger } from './swagger';
import { startTracing } from '@a-realm/otel';
import { 
  globalErrorHandler, 
  notFoundHandler, 
  setupProcessHandlers,
  asyncHandler,
  ErrorFactory 
} from '@a-realm/error-handler';

startTracing('ops-service');
const logger = createLogger(config.logLevel);

// Setup global error handlers
setupProcessHandlers();

// Validate CORS configuration on startup
validateCorsConfig();

const app = express();

app.disable('x-powered-by');
app.use(helmet());
app.use(cors(createCorsConfig()));
app.use(express.json());
app.use(requestIdMiddleware);
app.use(requestLogger(logger));

// Health endpoints
app.get('/healthz', (_req, res) => res.status(200).json({ status: 'ok' }));
app.get('/readyz', async (_req, res) => {
  try {
    const db = await checkDatabaseHealth();
    if (!db.healthy) return res.status(503).json({ status: 'not_ready', db });
    return res.status(200).json({ status: 'ready' });
  } catch {
    return res.status(503).json({ status: 'not_ready' });
  }
});

// Metrics
const register = new client.Registry();
client.collectDefaultMetrics({ register });
app.get('/metrics', async (_req, res) => {
  res.set('Content-Type', register.contentType);
  res.end(await register.metrics());
});

mountSwagger(app);

app.use(requireAuth());

// Ops endpoints with persistence
app.post('/agreements/:id/odometer', requireAnyRole(['user','admin']), (req, res, next) => {
  try { req.body = OdometerSchema.parse(req.body); next(); } catch (e: any) { return res.status(400).json({ message: 'validation error', details: e.errors || e.message }); }
}, async (req, res) => {
  try {
    const prisma = getPrisma();
    const { value } = req.body as any;
    if (typeof value !== 'number') return res.status(400).json({ message: 'value number required' });
    const reading = await prisma.odometerReading.create({ data: { agreementId: req.params.id, value } });
    return res.status(200).json(reading);
  } catch (e: any) {
    logger.error({ msg: 'ops:odometer:error', error: e.message });
    return res.status(500).json({ message: 'internal error' });
  }
});

app.post('/agreements/:id/fuel', requireAnyRole(['user','admin']), (req, res, next) => {
  try { req.body = FuelSchema.parse(req.body); next(); } catch (e: any) { return res.status(400).json({ message: 'validation error', details: e.errors || e.message }); }
}, async (req, res) => {
  try {
    const prisma = getPrisma();
    const { levelPct } = req.body as any;
    if (typeof levelPct !== 'number') return res.status(400).json({ message: 'levelPct number required' });
    const reading = await prisma.fuelReading.create({ data: { agreementId: req.params.id, levelPct } });
    return res.status(200).json(reading);
  } catch (e: any) {
    logger.error({ msg: 'ops:fuel:error', error: e.message });
    return res.status(500).json({ message: 'internal error' });
  }
});

app.post('/agreements/:id/replacement', requireAnyRole(['user','admin']), (req, res, next) => {
  try { req.body = ReplacementSchema.parse(req.body); next(); } catch (e: any) { return res.status(400).json({ message: 'validation error', details: e.errors || e.message }); }
}, async (req, res) => {
  try {
    const prisma = getPrisma();
    const { newVehicleId } = req.body as any;
    if (!newVehicleId) return res.status(400).json({ message: 'newVehicleId required' });
    // Simplified: update agreement vehicleId
    const agreement = await prisma.agreement.update({ where: { id: req.params.id }, data: { vehicleId: newVehicleId } });
    return res.status(200).json(agreement);
  } catch (e: any) {
    logger.error({ msg: 'ops:replacement:error', error: e.message });
    return res.status(500).json({ message: 'internal error' });
  }
});

const port = Number(process.env.OPS_SERVICE_PORT || config.port || 4050);
// Error handling middleware (must be last)
app.use(notFoundHandler());
app.use(globalErrorHandler({
  includeStack: config.nodeEnv === 'development',
  includeContext: config.nodeEnv === 'development'
}));

app.listen(port, () => logger.info({ msg: 'ops-service:started', port }));
