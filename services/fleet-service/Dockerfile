FROM node:18-alpine AS base
WORKDIR /app
RUN corepack enable && corepack prepare pnpm@8.15.4 --activate

COPY package.json pnpm-workspace.yaml turbo.json ./
COPY packages ./packages
COPY services/fleet-service ./services/fleet-service

RUN pnpm install --filter @a-realm/fleet-service... --prod --no-optional
RUN pnpm --filter @a-realm/fleet-service build

FROM node:18-alpine
WORKDIR /app
ENV NODE_ENV=production
COPY --from=base /app/services/fleet-service/dist ./dist
COPY --from=base /app/services/fleet-service/package.json ./package.json
COPY --from=base /app/node_modules ./node_modules
EXPOSE 4030
CMD ["node", "dist/index.js"]

