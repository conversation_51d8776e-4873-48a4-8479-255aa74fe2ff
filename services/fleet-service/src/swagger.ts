import swaggerUi from 'swagger-ui-express';
import type { Express } from 'express';

export function mountSwagger(app: Express) {
  const doc = {
    openapi: '3.0.3',
    info: { title: 'Fleet Service API', version: '0.1.0' },
    paths: {
      '/vehicles': {
        post: {
          summary: 'Create vehicle',
          responses: { '201': { description: 'Created' } }
        }
      }
    }
  };

  app.get('/docs-json', (_req, res) => res.json(doc));
  app.use('/docs', swaggerUi.serve, swaggerUi.setup(doc));
}
