import { z } from 'zod';

export const VehicleCreateSchema = z.object({
  vin: z.string().optional(),
  plateNumber: z.string().optional(),
  businessType: z.enum(['STR', 'LTR', 'SCHOOL', 'CHAUFFEUR', 'IB']).optional(),
  location: z.string().optional()
});

export const VehicleRegisterSchema = z.object({
  registrationNumber: z.string().min(1)
});

export const SalikTagSchema = z.object({ tag: z.string().min(1) });
export const VMDAttachSchema = z.object({ serial: z.string().min(1) });
export const TransferSchema = z.object({ location: z.string().optional(), businessType: z.enum(['STR', 'LTR', 'SCHOOL', 'CHAUFFEUR', 'IB']).optional() });

export type VehicleCreate = z.infer<typeof VehicleCreateSchema>;
export type VehicleRegister = z.infer<typeof VehicleRegisterSchema>;

