import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import client from 'prom-client';
import { getPrisma, checkDatabaseHealth } from '@a-realm/db';
import { publishEvent } from '@a-realm/messaging';
import { config, createCorsConfig, validateCorsConfig } from '@a-realm/config';
import { createLogger, requestIdMiddleware, requestLogger } from '@a-realm/logger';
import { VehicleCreateSchema, VehicleRegisterSchema, SalikTagSchema, VMDAttachSchema, TransferSchema } from './schemas';
import { requireAuth, requireAnyRole } from '@a-realm/auth-middleware';
import { mountSwagger } from './swagger';
import { startTracing } from '@a-realm/otel';
import { 
  globalErrorHandler, 
  notFoundHandler, 
  setupProcessHandlers,
  asyncHandler,
  ErrorFactory 
} from '@a-realm/error-handler';

startTracing('fleet-service');
const logger = createLogger(config.logLevel);

// Setup global error handlers
setupProcessHandlers();

// Validate CORS configuration on startup
validateCorsConfig();

const app = express();

app.disable('x-powered-by');
app.set('trust proxy', 1);
app.use(helmet());
app.use(cors(createCorsConfig()));
app.use(express.json());
app.use(requestIdMiddleware);
app.use(requestLogger(logger));

// Health endpoints
app.get('/healthz', (_req, res) => res.status(200).json({ status: 'ok' }));
app.get('/readyz', async (_req, res) => {
  try {
    const db = await checkDatabaseHealth();
    if (!db.healthy) return res.status(503).json({ status: 'not_ready', db });
    return res.status(200).json({ status: 'ready' });
  } catch {
    return res.status(503).json({ status: 'not_ready' });
  }
});

// Metrics
const register = new client.Registry();
client.collectDefaultMetrics({ register });
app.get('/metrics', async (_req, res) => {
  res.set('Content-Type', register.contentType);
  res.end(await register.metrics());
});

mountSwagger(app);

app.use(requireAuth());

function orgId(req: express.Request) {
  const userOrg = (req as any).user?.orgId;
  const hdr = req.headers['x-org-id'];
  const headerOrg = Array.isArray(hdr) ? hdr[0] : hdr;
  if (!userOrg) return undefined as any;
  if (headerOrg && headerOrg !== userOrg) return 'MISMATCH' as any;
  return userOrg;
}

function validate<T>(schema: { parse: (x: any) => T }) {
  return (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      req.body = schema.parse(req.body);
      next();
    } catch (e: any) {
      return res.status(400).json({ message: 'validation error', details: e.errors || e.message });
    }
  };
}

// Fleet endpoints with persistence
app.post('/vehicles', requireAnyRole(['user','admin']), validate(VehicleCreateSchema), async (req, res) => {
  try {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'orgId required' });
    if (oid === 'MISMATCH') return res.status(403).json({ message: 'org mismatch' });
    const prisma = getPrisma();
    const { vin, plateNumber, businessType, location } = req.body as any;
    const vehicle = await prisma.vehicle.create({ data: { orgId: oid, vin, plateNumber, businessType, location } });
    return res.status(201).json(vehicle);
  } catch (e: any) {
    logger.error({ msg: 'vehicles:create:error', error: e.message });
    return res.status(500).json({ message: 'internal error' });
  }
});

app.post('/vehicles/:id/register', requireAnyRole(['user','admin']), validate(VehicleRegisterSchema), async (req, res) => {
  try {
    const prisma = getPrisma();
    const { registrationNumber } = req.body as any;
    const updated = await prisma.vehicle.update({ where: { id: req.params.id }, data: { registrationNumber, status: 'AVAILABLE' } });
    await publishEvent('vehicle.registered', { type: 'VehicleRegistered', timestamp: new Date().toISOString(), data: { id: updated.id, registrationNumber } });
    return res.status(200).json(updated);
  } catch (e: any) {
    logger.error({ msg: 'vehicles:register:error', error: e.message });
    return res.status(500).json({ message: 'internal error' });
  }
});

app.post('/vehicles/:id/salik-tag', requireAnyRole(['user','admin']), validate(SalikTagSchema), async (req, res) => {
  try {
    const prisma = getPrisma();
    const { tag } = req.body as any;
    const updated = await prisma.vehicle.update({ where: { id: req.params.id }, data: { salikTag: tag } });
    await publishEvent('vehicle.salik_tagged', { type: 'SalikTagged', timestamp: new Date().toISOString(), data: { id: updated.id, tag } });
    return res.status(200).json(updated);
  } catch (e: any) {
    logger.error({ msg: 'vehicles:salik:error', error: e.message });
    return res.status(500).json({ message: 'internal error' });
  }
});

app.post('/vehicles/:id/vmd', requireAnyRole(['user','admin']), validate(VMDAttachSchema), async (req, res) => {
  try {
    const prisma = getPrisma();
    const { serial } = req.body as any;
    const updated = await prisma.vehicle.update({ where: { id: req.params.id }, data: { vmdSerial: serial } });
    await publishEvent('vehicle.vmd_attached', { type: 'VMDAttached', timestamp: new Date().toISOString(), data: { id: updated.id, serial } });
    return res.status(200).json(updated);
  } catch (e: any) {
    logger.error({ msg: 'vehicles:vmd:error', error: e.message });
    return res.status(500).json({ message: 'internal error' });
  }
});

app.post('/vehicles/:id/transfer', requireAnyRole(['user','admin']), validate(TransferSchema), async (req, res) => {
  try {
    const prisma = getPrisma();
    const { location, businessType } = req.body as any;
    const updated = await prisma.vehicle.update({ where: { id: req.params.id }, data: { location, businessType } });
    await publishEvent('vehicle.transferred', { type: 'VehicleTransferred', timestamp: new Date().toISOString(), data: { id: updated.id, location, businessType } });
    return res.status(200).json(updated);
  } catch (e: any) {
    logger.error({ msg: 'vehicles:transfer:error', error: e.message });
    return res.status(500).json({ message: 'internal error' });
  }
});

const port = Number(process.env.FLEET_SERVICE_PORT || config.port || 4030);
// Error handling middleware (must be last)
app.use(notFoundHandler());
app.use(globalErrorHandler({
  includeStack: config.nodeEnv === 'development',
  includeContext: config.nodeEnv === 'development'
}));

app.listen(port, () => logger.info({ msg: 'fleet-service:started', port }));
