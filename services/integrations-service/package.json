{"name": "@a-realm/integrations-service", "version": "0.1.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p tsconfig.json", "dev": "tsx watch src/index.ts", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "start": "node dist/index.js", "typecheck": "tsc -p tsconfig.json"}, "dependencies": {"@a-realm/auth-middleware": "workspace:*", "@a-realm/config": "workspace:*", "@a-realm/db": "workspace:*", "@a-realm/error-handler": "workspace:*", "@a-realm/logger": "workspace:*", "@a-realm/otel": "workspace:*", "cors": "^2.8.5", "express": "^4.19.2", "helmet": "^7.1.0", "prom-client": "^15.1.3"}, "devDependencies": {"tsx": "^4.11.0", "typescript": "^5.4.5"}}