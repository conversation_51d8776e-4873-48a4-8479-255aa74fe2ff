import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import client from 'prom-client';
import fs from 'fs';
import path from 'path';

import { config, createCorsConfig, validateCorsConfig } from '@a-realm/config';
import { createLogger, requestIdMiddleware, requestLogger } from '@a-realm/logger';
import { getPrisma } from '@a-realm/db';
import { requireAuth, requireAnyRole } from '@a-realm/auth-middleware';
import { startTracing } from '@a-realm/otel';
import { mountSwagger } from './swagger';
import { globalErrorHandler, notFoundHandler, setupProcessHandlers } from '@a-realm/error-handler';

startTracing('integrations-service');
const logger = createLogger(config.logLevel);
setupProcessHandlers();
validateCorsConfig();

const app = express();
app.disable('x-powered-by');
app.set('trust proxy', 1);
app.use(helmet());
app.use(cors(createCorsConfig()));
app.use(express.json({ limit: '1mb' }));
app.use(requestIdMiddleware);
app.use(requestLogger(logger));

// Health/metrics
app.get('/healthz', (_req, res) => res.status(200).json({ status: 'ok' }));
const register = new client.Registry();
client.collectDefaultMetrics({ register });
app.get('/metrics', async (_req, res) => { res.set('Content-Type', register.contentType); res.end(await register.metrics()); });

app.use(requireAuth());
mountSwagger(app);

function orgId(req: express.Request) {
  const userOrg = (req as any).user?.orgId;
  const hdr = req.headers['x-org-id'];
  const headerOrg = Array.isArray(hdr) ? hdr[0] : hdr;
  if (!userOrg) return undefined as any;
  if (headerOrg && headerOrg !== userOrg) return 'MISMATCH' as any;
  return userOrg;
}

function ensureDir(p: string) { if (!fs.existsSync(p)) fs.mkdirSync(p, { recursive: true }); }

// List exports
app.get('/exports', requireAnyRole(['admin']), async (req, res) => {
  try {
    const oid = orgId(req); if (!oid) return res.status(400).json({ message: 'orgId required' }); if (oid === 'MISMATCH') return res.status(403).json({ message: 'org mismatch' });
    const prisma = getPrisma();
    const status = (req.query.status as string) as any;
    const where: any = { orgId: oid };
    if (status) where.status = status;
    const list = await prisma.integrationExport.findMany({ where, orderBy: { createdAt: 'desc' }, take: 200 });
    res.json(list);
  } catch (e: any) {
    logger.error({ msg: 'exports:list:error', error: e.message });
    res.status(500).json({ message: 'internal error' });
  }
});

// Run exporters: AR_INVOICE, AR_RECEIPT
app.post('/exports/run', requireAnyRole(['admin']), async (req, res) => {
  const oid = orgId(req); if (!oid) return res.status(400).json({ message: 'orgId required' }); if (oid === 'MISMATCH') return res.status(403).json({ message: 'org mismatch' });
  const prisma = getPrisma();
  const baseDir = process.env.EXPORT_DIR || path.resolve(process.cwd(), 'exports');
  ensureDir(baseDir);
  const now = new Date();
  const pending = await prisma.integrationExport.findMany({ where: { orgId: oid, status: 'PENDING' }, take: 100 });
  const done: any[] = []; const failed: any[] = [];
  for (const exp of pending) {
    try {
      let line = '';
      if (exp.exportType === 'AR_INVOICE') {
        const inv = await prisma.invoice.findUnique({ where: { id: exp.sourceId } });
        if (!inv) throw new Error('invoice not found');
        // Simple CSV: type,id,customer,amount,currency,dueDate
        line = ['INVOICE', inv.id, inv.customerId, inv.amount.toString(), inv.currency, inv.dueDate?.toISOString() || ''].join(',');
      } else if (exp.exportType === 'AR_RECEIPT') {
        const r = await prisma.receipt.findUnique({ where: { id: exp.sourceId } });
        if (!r) throw new Error('receipt not found');
        line = ['RECEIPT', r.id, r.customerId, r.amount.toString(), r.type, r.invoiceId || ''].join(',');
      } else {
        // skip unknown types for now
        await prisma.integrationExport.update({ where: { id: exp.id }, data: { status: 'FAILED', lastError: 'Unknown exportType' } });
        failed.push(exp.id);
        continue;
      }
      const file = path.join(baseDir, `${exp.exportType.toLowerCase()}_${now.getTime()}.csv`);
      fs.appendFileSync(file, line + '\n');
      await prisma.integrationExport.update({ where: { id: exp.id }, data: { status: 'EXPORTED', filePath: file } });
      done.push(exp.id);
    } catch (e: any) {
      await prisma.integrationExport.update({ where: { id: exp.id }, data: { status: 'FAILED', attempts: { increment: 1 }, lastError: e.message } });
      failed.push(exp.id);
    }
  }
  res.json({ exported: done.length, failed: failed.length });
});

// Requeue failed exports
app.post('/exports/:id/requeue', requireAnyRole(['admin']), async (req, res) => {
  const prisma = getPrisma();
  await prisma.integrationExport.update({ where: { id: req.params.id }, data: { status: 'PENDING', lastError: null } });
  res.json({ ok: true });
});

// Errors
app.use(notFoundHandler());
app.use(globalErrorHandler({ includeStack: config.nodeEnv === 'development', includeContext: config.nodeEnv === 'development' }));

const port = Number(process.env.INTEGRATIONS_SERVICE_PORT || config.port || 4060);
app.listen(port, () => logger.info({ msg: 'integrations-service:started', port }));
