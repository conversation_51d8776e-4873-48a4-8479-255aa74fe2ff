import swaggerUi from 'swagger-ui-express';
import type { Express } from 'express';

export function mountSwagger(app: Express) {
  const doc = {
    openapi: '3.0.3',
    info: { title: 'Integrations Service API', version: '0.1.0' },
    paths: {
      '/exports': {
        get: { summary: 'List exports', responses: { '200': { description: 'OK' } } }
      },
      '/exports/run': {
        post: { summary: 'Run exporters', responses: { '200': { description: 'OK' } } }
      },
      '/exports/{id}/requeue': {
        post: { summary: 'Requeue a failed export', responses: { '200': { description: 'OK' } } }
      }
    }
  };
  app.get('/docs-json', (_req, res) => res.json(doc));
  app.use('/docs', swaggerUi.serve, swaggerUi.setup(doc));
}

