import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import client from 'prom-client';
import { getPrisma, checkDatabaseHealth } from '@a-realm/db';
import { publishEvent } from '@a-realm/messaging';
import { config, createCorsConfig, validateCorsConfig } from '@a-realm/config';
import { createLogger, requestIdMiddleware, requestLogger } from '@a-realm/logger';
import { InvoiceCreateSchema, ReceiptCreateSchema, ReceiptApplySchema, SalikImportSchema, FinesImportSchema } from './schemas';
import { requireAuth, requireAnyRole } from '@a-realm/auth-middleware';
import { mountSwagger } from './swagger';
import { startTracing } from '@a-realm/otel';
import { 
  globalErrorHandler, 
  notFoundHandler, 
  setupProcessHandlers,
  asyncHandler,
  ErrorFactory 
} from '@a-realm/error-handler';

startTracing('billing-service');
const logger = createLogger(config.logLevel);

// Setup global error handlers
setupProcessHandlers();

// Validate CORS configuration on startup
validateCorsConfig();

const app = express();

app.disable('x-powered-by');
app.use(helmet());
app.use(cors(createCorsConfig()));
app.use(express.json());
app.use(requestIdMiddleware);
app.use(requestLogger(logger));

// Health endpoints
app.get('/healthz', (_req, res) => res.status(200).json({ status: 'ok' }));
app.get('/readyz', async (_req, res) => {
  try {
    const db = await checkDatabaseHealth();
    if (!db.healthy) return res.status(503).json({ status: 'not_ready', db });
    return res.status(200).json({ status: 'ready' });
  } catch {
    return res.status(503).json({ status: 'not_ready' });
  }
});

// Metrics
const register = new client.Registry();
client.collectDefaultMetrics({ register });
app.get('/metrics', async (_req, res) => {
  res.set('Content-Type', register.contentType);
  res.end(await register.metrics());
});

mountSwagger(app);

app.use(requireAuth());

function orgId(req: express.Request) {
  const userOrg = (req as any).user?.orgId;
  const hdr = req.headers['x-org-id'];
  const headerOrg = Array.isArray(hdr) ? hdr[0] : hdr;
  if (!userOrg) return undefined as any;
  if (headerOrg && headerOrg !== userOrg) return 'MISMATCH' as any;
  return userOrg;
}

function validate<T>(schema: { parse: (x: any) => T }) {
  return (req: express.Request, res: express.Response, next: express.NextFunction) => {
    try {
      req.body = schema.parse(req.body);
      next();
    } catch (e: any) {
      return res.status(400).json({ message: 'validation error', details: e.errors || e.message });
    }
  };
}

// Billing endpoints with persistence
app.post('/invoices', requireAnyRole(['user','admin']), validate(InvoiceCreateSchema), async (req, res) => {
  try {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'orgId required' });
    if (oid === 'MISMATCH') return res.status(403).json({ message: 'org mismatch' });
    const { agreementId, customerId, amount, currency, dueDate } = req.body as any;
    if (!customerId || !amount) return res.status(400).json({ message: 'customerId and amount required' });
    const prisma: any = getPrisma();
    const invoice = await prisma.invoice.create({ data: { orgId: oid, agreementId, customerId, amount, currency, dueDate } });
    await publishEvent('invoice.created', { type: 'InvoiceCreated', timestamp: new Date().toISOString(), data: { id: invoice.id, orgId: oid } });
    // enqueue export
    try { await prisma.integrationExport.create({ data: { orgId: oid, exportType: 'AR_INVOICE', sourceType: 'invoice', sourceId: invoice.id } }); } catch {}
    return res.status(201).json(invoice);
  } catch (e: any) {
    logger.error({ msg: 'invoices:create:error', error: e.message });
    return res.status(500).json({ message: 'internal error' });
  }
});

app.post('/receipts', requireAnyRole(['user','admin']), validate(ReceiptCreateSchema), async (req, res) => {
  try {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'orgId required' });
    if (oid === 'MISMATCH') return res.status(403).json({ message: 'org mismatch' });
    const { customerId, invoiceId, amount, type } = req.body as any;
    if (!customerId || !amount) return res.status(400).json({ message: 'customerId and amount required' });
    const prisma: any = getPrisma();
    const receipt = await prisma.receipt.create({ data: { orgId: oid, customerId, invoiceId, amount, type } });
    await publishEvent('receipt.created', { type: 'ReceiptCreated', timestamp: new Date().toISOString(), data: { id: receipt.id, orgId: oid } });
    try { await prisma.integrationExport.create({ data: { orgId: oid, exportType: 'AR_RECEIPT', sourceType: 'receipt', sourceId: receipt.id } }); } catch {}
    return res.status(201).json(receipt);
  } catch (e: any) {
    logger.error({ msg: 'receipts:create:error', error: e.message });
    return res.status(500).json({ message: 'internal error' });
  }
});

app.post('/receipts/:id/apply', requireAnyRole(['user','admin']), validate(ReceiptApplySchema), async (req, res) => {
  try {
    const { invoiceId } = req.body as any;
    const prisma: any = getPrisma();
    const receipt = await prisma.receipt.update({ where: { id: req.params.id }, data: { applied: true, invoiceId } });
    if (invoiceId) {
      // naive: mark invoice paid
      await prisma.invoice.update({ where: { id: invoiceId }, data: { status: 'PAID' } });
    }
    await publishEvent('receipt.applied', { type: 'ReceiptApplied', timestamp: new Date().toISOString(), data: { id: receipt.id, invoiceId } });
    return res.status(200).json(receipt);
  } catch (e: any) {
    logger.error({ msg: 'receipts:apply:error', error: e.message });
    return res.status(500).json({ message: 'internal error' });
  }
});

// Salik import and list
app.post('/salik/import', requireAnyRole(['admin']), (req, res, next) => {
  try { req.body = SalikImportSchema.parse(req.body); next(); } catch (e: any) { return res.status(400).json({ message: 'validation error', details: e.errors || e.message }); }
}, async (req, res) => {
  try {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'orgId required' });
    if (oid === 'MISMATCH') return res.status(403).json({ message: 'org mismatch' });
    const prisma: any = getPrisma();
    const results: any[] = [];
    for (const r of (req.body as any).records) {
      // try match vehicle by tag or plate
      const vehicle = await prisma.vehicle.findFirst({ where: { orgId: oid, OR: [ { salikTag: r.tag || '' }, { plateNumber: r.plateNumber || '' } ] } });
      let agreementId: string | undefined = undefined;
      let customerId: string | undefined = undefined;
      if (vehicle) {
        const agr = await prisma.agreement.findFirst({ where: { vehicleId: vehicle.id }, orderBy: { startDate: 'desc' } });
        if (agr) { agreementId = agr.id; customerId = agr.customerId; }
      }
      const charge = await prisma.salikCharge.create({ data: {
        orgId: oid,
        vehicleId: vehicle?.id,
        agreementId,
        customerId,
        tag: r.tag,
        plateNumber: r.plateNumber,
        amount: r.amount,
        incurredAt: new Date(r.incurredAt),
        status: agreementId ? 'ALLOCATED' : 'UNALLOCATED'
      } });
      results.push(charge);
    }
    return res.status(201).json({ imported: results.length, charges: results });
  } catch (e: any) {
    logger.error({ msg: 'salik:import:error', error: e.message });
    return res.status(500).json({ message: 'internal error' });
  }
});

app.get('/salik/charges', requireAnyRole(['user','admin']), async (req, res) => {
  try {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'orgId required' });
    if (oid === 'MISMATCH') return res.status(403).json({ message: 'org mismatch' });
    const prisma: any = getPrisma();
    const list = await prisma.salikCharge.findMany({ where: { orgId: oid }, orderBy: { incurredAt: 'desc' }, take: 200 });
    return res.json(list);
  } catch (e: any) {
    logger.error({ msg: 'salik:list:error', error: e.message });
    return res.status(500).json({ message: 'internal error' });
  }
});

// Traffic fines import and list
app.post('/fines/import', requireAnyRole(['admin']), (req, res, next) => {
  try { req.body = FinesImportSchema.parse(req.body); next(); } catch (e: any) { return res.status(400).json({ message: 'validation error', details: e.errors || e.message }); }
}, async (req, res) => {
  try {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'orgId required' });
    if (oid === 'MISMATCH') return res.status(403).json({ message: 'org mismatch' });
    const prisma = getPrisma();
    const results: any[] = [];
    for (const r of (req.body as any).records) {
      const vehicle = await prisma.vehicle.findFirst({ where: { orgId: oid, plateNumber: r.plateNumber || undefined } });
      let agreementId: string | undefined = undefined;
      let customerId: string | undefined = undefined;
      if (vehicle) {
        const agr = await prisma.agreement.findFirst({ where: { vehicleId: vehicle.id }, orderBy: { startDate: 'desc' } });
        if (agr) { agreementId = agr.id; customerId = agr.customerId; }
      }
      const fine = await prisma.trafficFine.create({ data: {
        orgId: oid,
        vehicleId: vehicle?.id,
        agreementId,
        customerId,
        plateNumber: r.plateNumber,
        violationCode: r.violationCode,
        amount: r.amount,
        occurredAt: new Date(r.occurredAt),
        status: agreementId ? 'ALLOCATED' : 'UNALLOCATED'
      } });
      results.push(fine);
    }
    return res.status(201).json({ imported: results.length, fines: results });
  } catch (e: any) {
    logger.error({ msg: 'fines:import:error', error: e.message });
    return res.status(500).json({ message: 'internal error' });
  }
});

app.get('/fines', requireAnyRole(['user','admin']), async (req, res) => {
  try {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'orgId required' });
    if (oid === 'MISMATCH') return res.status(403).json({ message: 'org mismatch' });
    const prisma = getPrisma();
    const list = await prisma.trafficFine.findMany({ where: { orgId: oid }, orderBy: { occurredAt: 'desc' }, take: 200 });
    return res.json(list);
  } catch (e: any) {
    logger.error({ msg: 'fines:list:error', error: e.message });
    return res.status(500).json({ message: 'internal error' });
  }
});

// Recurring billing: generate invoices for due schedules
app.post('/recurring/run', requireAnyRole(['admin']), async (req, res) => {
  try {
    const oid = orgId(req);
    if (!oid) return res.status(400).json({ message: 'orgId required' });
    if (oid === 'MISMATCH') return res.status(403).json({ message: 'org mismatch' });
    const prisma = getPrisma();
    const now = new Date();
    const due = await prisma.agreementSchedule.findMany({
      where: {
        status: 'DUE',
        dueDate: { lte: now },
        agreement: { orgId: oid }
      },
      include: { agreement: true },
      take: 100
    });
    const created: any[] = [];
    for (const s of due) {
      if (!s.agreement) continue;
      const invoice = await prisma.invoice.create({ data: {
        orgId: oid,
        agreementId: s.agreementId,
        customerId: s.agreement.customerId,
        amount: s.amount,
        currency: s.currency,
        issueDate: now,
        dueDate: s.dueDate,
        status: 'OPEN'
      } });
      await prisma.agreementSchedule.update({ where: { id: s.id }, data: { status: 'INVOICED', invoiceId: invoice.id } });
      created.push(invoice);
      await publishEvent('invoice.created', { type: 'InvoiceCreated', timestamp: new Date().toISOString(), data: { id: invoice.id, orgId: oid } });
    }
    return res.json({ generated: created.length, invoices: created });
  } catch (e: any) {
    logger.error({ msg: 'recurring:run:error', error: e.message });
    return res.status(500).json({ message: 'internal error' });
  }
});

const port = Number(process.env.BILLING_SERVICE_PORT || config.port || 4040);
// Error handling middleware (must be last)
app.use(notFoundHandler());
app.use(globalErrorHandler({
  includeStack: config.nodeEnv === 'development',
  includeContext: config.nodeEnv === 'development'
}));

app.listen(port, () => logger.info({ msg: 'billing-service:started', port }));
