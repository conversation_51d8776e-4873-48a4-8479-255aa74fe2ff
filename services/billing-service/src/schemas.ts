import { z } from 'zod';

export const InvoiceCreateSchema = z.object({
  agreementId: z.string().uuid().optional(),
  customerId: z.string().uuid(),
  amount: z.coerce.number().positive(),
  currency: z.string().default('AED'),
  dueDate: z.coerce.date().optional()
});

export const ReceiptCreateSchema = z.object({
  customerId: z.string().uuid(),
  invoiceId: z.string().uuid().optional(),
  amount: z.coerce.number().positive(),
  type: z.enum(['STANDARD', 'PDC']).default('STANDARD')
});

export const ReceiptApplySchema = z.object({
  invoiceId: z.string().uuid().optional()
});

export const SalikImportSchema = z.object({
  source: z.string().default('MANUAL'),
  records: z.array(z.object({
    tag: z.string().optional(),
    plateNumber: z.string().optional(),
    amount: z.coerce.number().positive(),
    incurredAt: z.coerce.date()
  })).min(1)
});

export const FinesImportSchema = z.object({
  source: z.string().default('MANUAL'),
  records: z.array(z.object({
    plateNumber: z.string().optional(),
    violationCode: z.string().optional(),
    amount: z.coerce.number().positive(),
    occurredAt: z.coerce.date()
  })).min(1)
});
