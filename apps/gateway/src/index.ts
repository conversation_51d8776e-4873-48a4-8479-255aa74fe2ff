import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import client from 'prom-client';
import { config, createCorsConfig, validateCorsConfig } from '@a-realm/config';
import { createLogger, requestIdMiddleware, requestLogger } from '@a-realm/logger';
import jwt from 'jsonwebtoken';
import { createProxyMiddleware } from 'http-proxy-middleware';
import { startTracing } from '@a-realm/otel';
import rateLimit from 'express-rate-limit';
import hpp from 'hpp';

startTracing('gateway');
const logger = createLogger(config.logLevel);

// Setup global error handlers
setupProcessHandlers();

// Validate CORS configuration on startup
validateCorsConfig();

const app = express();

app.disable('x-powered-by');
app.set('trust proxy', 1);
app.use(helmet());
app.use(
  helmet.contentSecurityPolicy({
    useDefaults: true,
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", 'data:'],
      connectSrc: ["'self'"],
    }
  })
);
// Secure CORS configuration
const apiCors = cors(createCorsConfig());
// WAF: JSON parser with strict limits
app.use(express.json({ limit: '512kb' }));
// Prevent HTTP parameter pollution
app.use(hpp());
app.use(requestIdMiddleware);
app.use(requestLogger(logger));

// Rate limiting
const generalLimiter = rateLimit({ windowMs: 60_000, max: 300, standardHeaders: true, legacyHeaders: false });
const authLimiter = rateLimit({ windowMs: 60_000, max: 60 });
// Apply CORS and rate-limits per route group
app.use('/api/', apiCors, generalLimiter);
app.use('/api/auth', apiCors, authLimiter);
app.use('/api/sales', apiCors, rateLimit({ windowMs: 60_000, max: 240 }));
app.use('/api/fleet', apiCors, rateLimit({ windowMs: 60_000, max: 240 }));
app.use('/api/billing', apiCors, rateLimit({ windowMs: 60_000, max: 120 }));
app.use('/api/ops', apiCors, rateLimit({ windowMs: 60_000, max: 240 }));

// Auth middleware (JWT)
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request { user?: any }
  }
}

function authMiddleware(req: express.Request, res: express.Response, next: express.NextFunction) {
  const header = req.headers.authorization;
  if (!header || !header.startsWith('Bearer ')) return res.status(401).json({ message: 'missing token' });
  const token = header.slice(7);
  try {
    const payload = jwt.verify(token, config.jwtSecret);
    req.user = payload;
    next();
  } catch (_e) {
    return res.status(401).json({ message: 'invalid token' });
  }
}

function requireAnyRole(roles: string[]) {
  return (req: express.Request, res: express.Response, next: express.NextFunction) => {
    const userRoles: string[] = (req.user?.roles as string[]) || [];
    if (roles.some(r => userRoles.includes(r))) return next();
    return res.status(403).json({ message: 'forbidden' });
  };
}

// Health endpoints
app.get('/healthz', (_req, res) => res.status(200).json({ status: 'ok' }));
app.get('/readyz', (_req, res) => res.status(200).json({ status: 'ready' }));

// Metrics
const register = new client.Registry();
client.collectDefaultMetrics({ register });
app.get('/metrics', async (_req, res) => {
  res.set('Content-Type', register.contentType);
  res.end(await register.metrics());
});

// API Docs aggregator (multi-spec)
import swaggerUi from 'swagger-ui-express';
import { fetch } from 'undici';
import { 
  globalErrorHandler, 
  notFoundHandler, 
  setupProcessHandlers,
  asyncHandler,
  ErrorFactory 
} from '@a-realm/error-handler';

// Lightweight proxied JSON endpoints for each service spec
async function forwardSpec(res: express.Response, url?: string) {
  if (!url) return res.status(503).json({ message: 'spec upstream not configured' });
  try {
    const r = await fetch(url);
    const j = await r.json();
    return res.json(j);
  } catch (e: any) {
    return res.status(502).json({ message: 'failed to fetch spec', error: e.message });
  }
}

app.get('/api/docs-json/sales', (req, res) => forwardSpec(res, (process.env.SALES_URL || config.salesUrl || 'http://localhost:4020') + '/docs-json'));
app.get('/api/docs-json/fleet', (req, res) => forwardSpec(res, (process.env.FLEET_URL || config.fleetUrl || 'http://localhost:4030') + '/docs-json'));
app.get('/api/docs-json/billing', (req, res) => forwardSpec(res, (process.env.BILLING_URL || config.billingUrl || 'http://localhost:4040') + '/docs-json'));
app.get('/api/docs-json/ops', (req, res) => forwardSpec(res, (process.env.OPS_URL || config.opsUrl || 'http://localhost:4050') + '/docs-json'));
app.get('/api/docs-json/integrations', (req, res) => forwardSpec(res, (process.env.INTEGRATIONS_URL || config.integrationsUrl || 'http://localhost:4060') + '/docs-json'));

app.use('/api/docs', swaggerUi.serve, swaggerUi.setup(null, {
  swaggerOptions: {
    urls: [
      { url: '/api/docs-json/sales', name: 'Sales' },
      { url: '/api/docs-json/fleet', name: 'Fleet' },
      { url: '/api/docs-json/billing', name: 'Billing' },
      { url: '/api/docs-json/ops', name: 'Ops' },
      { url: '/api/docs-json/integrations', name: 'Integrations' }
    ]
  }
}));

// Simple status endpoint aggregating health from services
app.get('/api/status', async (_req, res) => {
  const services = [
    { name: 'auth', base: process.env.AUTH_URL || config.authUrl || 'http://localhost:4010' },
    { name: 'sales', base: process.env.SALES_URL || config.salesUrl || 'http://localhost:4020' },
    { name: 'fleet', base: process.env.FLEET_URL || config.fleetUrl || 'http://localhost:4030' },
    { name: 'billing', base: process.env.BILLING_URL || config.billingUrl || 'http://localhost:4040' },
    { name: 'ops', base: process.env.OPS_URL || config.opsUrl || 'http://localhost:4050' }
  ];
  const results: any[] = [];
  for (const s of services) {
    const r: any = { name: s.name, health: null, ready: null };
    try { const x = await fetch(`${s.base}/healthz`); r.health = x.status; } catch { r.health = 'ERR'; }
    try { const x = await fetch(`${s.base}/readyz`); r.ready = x.status; } catch { r.ready = 'ERR'; }
    results.push(r);
  }
  res.json({ services: results });
});

// Docs index (HTML) with links and health
app.get('/api/docs/index', async (_req, res) => {
  const base = '/api/docs-json';
  const status = await (await fetch('http://localhost:' + (process.env.GATEWAY_PORT || config.port || 4000) + '/api/status').catch(() => ({ json: async () => ({ services: [] }) }))).json().catch(() => ({ services: [] }));
  const links = [
    { name: 'Sales', href: base + '/sales' },
    { name: 'Fleet', href: base + '/fleet' },
    { name: 'Billing', href: base + '/billing' },
    { name: 'Ops', href: base + '/ops' }
  ];
  const body = `<!doctype html><html><head><meta charset="utf-8"><title>Docs Index</title></head><body>
  <h2>Service Docs</h2>
  <ul>${links.map(l => `<li><a href="${l.href}">${l.name}</a></li>`).join('')}</ul>
  <h3>Status</h3>
  <pre>${JSON.stringify(status, null, 2)}</pre>
  <p>For full UI: <a href="/api/docs">/api/docs</a></p>
  </body></html>`;
  res.setHeader('content-type', 'text/html');
  res.send(body);
});
app.get('/api/secure/ping', authMiddleware, requireAnyRole(['user', 'admin']), (_req, res) => res.json({ ok: true }));

// Proxy helpers
function mkProxy(target?: string, basePath?: string) {
  if (!target) return undefined;
  const rewrite: Record<string, string> = {};
  if (basePath) rewrite[`^${basePath}`] = '';
  return createProxyMiddleware({
    target,
    changeOrigin: true,
    pathRewrite: rewrite
  } as any);
}

// Mount proxied routes
const salesProxy = mkProxy(process.env.SALES_URL || config.salesUrl || 'http://localhost:4020', '/api/sales');
const fleetProxy = mkProxy(process.env.FLEET_URL || config.fleetUrl || 'http://localhost:4030', '/api/fleet');
const billingProxy = mkProxy(process.env.BILLING_URL || config.billingUrl || 'http://localhost:4040', '/api/billing');
const opsProxy = mkProxy(process.env.OPS_URL || config.opsUrl || 'http://localhost:4050', '/api/ops');
const integrationsProxy = mkProxy(process.env.INTEGRATIONS_URL || config.integrationsUrl || 'http://localhost:4060', '/api/integrations');
const authProxy = mkProxy(process.env.AUTH_URL || config.authUrl || 'http://localhost:4010', '/api/auth');

// WAF: Require JSON content-type for write methods
function requireJsonForBody(req: express.Request, res: express.Response, next: express.NextFunction) {
  if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
    if (!req.is('application/json')) return res.status(415).json({ message: 'content-type application/json required' });
  }
  next();
}

// Enforce tenant from JWT and inject header for upstream services
function enforceOrgFromToken(req: express.Request, res: express.Response, next: express.NextFunction) {
  const userOrg = (req as any).user?.orgId;
  if (!userOrg) return res.status(400).json({ message: 'orgId missing in token' });
  const hdr = req.headers['x-org-id'];
  const hdrVal = Array.isArray(hdr) ? hdr[0] : hdr;
  if (hdrVal && hdrVal !== userOrg) return res.status(403).json({ message: 'org mismatch' });
  // inject or overwrite
  req.headers['x-org-id'] = userOrg;
  next();
}

if (authProxy) app.use('/api/auth', authProxy);
if (salesProxy) app.use('/api/sales', authMiddleware, requireAnyRole(['user', 'admin']), enforceOrgFromToken, requireJsonForBody, salesProxy);
if (fleetProxy) app.use('/api/fleet', authMiddleware, requireAnyRole(['user', 'admin']), enforceOrgFromToken, requireJsonForBody, fleetProxy);
if (billingProxy) app.use('/api/billing', authMiddleware, requireAnyRole(['user', 'admin']), enforceOrgFromToken, requireJsonForBody, billingProxy);
if (opsProxy) app.use('/api/ops', authMiddleware, requireAnyRole(['user', 'admin']), enforceOrgFromToken, requireJsonForBody, opsProxy);
if (integrationsProxy) app.use('/api/integrations', authMiddleware, requireAnyRole(['admin']), enforceOrgFromToken, requireJsonForBody, integrationsProxy);

const port = Number(process.env.GATEWAY_PORT || config.port || 4000);
// Error handling middleware (must be last)
app.use(notFoundHandler());
app.use(globalErrorHandler({
  includeStack: config.nodeEnv === 'development',
  includeContext: config.nodeEnv === 'development'
}));

app.listen(port, () => logger.info({ msg: 'gateway:started', port }));
