# syntax=docker/dockerfile:1
FROM node:18-alpine AS base
WORKDIR /app

RUN corepack enable && corepack prepare pnpm@8.15.4 --activate

COPY package.json pnpm-workspace.yaml turbo.json ./
COPY packages ./packages
COPY apps/gateway ./apps/gateway

RUN pnpm install --filter @a-realm/gateway... --prod --no-optional
RUN pnpm --filter @a-realm/gateway build

FROM node:18-alpine
WORKDIR /app
ENV NODE_ENV=production
COPY --from=base /app/apps/gateway/dist ./dist
COPY --from=base /app/apps/gateway/package.json ./package.json
COPY --from=base /app/node_modules ./node_modules
EXPOSE 4000
CMD ["node", "dist/index.js"]

