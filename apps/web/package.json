{"name": "@a-realm/web", "version": "0.1.0", "private": true, "scripts": {"build": "tsc -b && vite build", "dev": "vite", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.18.0", "@mui/material": "^5.15.17", "@reduxjs/toolkit": "^2.2.3", "@tanstack/react-query": "^5.45.0", "date-fns": "^3.6.0", "framer-motion": "^11.18.2", "lodash": "^4.17.21", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.51.5", "react-router-dom": "^6.25.1", "recharts": "^2.12.7", "yup": "^1.4.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "typescript": "^5.4.5", "vite": "^5.3.1"}}