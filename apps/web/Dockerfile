FROM node:18-alpine AS base
WORKDIR /app
RUN corepack enable && corepack prepare pnpm@8.15.4 --activate

COPY package.json pnpm-workspace.yaml turbo.json ./
COPY apps/web ./apps/web

RUN pnpm install --filter @a-realm/web... --prod --no-optional
RUN pnpm --filter @a-realm/web build

FROM nginx:alpine
COPY --from=base /app/apps/web/dist /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]

