import {
  Box,
  Container,
  Typography,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON>rid,
  <PERSON><PERSON>,
  Tabs,
  Tab,
  Fab
} from '@mui/material';
import {
  Add as AddIcon,
  Dashboard as DashboardIcon,
  Assignment as AssignmentIcon,
  AccountBalance as AccountBalanceIcon
} from '@mui/icons-material';
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { STRAgreement, STRListItem } from '../types/str';
import { getSTRPermissions, canUserPerformAction } from '../utils/strPermissions';
import STRCreateWizard from '../components/str/STRCreateWizard';

// Mock data for development
const mockAgreements: STRListItem[] = [
  {
    id: 'STR-001',
    agreementNumber: 'STR-2024-001',
    customerName: '<PERSON>',
    vehiclePlate: 'ABC-123',
    vehicleModel: 'Toyota Camry 2023',
    startDate: '2024-01-15',
    endDate: '2024-01-20',
    status: 'ACTIVE',
    workflowStage: 'ACTIVE',
    totalAmount: 1500,
    outstandingAmount: 0,
    canExtend: true,
    canReturn: true,
    canPay: false
  },
  {
    id: 'STR-002',
    agreementNumber: 'STR-2024-002',
    customerName: 'Jane Smith',
    vehiclePlate: 'XYZ-789',
    vehicleModel: 'Honda Accord 2023',
    startDate: '2024-01-10',
    endDate: '2024-01-25',
    status: 'ON_HIRE',
    workflowStage: 'HANDOVER',
    totalAmount: 2200,
    outstandingAmount: 500,
    canExtend: false,
    canReturn: false,
    canPay: true
  }
];

interface STRNewProps {
  userRoles: string[];
  token: string;
}

export default function STRNew({ userRoles, token }: STRNewProps) {
  const navigate = useNavigate();
  const [currentTab, setCurrentTab] = useState(0);
  const [showCreateWizard, setShowCreateWizard] = useState(false);
  const [agreements, setAgreements] = useState<STRListItem[]>(mockAgreements);
  const [loading, setLoading] = useState(false);

  const permissions = getSTRPermissions(userRoles);
  const isAgent = permissions.canCreate;
  const isCustomer = !isAgent;

  useEffect(() => {
    loadAgreements();
  }, []);

  const loadAgreements = async () => {
    setLoading(true);
    try {
      // TODO: Implement API call based on user role
      // if (isAgent) {
      //   const response = await api.getAllAgreements(token);
      //   setAgreements(response);
      // } else {
      //   const response = await api.getCustomerAgreements(token);
      //   setAgreements(response);
      // }
      
      // For now, use mock data
      setAgreements(mockAgreements);
    } catch (error) {
      console.error('Failed to load agreements:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAgreement = () => {
    if (permissions.canCreate) {
      setShowCreateWizard(true);
    }
  };

  const handleWizardComplete = (agreement: STRAgreement) => {
    setShowCreateWizard(false);
    // Add the new agreement to the list
    const newListItem: STRListItem = {
      id: agreement.id,
      agreementNumber: agreement.agreementNumber,
      customerName: agreement.customer?.name || 'Unknown',
      vehiclePlate: agreement.vehicle?.plateNumber || 'Unknown',
      vehicleModel: `${agreement.vehicle?.make} ${agreement.vehicle?.model}` || 'Unknown',
      startDate: agreement.startDate,
      endDate: agreement.endDate,
      status: agreement.status,
      workflowStage: agreement.workflowStage,
      totalAmount: agreement.totalAmount,
      outstandingAmount: agreement.outstandingAmount,
      canExtend: agreement.allowExtension,
      canReturn: agreement.allowEarlyReturn,
      canPay: agreement.outstandingAmount > 0
    };
    
    setAgreements(prev => [newListItem, ...prev]);
    
    // Navigate to the agreement detail page
    navigate(`/str/${agreement.id}`);
  };

  const handleWizardCancel = () => {
    setShowCreateWizard(false);
  };

  const renderAgentDashboard = () => (
    <Box>
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            STR Management Console
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage Short Term Rental agreements and operations
          </Typography>
        </Box>
        
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateAgreement}
          size="large"
        >
          Create STR Agreement
        </Button>
      </Stack>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <DashboardIcon color="primary" />
                <Box>
                  <Typography variant="h6">12</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Agreements
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <AssignmentIcon color="warning" />
                <Box>
                  <Typography variant="h6">5</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Pending Returns
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <AccountBalanceIcon color="success" />
                <Box>
                  <Typography variant="h6">AED 45,200</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Outstanding Amount
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <DashboardIcon color="info" />
                <Box>
                  <Typography variant="h6">3</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Vehicles Available
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card>
        <CardContent>
          <Tabs value={currentTab} onChange={(_, value) => setCurrentTab(value)} sx={{ mb: 3 }}>
            <Tab label="All Agreements" />
            <Tab label="Active" />
            <Tab label="Pending Closure" />
            <Tab label="Closed" />
          </Tabs>
          
          <Typography variant="h6" gutterBottom>
            Recent Agreements
          </Typography>
          
          {agreements.length === 0 ? (
            <Alert severity="info">
              No agreements found. Create your first STR agreement to get started.
            </Alert>
          ) : (
            <Box>
              {agreements.map((agreement) => (
                <Card key={agreement.id} variant="outlined" sx={{ mb: 2 }}>
                  <CardContent>
                    <Grid container spacing={2} alignItems="center">
                      <Grid item xs={12} md={3}>
                        <Typography variant="subtitle1" fontWeight={600}>
                          {agreement.agreementNumber}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {agreement.customerName}
                        </Typography>
                      </Grid>
                      
                      <Grid item xs={12} md={3}>
                        <Typography variant="body2">
                          {agreement.vehiclePlate}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {agreement.vehicleModel}
                        </Typography>
                      </Grid>
                      
                      <Grid item xs={12} md={2}>
                        <Typography variant="body2">
                          {new Date(agreement.startDate).toLocaleDateString()} - {new Date(agreement.endDate).toLocaleDateString()}
                        </Typography>
                      </Grid>
                      
                      <Grid item xs={12} md={2}>
                        <Typography variant="body2" fontWeight={500}>
                          AED {agreement.totalAmount.toLocaleString()}
                        </Typography>
                        {agreement.outstandingAmount > 0 && (
                          <Typography variant="caption" color="error">
                            Outstanding: AED {agreement.outstandingAmount.toLocaleString()}
                          </Typography>
                        )}
                      </Grid>
                      
                      <Grid item xs={12} md={2}>
                        <Button
                          variant="outlined"
                          size="small"
                          onClick={() => navigate(`/str/${agreement.id}`)}
                        >
                          View Details
                        </Button>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              ))}
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );

  const renderCustomerPortal = () => (
    <Box>
      <Typography variant="h4" gutterBottom>
        My Rentals
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        View and manage your rental agreements
      </Typography>

      {agreements.length === 0 ? (
        <Alert severity="info">
          You don't have any rental agreements yet.
        </Alert>
      ) : (
        <Grid container spacing={3}>
          {agreements.map((agreement) => (
            <Grid item xs={12} md={6} lg={4} key={agreement.id}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {agreement.vehicleModel}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {agreement.agreementNumber}
                  </Typography>
                  
                  <Stack spacing={1} sx={{ mb: 2 }}>
                    <Typography variant="body2">
                      <strong>Dates:</strong> {new Date(agreement.startDate).toLocaleDateString()} - {new Date(agreement.endDate).toLocaleDateString()}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Total:</strong> AED {agreement.totalAmount.toLocaleString()}
                    </Typography>
                    {agreement.outstandingAmount > 0 && (
                      <Typography variant="body2" color="error">
                        <strong>Outstanding:</strong> AED {agreement.outstandingAmount.toLocaleString()}
                      </Typography>
                    )}
                  </Stack>
                  
                  <Stack direction="row" spacing={1}>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => navigate(`/str/${agreement.id}`)}
                    >
                      View Details
                    </Button>
                    {agreement.canPay && (
                      <Button variant="contained" size="small" color="primary">
                        Pay Now
                      </Button>
                    )}
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );

  if (showCreateWizard) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <STRCreateWizard
          onComplete={handleWizardComplete}
          onCancel={handleWizardCancel}
          userRoles={userRoles}
          token={token}
        />
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {isAgent ? renderAgentDashboard() : renderCustomerPortal()}
      
      {/* Floating Action Button for Quick Create (Agent only) */}
      {isAgent && !showCreateWizard && (
        <Fab
          color="primary"
          aria-label="create agreement"
          onClick={handleCreateAgreement}
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
          }}
        >
          <AddIcon />
        </Fab>
      )}
    </Container>
  );
}
