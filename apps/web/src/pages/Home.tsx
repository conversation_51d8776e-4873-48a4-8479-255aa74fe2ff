import React from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Stack,
  Chip,
  Avatar,
  useTheme,
  useMediaQuery,
  Paper,
  Divider
} from '@mui/material';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  DirectionsCar,
  Assignment,
  AccountBalance,
  Analytics,
  Security,
  Speed,
  CloudSync,
  Support
} from '@mui/icons-material';
import AdminDashboard from '../components/AdminDashboard';
import UserDashboard from '../components/UserDashboard';

interface HomeProps {
  token: string | null;
  roles: string[];
}

const Home: React.FC<HomeProps> = ({ token, roles }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isAdmin = roles.includes('admin');

  // If user is authenticated, show role-based dashboard
  if (token) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ py: 3 }}>
          {isAdmin ? (
            <AdminDashboard token={token} roles={roles} />
          ) : (
            <UserDashboard token={token} roles={roles} />
          )}
        </Box>
      </Container>
    );
  }

  // Landing page for unauthenticated users
  const features = [
    {
      icon: <DirectionsCar />,
      title: 'Fleet Management',
      description: 'Comprehensive vehicle tracking, maintenance scheduling, and availability management.',
      color: '#3f51b5'
    },
    {
      icon: <Assignment />,
      title: 'Agreement Processing',
      description: 'Streamlined rental agreements with automated workflows and digital signatures.',
      color: '#00bcd4'
    },
    {
      icon: <AccountBalance />,
      title: 'Financial Operations',
      description: 'Automated billing, payment processing, and comprehensive financial reporting.',
      color: '#4caf50'
    },
    {
      icon: <Analytics />,
      title: 'Business Intelligence',
      description: 'Real-time analytics, performance metrics, and data-driven insights.',
      color: '#ff9800'
    }
  ];

  const quickActions = [
    {
      title: 'Create STR Agreement',
      description: 'Start a new short-term rental agreement',
      link: '/str',
      icon: <DirectionsCar />,
      color: 'primary',
      available: true
    },
    {
      title: 'Process Agreement Closure',
      description: 'Close existing rental agreements',
      link: '/closure',
      icon: <Assignment />,
      color: 'secondary',
      available: true
    },
    {
      title: 'Billing & Payments',
      description: 'Manage invoices and process payments',
      link: '/billing',
      icon: <AccountBalance />,
      color: 'success',
      available: isAdmin
    },
    {
      title: 'Fleet Analytics',
      description: 'View fleet performance and analytics',
      link: '/admin/lease',
      icon: <Analytics />,
      color: 'warning',
      available: isAdmin
    }
  ];

  const stats = [
    { label: 'Active Vehicles', value: '150+', icon: <DirectionsCar /> },
    { label: 'Monthly Agreements', value: '500+', icon: <Assignment /> },
    { label: 'Revenue Processed', value: '$2M+', icon: <AccountBalance /> },
    { label: 'Customer Satisfaction', value: '98%', icon: <Support /> }
  ];

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          background: `linear-gradient(135deg, ${theme.palette.primary.main}15 0%, ${theme.palette.secondary.main}15 100%)`,
          py: { xs: 6, md: 10 },
          mb: 6
        }}
      >
        <Container maxWidth="lg">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Grid container spacing={4} alignItems="center">
              <Grid item xs={12} md={6}>
                <Typography
                  variant="h2"
                  component="h1"
                  gutterBottom
                  sx={{
                    fontWeight: 700,
                    fontSize: { xs: '2.5rem', md: '3.5rem' },
                    background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent'
                  }}
                >
                  A-ReALM
                </Typography>
                <Typography
                  variant="h5"
                  color="text.secondary"
                  gutterBottom
                  sx={{ mb: 3, fontWeight: 400 }}
                >
                  Automotive Rental and Lease Management System
                </Typography>
                <Typography
                  variant="body1"
                  color="text.secondary"
                  sx={{ mb: 4, fontSize: '1.1rem', lineHeight: 1.6 }}
                >
                  Streamline your automotive rental business with our comprehensive platform. 
                  Manage fleet operations, process agreements, handle billing, and gain valuable 
                  insights - all in one powerful system.
                </Typography>
                <Stack direction="row" spacing={2} flexWrap="wrap">
                  {token ? (
                    <Button
                      variant="contained"
                      size="large"
                      component={Link}
                      to="/str"
                      sx={{ px: 4, py: 1.5 }}
                    >
                      Go to Dashboard
                    </Button>
                  ) : (
                    <Button
                      variant="contained"
                      size="large"
                      component={Link}
                      to="/login"
                      sx={{ px: 4, py: 1.5 }}
                    >
                      Get Started
                    </Button>
                  )}
                  <Button
                    variant="outlined"
                    size="large"
                    sx={{ px: 4, py: 1.5 }}
                  >
                    Learn More
                  </Button>
                </Stack>
              </Grid>
              <Grid item xs={12} md={6}>
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  <Paper
                    elevation={0}
                    sx={{
                      p: 4,
                      background: 'rgba(255, 255, 255, 0.9)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: 3,
                      border: '1px solid rgba(255, 255, 255, 0.2)'
                    }}
                  >
                    <Grid container spacing={2}>
                      {stats.map((stat, index) => (
                        <Grid item xs={6} key={index}>
                          <Box textAlign="center">
                            <Avatar
                              sx={{
                                bgcolor: theme.palette.primary.main,
                                mx: 'auto',
                                mb: 1,
                                width: 48,
                                height: 48
                              }}
                            >
                              {stat.icon}
                            </Avatar>
                            <Typography variant="h6" fontWeight="bold">
                              {stat.value}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {stat.label}
                            </Typography>
                          </Box>
                        </Grid>
                      ))}
                    </Grid>
                  </Paper>
                </motion.div>
              </Grid>
            </Grid>
          </motion.div>
        </Container>
      </Box>

      <Container maxWidth="lg">
        {/* Authentication Status */}
        {token && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Paper
              sx={{
                p: 3,
                mb: 4,
                background: `linear-gradient(135deg, ${theme.palette.success.main}15, ${theme.palette.success.main}05)`,
                border: `1px solid ${theme.palette.success.main}30`
              }}
            >
              <Stack direction="row" alignItems="center" spacing={2}>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <Security />
                </Avatar>
                <Box>
                  <Typography variant="h6" color="success.main">
                    Welcome back!
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    You're logged in as: {roles.join(', ') || 'user'}
                  </Typography>
                </Box>
                <Box sx={{ ml: 'auto' }}>
                  <Chip
                    label={`${roles.length} role${roles.length !== 1 ? 's' : ''}`}
                    color="success"
                    variant="outlined"
                  />
                </Box>
              </Stack>
            </Paper>
          </motion.div>
        )}

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Typography variant="h4" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
            Quick Actions
          </Typography>
          <Grid container spacing={3} sx={{ mb: 6 }}>
            {quickActions.map((action, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Card
                  component={motion.div}
                  whileHover={{ y: -4, transition: { duration: 0.2 } }}
                  sx={{
                    height: '100%',
                    opacity: action.available ? 1 : 0.6,
                    cursor: action.available ? 'pointer' : 'not-allowed',
                    transition: 'all 0.3s ease'
                  }}
                >
                  <CardContent sx={{ pb: 1 }}>
                    <Avatar
                      sx={{
                        bgcolor: `${action.color}.main`,
                        mb: 2,
                        width: 56,
                        height: 56
                      }}
                    >
                      {action.icon}
                    </Avatar>
                    <Typography variant="h6" gutterBottom>
                      {action.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {action.description}
                    </Typography>
                  </CardContent>
                  <CardActions>
                    {action.available ? (
                      <Button
                        component={Link}
                        to={action.link}
                        color={action.color as any}
                        size="small"
                      >
                        Open
                      </Button>
                    ) : (
                      <Button disabled size="small">
                        Admin Only
                      </Button>
                    )}
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        </motion.div>

        {/* Features Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Typography variant="h4" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
            Platform Features
          </Typography>
          <Grid container spacing={4} sx={{ mb: 6 }}>
            {features.map((feature, index) => (
              <Grid item xs={12} md={6} key={index}>
                <Card
                  component={motion.div}
                  whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
                  sx={{ height: '100%' }}
                >
                  <CardContent>
                    <Stack direction="row" spacing={2} alignItems="flex-start">
                      <Avatar
                        sx={{
                          bgcolor: feature.color,
                          width: 48,
                          height: 48
                        }}
                      >
                        {feature.icon}
                      </Avatar>
                      <Box>
                        <Typography variant="h6" gutterBottom>
                          {feature.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {feature.description}
                        </Typography>
                      </Box>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </motion.div>

        {/* System Benefits */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Paper
            sx={{
              p: 4,
              mb: 6,
              background: `linear-gradient(135deg, ${theme.palette.primary.main}08, ${theme.palette.secondary.main}08)`
            }}
          >
            <Typography variant="h4" gutterBottom sx={{ mb: 3, fontWeight: 600, textAlign: 'center' }}>
              Why Choose A-ReALM?
            </Typography>
            <Grid container spacing={4}>
              <Grid item xs={12} md={4}>
                <Box textAlign="center">
                  <Avatar sx={{ bgcolor: 'primary.main', mx: 'auto', mb: 2, width: 64, height: 64 }}>
                    <Speed />
                  </Avatar>
                  <Typography variant="h6" gutterBottom>
                    Increased Efficiency
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Automate routine tasks and streamline operations to save time and reduce errors.
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box textAlign="center">
                  <Avatar sx={{ bgcolor: 'secondary.main', mx: 'auto', mb: 2, width: 64, height: 64 }}>
                    <CloudSync />
                  </Avatar>
                  <Typography variant="h6" gutterBottom>
                    Real-time Sync
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Keep all stakeholders updated with real-time data synchronization across the platform.
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box textAlign="center">
                  <Avatar sx={{ bgcolor: 'success.main', mx: 'auto', mb: 2, width: 64, height: 64 }}>
                    <Security />
                  </Avatar>
                  <Typography variant="h6" gutterBottom>
                    Enterprise Security
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Bank-level security with role-based access control and audit trails.
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </motion.div>
      </Container>
    </Box>
  );
};

export default Home;
