import {
  Box,
  Container,
  Typography,
  But<PERSON>,
  <PERSON>ack,
  Card,
  CardContent,
  Grid,
  Alert,
  Tabs,
  Tab,
  Fab,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip
} from '@mui/material';
import {
  Add as AddIcon,
  Assignment as AssignmentIcon,
  AssignmentReturn as AssignmentReturnIcon,
  Visibility as VisibilityIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { STRAgreement, CustomerReturnRequest, ClosurePermissions } from '../types/str';
import { getClosurePermissions, getClosureStatusColor } from '../utils/strPermissions';
import ClosureWorkspace from '../components/closure/ClosureWorkspace';
import CustomerReturnWizard from '../components/closure/CustomerReturnWizard';

// Mock data for development
const mockReturnRequests: CustomerReturnRequest[] = [
  {
    id: 'RET-001',
    agreementId: 'STR-001',
    customerId: 'CUST-001',
    proposedReturnDate: '2024-01-20',
    proposedReturnTime: '14:00',
    returnLocation: 'Dubai Airport Terminal 1',
    estimatedOdometer: 52500,
    estimatedFuelLevel: 75,
    reportedDamages: ['Minor scratch on rear bumper'],
    returnPhotos: ['photo1.jpg', 'photo2.jpg'],
    returnVideos: [],
    customerNotes: 'Vehicle in good condition overall',
    termsAccepted: true,
    declarationSigned: true,
    status: 'SUBMITTED',
    submittedAt: '2024-01-19T10:30:00Z',
    estimatedCharges: {
      fuelTopUp: 50,
      mileageOverage: 0,
      estimatedTotal: 50
    }
  },
  {
    id: 'RET-002',
    agreementId: 'STR-002',
    customerId: 'CUST-002',
    proposedReturnDate: '2024-01-21',
    proposedReturnTime: '16:00',
    returnLocation: 'Dubai Mall',
    estimatedOdometer: 51800,
    estimatedFuelLevel: 90,
    reportedDamages: [],
    returnPhotos: ['photo3.jpg'],
    returnVideos: ['video1.mp4'],
    customerNotes: '',
    termsAccepted: true,
    declarationSigned: true,
    status: 'UNDER_REVIEW',
    submittedAt: '2024-01-20T08:15:00Z',
    processedBy: 'agent-001',
    estimatedCharges: {
      fuelTopUp: 0,
      mileageOverage: 0,
      estimatedTotal: 0
    }
  }
];

interface ClosureNewProps {
  userRoles: string[];
  token: string;
}

export default function ClosureNew({ userRoles, token }: ClosureNewProps) {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [currentTab, setCurrentTab] = useState(0);
  const [showClosureWorkspace, setShowClosureWorkspace] = useState(false);
  const [showReturnWizard, setShowReturnWizard] = useState(false);
  const [selectedAgreementId, setSelectedAgreementId] = useState<string>('');
  const [returnRequests, setReturnRequests] = useState<CustomerReturnRequest[]>(mockReturnRequests);
  const [loading, setLoading] = useState(false);

  const permissions = getClosurePermissions(userRoles);
  const isAgent = permissions.canInitiateClosure;
  const isCustomer = !isAgent;

  useEffect(() => {
    // Check if we should open closure workspace directly
    const agreementId = searchParams.get('agreementId');
    if (agreementId && isAgent) {
      setSelectedAgreementId(agreementId);
      setShowClosureWorkspace(true);
    }
    
    loadReturnRequests();
  }, [searchParams, isAgent]);

  const loadReturnRequests = async () => {
    setLoading(true);
    try {
      // TODO: Implement API call based on user role
      // if (isAgent) {
      //   const response = await api.getAllReturnRequests(token);
      //   setReturnRequests(response);
      // } else {
      //   const response = await api.getCustomerReturnRequests(token);
      //   setReturnRequests(response);
      // }
      
      // For now, use mock data
      setReturnRequests(mockReturnRequests);
    } catch (error) {
      console.error('Failed to load return requests:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStartClosure = (agreementId?: string) => {
    if (permissions.canInitiateClosure) {
      setSelectedAgreementId(agreementId || '');
      setShowClosureWorkspace(true);
    }
  };

  const handleStartReturn = () => {
    setShowReturnWizard(true);
  };

  const handleClosureComplete = (closedAgreement: STRAgreement) => {
    setShowClosureWorkspace(false);
    // Navigate to agreement detail or show success message
    navigate(`/str/${closedAgreement.id}`);
  };

  const handleClosureCancel = () => {
    setShowClosureWorkspace(false);
    setSelectedAgreementId('');
  };

  const handleReturnComplete = (returnRequest: CustomerReturnRequest) => {
    setShowReturnWizard(false);
    // Add to return requests list
    setReturnRequests(prev => [returnRequest, ...prev]);
    // Show success message or navigate
  };

  const handleReturnCancel = () => {
    setShowReturnWizard(false);
  };

  const handleProcessReturn = (returnRequest: CustomerReturnRequest) => {
    if (permissions.canInitiateClosure) {
      setSelectedAgreementId(returnRequest.agreementId);
      setShowClosureWorkspace(true);
    }
  };

  const renderAgentDashboard = () => (
    <Box>
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Closure Management Console
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Process vehicle returns and close agreements
          </Typography>
        </Box>
        
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleStartClosure()}
          size="large"
        >
          Start Closure Process
        </Button>
      </Stack>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <AssignmentReturnIcon color="primary" />
                <Box>
                  <Typography variant="h6">{returnRequests.length}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Return Requests
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <AssignmentIcon color="warning" />
                <Box>
                  <Typography variant="h6">
                    {returnRequests.filter(r => r.status === 'SUBMITTED').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Pending Review
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <CheckCircleIcon color="success" />
                <Box>
                  <Typography variant="h6">
                    {returnRequests.filter(r => r.status === 'COMPLETED').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Completed Today
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <AssignmentIcon color="info" />
                <Box>
                  <Typography variant="h6">
                    {returnRequests.filter(r => r.status === 'PROCESSING').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    In Progress
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card>
        <CardContent>
          <Tabs value={currentTab} onChange={(_, value) => setCurrentTab(value)} sx={{ mb: 3 }}>
            <Tab label="All Return Requests" />
            <Tab label="Pending Review" />
            <Tab label="In Progress" />
            <Tab label="Completed" />
          </Tabs>
          
          <Typography variant="h6" gutterBottom>
            Return Requests
          </Typography>
          
          {returnRequests.length === 0 ? (
            <Alert severity="info">
              No return requests found.
            </Alert>
          ) : (
            <List>
              {returnRequests.map((request) => (
                <ListItem key={request.id} divider>
                  <ListItemText
                    primary={
                      <Stack direction="row" spacing={2} alignItems="center">
                        <Typography variant="subtitle1">
                          Agreement {request.agreementId}
                        </Typography>
                        <Chip
                          label={request.status}
                          color={getClosureStatusColor(request.status)}
                          size="small"
                        />
                      </Stack>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2">
                          Return: {request.proposedReturnDate} at {request.proposedReturnTime}
                        </Typography>
                        <Typography variant="body2">
                          Location: {request.returnLocation}
                        </Typography>
                        <Typography variant="body2">
                          Submitted: {new Date(request.submittedAt).toLocaleString()}
                        </Typography>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Stack direction="row" spacing={1}>
                      <IconButton
                        onClick={() => {/* View details */}}
                        size="small"
                      >
                        <VisibilityIcon />
                      </IconButton>
                      {request.status === 'SUBMITTED' && (
                        <Button
                          variant="contained"
                          size="small"
                          onClick={() => handleProcessReturn(request)}
                        >
                          Process
                        </Button>
                      )}
                    </Stack>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}
        </CardContent>
      </Card>
    </Box>
  );

  const renderCustomerPortal = () => (
    <Box>
      <Typography variant="h4" gutterBottom>
        Vehicle Return
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Submit a return request for your rental vehicle
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Start Vehicle Return
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Submit photos and details about your vehicle return. Our team will review and contact you to confirm the return process.
              </Typography>
              
              <Button
                variant="contained"
                startIcon={<AssignmentReturnIcon />}
                onClick={handleStartReturn}
                size="large"
              >
                Start Return Request
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Your Return Requests
              </Typography>
              
              {returnRequests.length === 0 ? (
                <Typography variant="body2" color="text.secondary">
                  No return requests yet.
                </Typography>
              ) : (
                <List dense>
                  {returnRequests.slice(0, 3).map((request) => (
                    <ListItem key={request.id} sx={{ px: 0 }}>
                      <ListItemText
                        primary={`Agreement ${request.agreementId}`}
                        secondary={
                          <Stack direction="row" spacing={1} alignItems="center">
                            <Chip
                              label={request.status}
                              color={getClosureStatusColor(request.status)}
                              size="small"
                            />
                            <Typography variant="caption">
                              {new Date(request.submittedAt).toLocaleDateString()}
                            </Typography>
                          </Stack>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  if (showClosureWorkspace) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <ClosureWorkspace
          agreementId={selectedAgreementId}
          userRoles={userRoles}
          token={token}
          onComplete={handleClosureComplete}
          onCancel={handleClosureCancel}
        />
      </Container>
    );
  }

  if (showReturnWizard) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <CustomerReturnWizard
          agreementId="STR-001" // TODO: Get from context or selection
          customerId="CUST-001" // TODO: Get from user context
          token={token}
          onComplete={handleReturnComplete}
          onCancel={handleReturnCancel}
        />
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {isAgent ? renderAgentDashboard() : renderCustomerPortal()}
      
      {/* Floating Action Button for Quick Actions */}
      {isAgent && (
        <Fab
          color="primary"
          aria-label="start closure"
          onClick={() => handleStartClosure()}
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
          }}
        >
          <AddIcon />
        </Fab>
      )}
    </Container>
  );
}
