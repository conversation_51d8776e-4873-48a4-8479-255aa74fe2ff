import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import Login from '../Login';
import { api } from '../../api';

// Mock the API
jest.mock('../../api', () => ({
  api: {
    login: jest.fn(),
  },
}));

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

// Mock react-router-dom hooks
const mockNavigate = jest.fn();
const mockLocation = { state: null };

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => mockLocation,
}));

const theme = createTheme();

const renderLogin = (onAuth = jest.fn()) => {
  return render(
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <Login onAuth={onAuth} />
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('Login Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  describe('Rendering', () => {
    it('renders login form with all required elements', () => {
      renderLogin();
      
      expect(screen.getByText('A‑ReALM')).toBeInTheDocument();
      expect(screen.getByText('Welcome Back')).toBeInTheDocument();
      expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
      expect(screen.getByRole('checkbox', { name: /remember me/i })).toBeInTheDocument();
      expect(screen.getByText(/forgot password/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
    });

    it('renders quick access buttons', () => {
      renderLogin();
      
      expect(screen.getByRole('button', { name: /demo user/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /admin user/i })).toBeInTheDocument();
    });

    it('renders back navigation button', () => {
      renderLogin();
      
      expect(screen.getByText(/back to previous page/i)).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('shows error when username is empty', async () => {
      renderLogin();
      
      const usernameInput = screen.getByLabelText(/username/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });
      
      fireEvent.change(usernameInput, { target: { value: '' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/username is required/i)).toBeInTheDocument();
      });
    });

    it('shows error when username is too short', async () => {
      renderLogin();
      
      const usernameInput = screen.getByLabelText(/username/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });
      
      fireEvent.change(usernameInput, { target: { value: 'a' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/username must be at least 2 characters/i)).toBeInTheDocument();
      });
    });

    it('shows error when password is too short', async () => {
      renderLogin();
      
      const usernameInput = screen.getByLabelText(/username/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });
      
      fireEvent.change(usernameInput, { target: { value: 'admin' } });
      fireEvent.change(passwordInput, { target: { value: 'ab' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/password must be at least 3 characters/i)).toBeInTheDocument();
      });
    });

    it('clears field errors when user starts typing', async () => {
      renderLogin();
      
      const usernameInput = screen.getByLabelText(/username/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });
      
      // Trigger validation error
      fireEvent.change(usernameInput, { target: { value: '' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/username is required/i)).toBeInTheDocument();
      });
      
      // Start typing to clear error
      fireEvent.change(usernameInput, { target: { value: 'a' } });
      
      await waitFor(() => {
        expect(screen.queryByText(/username is required/i)).not.toBeInTheDocument();
      });
    });
  });

  describe('Form Interactions', () => {
    it('toggles password visibility', () => {
      renderLogin();
      
      const passwordInput = screen.getByLabelText(/password/i) as HTMLInputElement;
      const toggleButton = screen.getByRole('button', { name: '' }); // Password toggle button
      
      expect(passwordInput.type).toBe('password');
      
      fireEvent.click(toggleButton);
      expect(passwordInput.type).toBe('text');
      
      fireEvent.click(toggleButton);
      expect(passwordInput.type).toBe('password');
    });

    it('updates remember me checkbox', () => {
      renderLogin();
      
      const checkbox = screen.getByRole('checkbox', { name: /remember me/i }) as HTMLInputElement;
      
      expect(checkbox.checked).toBe(false);
      
      fireEvent.click(checkbox);
      expect(checkbox.checked).toBe(true);
    });

    it('sets demo user when demo button is clicked', () => {
      renderLogin();
      
      const usernameInput = screen.getByLabelText(/username/i) as HTMLInputElement;
      const demoButton = screen.getByRole('button', { name: /demo user/i });
      
      fireEvent.click(demoButton);
      
      expect(usernameInput.value).toBe('demo');
    });

    it('sets admin user when admin button is clicked', () => {
      renderLogin();
      
      const usernameInput = screen.getByLabelText(/username/i) as HTMLInputElement;
      const adminButton = screen.getByRole('button', { name: /admin user/i });
      
      fireEvent.click(adminButton);
      
      expect(usernameInput.value).toBe('admin');
    });
  });

  describe('API Integration', () => {
    it('calls API with correct username on successful login', async () => {
      const mockOnAuth = jest.fn();
      const mockApiResponse = { accessToken: 'test-token' };
      (api.login as jest.Mock).mockResolvedValue(mockApiResponse);
      
      renderLogin(mockOnAuth);
      
      const usernameInput = screen.getByLabelText(/username/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });
      
      fireEvent.change(usernameInput, { target: { value: 'admin' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(api.login).toHaveBeenCalledWith('admin');
        expect(mockOnAuth).toHaveBeenCalledWith('test-token');
      });
    });

    it('shows error message on API failure', async () => {
      const mockError = new Error('Invalid credentials');
      (api.login as jest.Mock).mockRejectedValue(mockError);
      
      renderLogin();
      
      const usernameInput = screen.getByLabelText(/username/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });
      
      fireEvent.change(usernameInput, { target: { value: 'admin' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument();
      });
    });

    it('shows loading state during API call', async () => {
      let resolvePromise: (value: any) => void;
      const mockPromise = new Promise((resolve) => {
        resolvePromise = resolve;
      });
      (api.login as jest.Mock).mockReturnValue(mockPromise);
      
      renderLogin();
      
      const usernameInput = screen.getByLabelText(/username/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });
      
      fireEvent.change(usernameInput, { target: { value: 'admin' } });
      fireEvent.click(submitButton);
      
      // Check loading state
      expect(screen.getByText(/signing in/i)).toBeInTheDocument();
      expect(submitButton).toBeDisabled();
      
      // Resolve the promise
      resolvePromise!({ accessToken: 'test-token' });
      
      await waitFor(() => {
        expect(screen.queryByText(/signing in/i)).not.toBeInTheDocument();
      });
    });
  });

  describe('Remember Me Functionality', () => {
    it('saves token to localStorage when remember me is checked', async () => {
      const mockOnAuth = jest.fn();
      const mockApiResponse = { accessToken: 'test-token' };
      (api.login as jest.Mock).mockResolvedValue(mockApiResponse);
      
      renderLogin(mockOnAuth);
      
      const usernameInput = screen.getByLabelText(/username/i);
      const rememberCheckbox = screen.getByRole('checkbox', { name: /remember me/i });
      const submitButton = screen.getByRole('button', { name: /sign in/i });
      
      fireEvent.change(usernameInput, { target: { value: 'admin' } });
      fireEvent.click(rememberCheckbox);
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(localStorage.getItem('authToken')).toBe('test-token');
      });
    });

    it('does not save token to localStorage when remember me is not checked', async () => {
      const mockOnAuth = jest.fn();
      const mockApiResponse = { accessToken: 'test-token' };
      (api.login as jest.Mock).mockResolvedValue(mockApiResponse);
      
      renderLogin(mockOnAuth);
      
      const usernameInput = screen.getByLabelText(/username/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });
      
      fireEvent.change(usernameInput, { target: { value: 'admin' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(localStorage.getItem('authToken')).toBeNull();
      });
    });
  });

  describe('Navigation', () => {
    it('navigates back when back button is clicked', () => {
      renderLogin();
      
      const backButton = screen.getByRole('button', { name: '' }); // Back arrow button
      fireEvent.click(backButton);
      
      expect(mockNavigate).toHaveBeenCalledWith(-1);
    });

    it('redirects to home page after successful login', async () => {
      const mockOnAuth = jest.fn();
      const mockApiResponse = { accessToken: 'test-token' };
      (api.login as jest.Mock).mockResolvedValue(mockApiResponse);
      
      renderLogin(mockOnAuth);
      
      const usernameInput = screen.getByLabelText(/username/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });
      
      fireEvent.change(usernameInput, { target: { value: 'admin' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/', { replace: true });
      });
    });

    it('redirects to intended page when coming from protected route', async () => {
      // Mock location state with intended destination
      mockLocation.state = { from: { pathname: '/admin/billing' } };
      
      const mockOnAuth = jest.fn();
      const mockApiResponse = { accessToken: 'test-token' };
      (api.login as jest.Mock).mockResolvedValue(mockApiResponse);
      
      renderLogin(mockOnAuth);
      
      const usernameInput = screen.getByLabelText(/username/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });
      
      fireEvent.change(usernameInput, { target: { value: 'admin' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/admin/billing', { replace: true });
      });
    });
  });

  describe('Auto-login from localStorage', () => {
    it('auto-logs in user if token exists in localStorage', () => {
      const mockOnAuth = jest.fn();
      localStorage.setItem('authToken', 'existing-token');
      
      renderLogin(mockOnAuth);
      
      expect(mockOnAuth).toHaveBeenCalledWith('existing-token');
      expect(mockNavigate).toHaveBeenCalledWith('/', { replace: true });
    });
  });
});
