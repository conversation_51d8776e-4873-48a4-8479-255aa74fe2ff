import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import Home from '../Home';
import theme from '../../theme';

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        {component}
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('Home Component', () => {
  describe('when user is not logged in', () => {
    it('renders welcome message and get started button', () => {
      renderWithProviders(<Home token={null} roles={[]} />);
      
      expect(screen.getByText('A-ReALM')).toBeInTheDocument();
      expect(screen.getByText('Automotive Rental and Lease Management System')).toBeInTheDocument();
      expect(screen.getByText('Get Started')).toBeInTheDocument();
      expect(screen.queryByText('Go to Dashboard')).not.toBeInTheDocument();
    });

    it('shows login button in navigation', () => {
      renderWithProviders(<Home token={null} roles={[]} />);
      
      // The login button should be visible for non-authenticated users
      expect(screen.getByText('Get Started')).toBeInTheDocument();
    });

    it('does not show admin-only quick actions', () => {
      renderWithProviders(<Home token={null} roles={[]} />);
      
      // Admin-only actions should be disabled/restricted
      const billingCard = screen.getByText('Billing & Payments').closest('.MuiCard-root');
      const fleetCard = screen.getByText('Fleet Analytics').closest('.MuiCard-root');
      
      expect(billingCard).toHaveStyle('opacity: 0.6');
      expect(fleetCard).toHaveStyle('opacity: 0.6');
    });
  });

  describe('when user is logged in as regular user', () => {
    it('renders dashboard button instead of get started', () => {
      renderWithProviders(<Home token="valid-token" roles={['user']} />);
      
      expect(screen.getByText('Go to Dashboard')).toBeInTheDocument();
      expect(screen.queryByText('Get Started')).not.toBeInTheDocument();
    });

    it('shows welcome back message', () => {
      renderWithProviders(<Home token="valid-token" roles={['user']} />);
      
      expect(screen.getByText('Welcome back!')).toBeInTheDocument();
      expect(screen.getByText('You\'re logged in as: user')).toBeInTheDocument();
    });

    it('enables user-accessible quick actions', () => {
      renderWithProviders(<Home token="valid-token" roles={['user']} />);
      
      // User actions should be enabled
      const strCard = screen.getByText('Create STR Agreement').closest('.MuiCard-root');
      const closureCard = screen.getByText('Process Agreement Closure').closest('.MuiCard-root');
      
      expect(strCard).toHaveStyle('opacity: 1');
      expect(closureCard).toHaveStyle('opacity: 1');
    });
  });

  describe('when user is logged in as admin', () => {
    it('shows admin role in welcome message', () => {
      renderWithProviders(<Home token="admin-token" roles={['admin', 'user']} />);
      
      expect(screen.getByText('You\'re logged in as: admin, user')).toBeInTheDocument();
      expect(screen.getByText('2 roles')).toBeInTheDocument();
    });

    it('enables all quick actions including admin-only', () => {
      renderWithProviders(<Home token="admin-token" roles={['admin']} />);
      
      // All actions should be enabled for admin
      const billingCard = screen.getByText('Billing & Payments').closest('.MuiCard-root');
      const fleetCard = screen.getByText('Fleet Analytics').closest('.MuiCard-root');
      
      expect(billingCard).toHaveStyle('opacity: 1');
      expect(fleetCard).toHaveStyle('opacity: 1');
    });
  });

  describe('features section', () => {
    it('renders all platform features', () => {
      renderWithProviders(<Home token={null} roles={[]} />);
      
      expect(screen.getByText('Platform Features')).toBeInTheDocument();
      expect(screen.getByText('Fleet Management')).toBeInTheDocument();
      expect(screen.getByText('Agreement Processing')).toBeInTheDocument();
      expect(screen.getByText('Financial Operations')).toBeInTheDocument();
      expect(screen.getByText('Business Intelligence')).toBeInTheDocument();
    });
  });

  describe('statistics section', () => {
    it('renders system statistics', () => {
      renderWithProviders(<Home token={null} roles={[]} />);
      
      expect(screen.getByText('150+')).toBeInTheDocument();
      expect(screen.getByText('Active Vehicles')).toBeInTheDocument();
      expect(screen.getByText('500+')).toBeInTheDocument();
      expect(screen.getByText('Monthly Agreements')).toBeInTheDocument();
      expect(screen.getByText('$2M+')).toBeInTheDocument();
      expect(screen.getByText('Revenue Processed')).toBeInTheDocument();
      expect(screen.getByText('98%')).toBeInTheDocument();
      expect(screen.getByText('Customer Satisfaction')).toBeInTheDocument();
    });
  });

  describe('benefits section', () => {
    it('renders why choose A-ReALM section', () => {
      renderWithProviders(<Home token={null} roles={[]} />);
      
      expect(screen.getByText('Why Choose A-ReALM?')).toBeInTheDocument();
      expect(screen.getByText('Increased Efficiency')).toBeInTheDocument();
      expect(screen.getByText('Real-time Sync')).toBeInTheDocument();
      expect(screen.getByText('Enterprise Security')).toBeInTheDocument();
    });
  });

  describe('navigation', () => {
    it('has working links to different sections', () => {
      renderWithProviders(<Home token="valid-token" roles={['user']} />);
      
      // Check that links are present and have correct href attributes
      const dashboardLink = screen.getByText('Go to Dashboard').closest('a');
      expect(dashboardLink).toHaveAttribute('href', '/str');
    });
  });

  describe('responsive design', () => {
    it('renders without crashing on different screen sizes', () => {
      // Test mobile view
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });
      
      renderWithProviders(<Home token={null} roles={[]} />);
      expect(screen.getByText('A-ReALM')).toBeInTheDocument();
      
      // Test desktop view
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1200,
      });
      
      renderWithProviders(<Home token={null} roles={[]} />);
      expect(screen.getByText('A-ReALM')).toBeInTheDocument();
    });
  });
});
