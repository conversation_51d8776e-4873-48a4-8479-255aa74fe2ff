import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import Register from '../Register';

// Mock the API
global.fetch = jest.fn();

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

// Mock react-router-dom hooks
const mockNavigate = jest.fn();
const mockLocation = { state: null };

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => mockLocation,
}));

const theme = createTheme();

const renderRegister = (onAuth = jest.fn()) => {
  return render(
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <Register onAuth={onAuth} />
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('Register Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    (global.fetch as jest.Mock).mockClear();
  });

  describe('Rendering', () => {
    it('renders registration form with all required elements', () => {
      renderRegister();
      
      expect(screen.getByText('A‑ReALM')).toBeInTheDocument();
      expect(screen.getByText('Create Account')).toBeInTheDocument();
      expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/account type/i)).toBeInTheDocument();
      expect(screen.getByRole('checkbox')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument();
    });

    it('renders role selection dropdown', () => {
      renderRegister();
      
      expect(screen.getByLabelText(/account type/i)).toBeInTheDocument();
    });

    it('renders terms and conditions links', () => {
      renderRegister();
      
      expect(screen.getByText(/terms of service/i)).toBeInTheDocument();
      expect(screen.getByText(/privacy policy/i)).toBeInTheDocument();
    });

    it('renders back navigation button', () => {
      renderRegister();
      
      expect(screen.getByText(/back to previous page/i)).toBeInTheDocument();
    });

    it('renders sign in link', () => {
      renderRegister();
      
      expect(screen.getByRole('link', { name: /sign in instead/i })).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('shows error when username is empty', async () => {
      renderRegister();
      
      const submitButton = screen.getByRole('button', { name: /create account/i });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/username is required/i)).toBeInTheDocument();
      });
    });

    it('shows error when username is too short', async () => {
      renderRegister();
      
      const usernameInput = screen.getByLabelText(/username/i);
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      fireEvent.change(usernameInput, { target: { value: 'ab' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/username must be at least 3 characters/i)).toBeInTheDocument();
      });
    });

    it('shows error when username contains invalid characters', async () => {
      renderRegister();
      
      const usernameInput = screen.getByLabelText(/username/i);
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      fireEvent.change(usernameInput, { target: { value: 'user@name' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/username can only contain letters, numbers, hyphens, and underscores/i)).toBeInTheDocument();
      });
    });

    it('shows error when email is empty', async () => {
      renderRegister();
      
      const submitButton = screen.getByRole('button', { name: /create account/i });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      });
    });

    it('shows error when email is invalid', async () => {
      renderRegister();
      
      const emailInput = screen.getByLabelText(/email address/i);
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument();
      });
    });

    it('shows error when password is empty', async () => {
      renderRegister();
      
      const submitButton = screen.getByRole('button', { name: /create account/i });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/password is required/i)).toBeInTheDocument();
      });
    });

    it('shows error when password is too weak', async () => {
      renderRegister();
      
      const passwordInput = screen.getByLabelText(/^password$/i);
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      fireEvent.change(passwordInput, { target: { value: 'weak' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/password must contain at least one uppercase letter, one lowercase letter, and one number/i)).toBeInTheDocument();
      });
    });

    it('shows error when passwords do not match', async () => {
      renderRegister();
      
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      fireEvent.change(passwordInput, { target: { value: 'Password123' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'Different123' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument();
      });
    });

    it('shows error when terms are not agreed', async () => {
      renderRegister();
      
      const usernameInput = screen.getByLabelText(/username/i);
      const emailInput = screen.getByLabelText(/email address/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      fireEvent.change(usernameInput, { target: { value: 'testuser' } });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'Password123' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'Password123' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/you must agree to the terms of service and privacy policy/i)).toBeInTheDocument();
      });
    });

    it('clears field errors when user starts typing', async () => {
      renderRegister();
      
      const usernameInput = screen.getByLabelText(/username/i);
      const submitButton = screen.getByRole('button', { name: /create account/i });
      
      // Trigger validation error
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText(/username is required/i)).toBeInTheDocument();
      });
      
      // Start typing to clear error
      fireEvent.change(usernameInput, { target: { value: 'a' } });
      
      await waitFor(() => {
        expect(screen.queryByText(/username is required/i)).not.toBeInTheDocument();
      });
    });
  });

  describe('Form Interactions', () => {
    it('toggles password visibility', () => {
      renderRegister();
      
      const passwordInput = screen.getByLabelText(/^password$/i) as HTMLInputElement;
      const toggleButtons = screen.getAllByRole('button', { name: '' }); // Password toggle buttons
      const passwordToggle = toggleButtons.find(btn => 
        btn.closest('.MuiTextField-root')?.querySelector('label')?.textContent === 'Password'
      );
      
      expect(passwordInput.type).toBe('password');
      
      if (passwordToggle) {
        fireEvent.click(passwordToggle);
        expect(passwordInput.type).toBe('text');
        
        fireEvent.click(passwordToggle);
        expect(passwordInput.type).toBe('password');
      }
    });

    it('updates role selection', () => {
      renderRegister();
      
      const roleSelect = screen.getByLabelText(/account type/i);
      
      // Default should be 'user'
      expect(roleSelect).toHaveDisplayValue('Regular User');
    });

    it('updates terms agreement checkbox', () => {
      renderRegister();
      
      const checkbox = screen.getByRole('checkbox') as HTMLInputElement;
      
      expect(checkbox.checked).toBe(false);
      
      fireEvent.click(checkbox);
      expect(checkbox.checked).toBe(true);
    });
  });

  describe('Registration Flow', () => {
    it('successfully registers a user account', async () => {
      const mockOnAuth = jest.fn();
      const mockResponse = { accessToken: 'test-token', refreshToken: 'refresh-token' };
      
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });
      
      renderRegister(mockOnAuth);
      
      // Fill out form
      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'testuser' } });
      fireEvent.change(screen.getByLabelText(/email address/i), { target: { value: '<EMAIL>' } });
      fireEvent.change(screen.getByLabelText(/^password$/i), { target: { value: 'Password123' } });
      fireEvent.change(screen.getByLabelText(/confirm password/i), { target: { value: 'Password123' } });
      fireEvent.click(screen.getByRole('checkbox'));
      
      // Submit form
      fireEvent.click(screen.getByRole('button', { name: /create account/i }));
      
      // Check loading state
      expect(screen.getByText(/creating account/i)).toBeInTheDocument();
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ username: 'testuser' }),
        });
      });
      
      await waitFor(() => {
        expect(mockOnAuth).toHaveBeenCalledWith('test-token');
      });
    });

    it('successfully registers an admin account', async () => {
      const mockOnAuth = jest.fn();
      const mockResponse = { accessToken: 'admin-token', refreshToken: 'refresh-token' };
      
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });
      
      renderRegister(mockOnAuth);
      
      // Fill out form with admin role
      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'adminuser' } });
      fireEvent.change(screen.getByLabelText(/email address/i), { target: { value: '<EMAIL>' } });
      fireEvent.change(screen.getByLabelText(/^password$/i), { target: { value: 'Password123' } });
      fireEvent.change(screen.getByLabelText(/confirm password/i), { target: { value: 'Password123' } });
      
      // Select admin role
      const roleSelect = screen.getByLabelText(/account type/i);
      fireEvent.mouseDown(roleSelect);
      fireEvent.click(screen.getByText('Administrator'));
      
      fireEvent.click(screen.getByRole('checkbox'));
      
      // Submit form
      fireEvent.click(screen.getByRole('button', { name: /create account/i }));
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ username: 'adminuser-admin' }),
        });
      });
    });

    it('shows error message on registration failure', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Registration failed'));
      
      renderRegister();
      
      // Fill out valid form
      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'testuser' } });
      fireEvent.change(screen.getByLabelText(/email address/i), { target: { value: '<EMAIL>' } });
      fireEvent.change(screen.getByLabelText(/^password$/i), { target: { value: 'Password123' } });
      fireEvent.change(screen.getByLabelText(/confirm password/i), { target: { value: 'Password123' } });
      fireEvent.click(screen.getByRole('checkbox'));
      
      // Submit form
      fireEvent.click(screen.getByRole('button', { name: /create account/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/registration failed/i)).toBeInTheDocument();
      });
    });
  });

  describe('Navigation', () => {
    it('navigates back when back button is clicked', () => {
      renderRegister();
      
      const backButton = screen.getByRole('button', { name: '' }); // Back arrow button
      fireEvent.click(backButton);
      
      expect(mockNavigate).toHaveBeenCalledWith(-1);
    });

    it('redirects to home page after successful registration', async () => {
      const mockOnAuth = jest.fn();
      const mockResponse = { accessToken: 'test-token', refreshToken: 'refresh-token' };
      
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });
      
      renderRegister(mockOnAuth);
      
      // Fill out and submit form
      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'testuser' } });
      fireEvent.change(screen.getByLabelText(/email address/i), { target: { value: '<EMAIL>' } });
      fireEvent.change(screen.getByLabelText(/^password$/i), { target: { value: 'Password123' } });
      fireEvent.change(screen.getByLabelText(/confirm password/i), { target: { value: 'Password123' } });
      fireEvent.click(screen.getByRole('checkbox'));
      fireEvent.click(screen.getByRole('button', { name: /create account/i }));
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/', { replace: true });
      });
    });

    it('redirects to intended page when coming from protected route', async () => {
      // Mock location state with intended destination
      mockLocation.state = { from: { pathname: '/admin/billing' } };
      
      const mockOnAuth = jest.fn();
      const mockResponse = { accessToken: 'test-token', refreshToken: 'refresh-token' };
      
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });
      
      renderRegister(mockOnAuth);
      
      // Fill out and submit form
      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'testuser' } });
      fireEvent.change(screen.getByLabelText(/email address/i), { target: { value: '<EMAIL>' } });
      fireEvent.change(screen.getByLabelText(/^password$/i), { target: { value: 'Password123' } });
      fireEvent.change(screen.getByLabelText(/confirm password/i), { target: { value: 'Password123' } });
      fireEvent.click(screen.getByRole('checkbox'));
      fireEvent.click(screen.getByRole('button', { name: /create account/i }));
      
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/admin/billing', { replace: true });
      });
    });
  });

  describe('Auto-redirect for authenticated users', () => {
    it('redirects authenticated users away from registration page', () => {
      const mockOnAuth = jest.fn();
      localStorage.setItem('authToken', 'existing-token');
      
      renderRegister(mockOnAuth);
      
      expect(mockNavigate).toHaveBeenCalledWith('/', { replace: true });
    });
  });
});
