import { Box, Button, Stack, TextField, Typography, MenuItem, Card, CardHeader, CardContent, Grid, Alert, Divider } from '@mui/material';
import { useState } from 'react';
import { api } from '../api';

export default function STR({ token }: { token: string | null }) {
  const [customerId, setCustomerId] = useState<string>('');
  const [vehicleId, setVehicleId] = useState<string>('');
  const [startDate, setStartDate] = useState<string>(new Date().toISOString());
  const [endDate, setEndDate] = useState<string>(new Date(Date.now() + 24*60*60*1000).toISOString());
  const [agreementId, setAgreementId] = useState<string>('');
  const [pricing, setPricing] = useState<any | null>(null);

  // Handover (on-hire) details
  const [odometerReading, setOdometerReading] = useState<number>(0);
  const [fuelLevel, setFuelLevel] = useState<number>(100);
  const [overallRating, setOverallRating] = useState<'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR'>('EXCELLENT');
  const [handoverNotes, setHandoverNotes] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  const disabled = !token;

  async function createCustomer() {
    setError(null);
    try {
      const res = await api.createCustomer(token!, { type: 'CASH', name: `Customer ${Date.now()}` });
      setCustomerId(res.id);
    } catch (e: any) {
      setError(e.message);
    }
  }

  async function createVehicle() {
    setError(null);
    try {
      const res = await api.createVehicle(token!, { plateNumber: `P-${Math.floor(Math.random()*10000)}` });
      setVehicleId(res.id);
    } catch (e: any) {
      setError(e.message);
    }
  }

  async function createAgreement() {
    setError(null);
    try {
      const res = await api.createAgreementSTR(token!, { customerId, vehicleId, startDate, endDate });
      const agrId = res?.agreement?.id || res?.id || '';
      setAgreementId(agrId);
      setPricing(res?.pricing || null);
      try {
        localStorage.setItem('a_realm_last_agreement', JSON.stringify({ id: agrId, vehicleId, customerId }));
      } catch {}
    } catch (e: any) {
      setError(e.message);
    }
  }

  async function onHire() {
    setError(null);
    try {
      await api.onHireAgreement(token!, agreementId, {
        vehicleId,
        customerId,
        handoverDate: startDate,
        odometerReading,
        fuelLevel,
        overallRating,
        documentsProvided: [],
        handoverNotes
      });
    } catch (e: any) {
      setError(e.message);
    }
  }

  return (
    <Card>
      <CardHeader title="Short Term Rental (STR)" subheader="Create a demo customer & vehicle, then build an STR agreement" />
      <CardContent>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
              <Button variant="outlined" onClick={createCustomer} disabled={disabled}>Create Demo Customer</Button>
              <TextField label="Customer ID" value={customerId} size="small" fullWidth onChange={(e)=>setCustomerId(e.target.value)} />
            </Stack>
          </Grid>
          <Grid item xs={12} md={6}>
            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
              <Button variant="outlined" onClick={createVehicle} disabled={disabled}>Create Demo Vehicle</Button>
              <TextField label="Vehicle ID" value={vehicleId} size="small" fullWidth onChange={(e)=>setVehicleId(e.target.value)} />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField label="Start Date" value={startDate} onChange={(e)=>setStartDate(e.target.value)} fullWidth />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField label="End Date" value={endDate} onChange={(e)=>setEndDate(e.target.value)} fullWidth />
          </Grid>

          <Grid item xs={12}><Divider textAlign="left">Handover Details</Divider></Grid>
          <Grid item xs={12} sm={4}>
            <TextField type="number" label="Odometer" value={odometerReading}
              onChange={(e)=>setOdometerReading(Number(e.target.value))} inputProps={{ min: 0 }} fullWidth />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField type="number" label="Fuel %" value={fuelLevel}
              onChange={(e)=>setFuelLevel(Number(e.target.value))} inputProps={{ min: 0, max: 100, step: 1 }} fullWidth />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField select label="Condition" value={overallRating} onChange={(e)=>setOverallRating(e.target.value as any)} fullWidth>
              <MenuItem value="EXCELLENT">EXCELLENT</MenuItem>
              <MenuItem value="GOOD">GOOD</MenuItem>
              <MenuItem value="FAIR">FAIR</MenuItem>
              <MenuItem value="POOR">POOR</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12}>
            <TextField label="Handover Notes" value={handoverNotes} onChange={(e)=>setHandoverNotes(e.target.value)} fullWidth />
          </Grid>

          <Grid item xs={12}>
            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
              <Button variant="contained" onClick={createAgreement} disabled={disabled || !customerId || !vehicleId}>Create STR Agreement</Button>
              <Button variant="outlined" onClick={onHire} disabled={disabled || !agreementId || !customerId || !vehicleId}>On-Hire</Button>
            </Stack>
          </Grid>
          <Grid item xs={12}>
            <TextField label="Agreement ID" value={agreementId} size="small" fullWidth onChange={(e)=>setAgreementId(e.target.value)} />
          </Grid>
          {pricing && (
            <Grid item xs={12}>
              <PricingView pricing={pricing} />
            </Grid>
          )}
          {error && (
            <Grid item xs={12}><Alert severity="error">{error}</Alert></Grid>
          )}
        </Grid>
      </CardContent>
    </Card>
  );
}

function PricingView({ pricing }: { pricing: any }) {
  const { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper } = require('@mui/material');
  const fmt = (v: any) => {
    const n = typeof v === 'string' ? Number(v) : (v?.toNumber?.() ?? Number(v));
    if (Number.isNaN(n)) return String(v);
    try { return new Intl.NumberFormat(undefined, { style: 'currency', currency: 'AED', minimumFractionDigits: 2 }).format(n); } catch { return n.toFixed(2); }
  };
  const lines: any[] = Array.isArray(pricing?.breakdown) ? pricing.breakdown : [];
  return (
    <Box>
      <Typography variant="subtitle2" gutterBottom>Pricing</Typography>
      <Stack spacing={0.5}>
        <Typography variant="body2">Base: {fmt(pricing?.baseAmount)}</Typography>
        <Typography variant="body2">Additional: {fmt(pricing?.additionalCharges)}</Typography>
        <Typography variant="body2">Discounts: {fmt(pricing?.discounts)}</Typography>
        <Typography variant="body2">Taxes: {fmt(pricing?.taxes)}</Typography>
        <Typography variant="body2" sx={{ fontWeight: 600 }}>Total: {fmt(pricing?.totalAmount)}</Typography>
      </Stack>
      {lines.length > 0 && (
        <TableContainer component={Paper} sx={{ mt: 1 }}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Description</TableCell>
                <TableCell>Qty</TableCell>
                <TableCell>Unit</TableCell>
                <TableCell>Total</TableCell>
                <TableCell>Category</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {lines.map((li, idx) => (
                <TableRow key={idx}>
                  <TableCell>{li?.description || '-'}</TableCell>
                  <TableCell>{li?.quantity ?? '-'}</TableCell>
                  <TableCell>{fmt(li?.unitPrice)}</TableCell>
                  <TableCell>{fmt(li?.totalPrice)}</TableCell>
                  <TableCell>{li?.category || '-'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );
}
