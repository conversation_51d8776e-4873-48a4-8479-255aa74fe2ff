import { Box, Button, Stack, TextField, Typography, MenuItem, Card, CardHeader, CardContent, Grid, Alert, Divider } from '@mui/material';
import { useEffect, useState } from 'react';
import { api } from '../api';

export default function Closure({ token }: { token: string | null }) {
  const [agreementId, setAgreementId] = useState<string>('');
  const [endDate, setEndDate] = useState<string>(new Date().toISOString());
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [vehicleId, setVehicleId] = useState<string>('');
  const [odometerReading, setOdometerReading] = useState<number>(0);
  const [fuelLevel, setFuelLevel] = useState<number>(100);
  const [overallRating, setOverallRating] = useState<'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR'>('GOOD');
  const [returnNotes, setReturnNotes] = useState<string>('');

  const disabled = !token;

  useEffect(() => {
    try {
      const raw = localStorage.getItem('a_realm_last_agreement');
      if (raw) {
        const parsed = JSON.parse(raw);
        if (parsed?.id && !agreementId) setAgreementId(parsed.id);
        if (parsed?.vehicleId && !vehicleId) setVehicleId(parsed.vehicleId);
      }
    } catch {}
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  async function closeAgreement() {
    setError(null); setResult(null);
    try {
      const res = await api.closeAgreement(token!, agreementId, {
        endDate,
        vehicleId,
        odometerReading,
        fuelLevel,
        overallRating,
        returnNotes
      });
      setResult(res);
    } catch (e: any) {
      setError(e.message);
    }
  }

  return (
    <Card>
      <CardHeader title="Agreement Closure" subheader="Add return details and close the agreement" />
      <CardContent>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <TextField label="Agreement ID" value={agreementId} onChange={(e)=>setAgreementId(e.target.value)} fullWidth />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField label="Vehicle ID" value={vehicleId} onChange={(e)=>setVehicleId(e.target.value)} fullWidth />
          </Grid>
          <Grid item xs={12}>
            <TextField label="End Date" value={endDate} onChange={(e)=>setEndDate(e.target.value)} fullWidth />
          </Grid>
          <Grid item xs={12}><Divider textAlign="left">Return Details</Divider></Grid>
          <Grid item xs={12} sm={4}>
            <TextField type="number" label="Odometer" value={odometerReading}
              onChange={(e)=>setOdometerReading(Number(e.target.value))} inputProps={{ min: 0 }} fullWidth />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField type="number" label="Fuel %" value={fuelLevel}
              onChange={(e)=>setFuelLevel(Number(e.target.value))} inputProps={{ min: 0, max: 100, step: 1 }} fullWidth />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField select label="Condition" value={overallRating} onChange={(e)=>setOverallRating(e.target.value as any)} fullWidth>
              <MenuItem value="EXCELLENT">EXCELLENT</MenuItem>
              <MenuItem value="GOOD">GOOD</MenuItem>
              <MenuItem value="FAIR">FAIR</MenuItem>
              <MenuItem value="POOR">POOR</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12}>
            <TextField label="Return Notes" value={returnNotes} onChange={(e)=>setReturnNotes(e.target.value)} fullWidth />
          </Grid>
          <Grid item xs={12}>
            <Button variant="contained" onClick={closeAgreement} disabled={disabled || !agreementId}>Close Agreement</Button>
          </Grid>
          {result && (
            <Grid item xs={12}><Alert severity="success">Closed: {result.id}</Alert></Grid>
          )}
          {error && (
            <Grid item xs={12}><Alert severity="error">{error}</Alert></Grid>
          )}
        </Grid>
      </CardContent>
    </Card>
  );
}
