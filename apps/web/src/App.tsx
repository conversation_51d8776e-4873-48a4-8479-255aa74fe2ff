import { Con<PERSON><PERSON>, <PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>B<PERSON>, Tool<PERSON>, IconButton, Drawer, List, ListItem, ListItemButton, ListItemText, Divider, Chip, Avatar } from '@mui/material';
import { motion } from 'framer-motion';
import { Routes, Route, Link, Navigate } from 'react-router-dom';
import { Logout, Person } from '@mui/icons-material';
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import STR from './pages/STR';
import STRNew from './pages/STRNew';
import ClosureNew from './pages/ClosureNew';
import Closure from './pages/Closure';
import SalikAdmin from './pages/SalikAdmin';
import FinesAdmin from './pages/FinesAdmin';
import LeaseAdmin from './pages/LeaseAdmin';
import IntegrationsAdmin from './pages/IntegrationsAdmin';
import ProtectedRoute from './components/ProtectedRoute';
import { useState, useEffect } from 'react';
import Billing from './pages/Billing';

export default function App() {
  const [token, setToken] = useState<string | null>(null);
  const roles = decodeRoles(token);
  const [navOpen, setNavOpen] = useState(false);
  const isAdmin = roles.includes('admin');
  const isAuthenticated = !!token;

  // Check for saved token on app load
  useEffect(() => {
    const savedToken = localStorage.getItem('authToken');
    if (savedToken) {
      setToken(savedToken);
    }
  }, []);

  const handleLogout = () => {
    setToken(null);
    localStorage.removeItem('authToken');
    // Optionally redirect to home page
    window.location.href = '/';
  };
  return (
    <>
      <AppBar color="inherit" position="sticky">
        <Toolbar sx={{ gap: 2 }}>
          <IconButton edge="start" onClick={() => setNavOpen(true)} sx={{ display: { xs: 'inline-flex', md: 'none' } }} aria-label="menu">
            {/* simple hamburger */}
            <Box component="span" sx={{ width: 22, height: 2, bgcolor: 'text.primary', display: 'block', position: 'relative', '&::before, &::after': { content: '""', position: 'absolute', left: 0, width: 22, height: 2, bgcolor: 'text.primary' }, '&::before': { top: -6 }, '&::after': { top: 6 } }} />
          </IconButton>
          <Typography variant="h6" sx={{ fontWeight: 700 }}>A‑ReALM</Typography>
          <Stack direction="row" spacing={1} sx={{ display: { xs: 'none', md: 'flex' }, ml: 2 }}>
            <Button component={Link} to="/" size="small">Dashboard</Button>
            {!isAuthenticated && <Button component={Link} to="/login" size="small">Login</Button>}
            {isAuthenticated && <Button component={Link} to="/str-new" size="small">STR Agreements</Button>}
            {isAuthenticated && <Button component={Link} to="/closure-new" size="small">Closure</Button>}
            {isAdmin && <Button component={Link} to="/billing" size="small">Billing</Button>}
            {isAdmin && <Button component={Link} to="/admin/lease" size="small">Fleet</Button>}
            {isAdmin && <Button component={Link} to="/admin/integrations" size="small">Integrations</Button>}
            {isAdmin && <Button component={Link} to="/admin/salik" size="small">Salik</Button>}
            {isAdmin && <Button component={Link} to="/admin/fines" size="small">Fines</Button>}
          </Stack>

          {/* Authentication Status */}
          <Box sx={{ ml: 'auto', display: 'flex', alignItems: 'center', gap: 1 }}>
            {isAuthenticated ? (
              <Stack direction="row" alignItems="center" spacing={1}>
                <Chip
                  avatar={<Avatar sx={{ bgcolor: 'primary.main' }}><Person /></Avatar>}
                  label={`${roles.join(', ') || 'user'}`}
                  variant="outlined"
                  size="small"
                  color="primary"
                />
                <IconButton
                  onClick={handleLogout}
                  size="small"
                  sx={{
                    color: 'text.secondary',
                    '&:hover': { color: 'error.main' }
                  }}
                  title="Logout"
                >
                  <Logout fontSize="small" />
                </IconButton>
              </Stack>
            ) : (
              <Typography variant="body2" color="text.secondary">
                Not signed in
              </Typography>
            )}
          </Box>
        </Toolbar>
      </AppBar>

      <Drawer anchor="left" open={navOpen} onClose={() => setNavOpen(false)} sx={{ display: { md: 'none' } }}>
        <Box sx={{ width: 260 }} role="presentation" onClick={() => setNavOpen(false)}>
          <Typography variant="h6" sx={{ p: 2 }}>Navigation</Typography>
          <Divider />
          <List>
            <ListItem disablePadding><ListItemButton component={Link} to="/"><ListItemText primary="Dashboard" /></ListItemButton></ListItem>
            {!isAuthenticated && <ListItem disablePadding><ListItemButton component={Link} to="/login"><ListItemText primary="Login" /></ListItemButton></ListItem>}
            {isAuthenticated && <ListItem disablePadding><ListItemButton component={Link} to="/str-new"><ListItemText primary="STR Agreements" /></ListItemButton></ListItem>}
            {isAuthenticated && <ListItem disablePadding><ListItemButton component={Link} to="/closure-new"><ListItemText primary="Closure" /></ListItemButton></ListItem>}
            {isAdmin && (<ListItem disablePadding><ListItemButton component={Link} to="/billing"><ListItemText primary="Billing" /></ListItemButton></ListItem>)}
            {isAdmin && (<ListItem disablePadding><ListItemButton component={Link} to="/admin/lease"><ListItemText primary="Fleet" /></ListItemButton></ListItem>)}
            {isAdmin && (<ListItem disablePadding><ListItemButton component={Link} to="/admin/integrations"><ListItemText primary="Integrations" /></ListItemButton></ListItem>)}
            {isAdmin && (<ListItem disablePadding><ListItemButton component={Link} to="/admin/salik"><ListItemText primary="Salik" /></ListItemButton></ListItem>)}
            {isAdmin && (<ListItem disablePadding><ListItemButton component={Link} to="/admin/fines"><ListItemText primary="Fines" /></ListItemButton></ListItem>)}
            {isAuthenticated && (
              <ListItem disablePadding>
                <ListItemButton onClick={handleLogout}>
                  <ListItemText primary="Logout" />
                </ListItemButton>
              </ListItem>
            )}
          </List>
        </Box>
      </Drawer>

      <Container maxWidth="lg">
        <Box sx={{ py: 3 }}>
          <Routes>
            <Route path="/" element={<Home token={token} roles={roles} />} />
            <Route
              path="/login"
              element={
                isAuthenticated ? (
                  <Navigate to="/" replace />
                ) : (
                  <Login onAuth={(t) => setToken(t)} />
                )
              }
            />
            <Route
              path="/register"
              element={
                isAuthenticated ? (
                  <Navigate to="/" replace />
                ) : (
                  <Register onAuth={(t) => setToken(t)} />
                )
              }
            />
            <Route
              path="/str"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated} userRoles={roles}>
                  <STR token={token} />
                </ProtectedRoute>
              }
            />
            <Route
              path="/str-new"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated} userRoles={roles}>
                  <STRNew userRoles={roles} token={token || ''} />
                </ProtectedRoute>
              }
            />
            <Route
              path="/closure"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated} userRoles={roles}>
                  <Closure token={token} />
                </ProtectedRoute>
              }
            />
            <Route
              path="/closure-new"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated} userRoles={roles}>
                  <ClosureNew userRoles={roles} token={token || ''} />
                </ProtectedRoute>
              }
            />
            <Route
              path="/billing"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated} requiredRoles={['admin']} userRoles={roles}>
                  <Billing token={token || ''} roles={roles} />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/lease"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated} requiredRoles={['admin']} userRoles={roles}>
                  <LeaseAdmin token={token || ''} roles={roles} />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/integrations"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated} requiredRoles={['admin', 'finance']} userRoles={roles}>
                  <IntegrationsAdmin token={token} roles={roles} />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/salik"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated} requiredRoles={['admin', 'finance', 'operations', 'customer']} userRoles={roles}>
                  <SalikAdmin token={token} roles={roles} />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/fines"
              element={
                <ProtectedRoute isAuthenticated={isAuthenticated} requiredRoles={['admin', 'finance', 'operations', 'customer']} userRoles={roles}>
                  <FinesAdmin token={token} roles={roles} />
                </ProtectedRoute>
              }
            />
          </Routes>
        </Box>
      </Container>
      <Box component="footer" sx={{ py: 4, textAlign: 'center', color: 'text.secondary' }}>
        <Typography variant="body2">© {new Date().getFullYear()} A‑ReALM</Typography>
      </Box>
    </>
  );
}



function decodeRoles(token: string | null): string[] {
  try {
    if (!token) return [];
    const part = token.split('.')[1] || '';
    // Convert base64url to base64
    const base64 = part.replace(/-/g, '+').replace(/_/g, '/').padEnd(Math.ceil(part.length / 4) * 4, '=');
    const payload = JSON.parse(atob(base64));
    return Array.isArray(payload.roles) ? payload.roles : [];
  } catch {
    return [];
  }
}

function Guard({ roles, required, children }: { roles: string[], required: string[], children: React.ReactNode }) {
  const has = required.some(r => roles.includes(r));
  if (!has) return <Typography color="error">Forbidden</Typography>;
  return <>{children}</>;
}
