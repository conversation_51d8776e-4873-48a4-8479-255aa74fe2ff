import { STRPermissions, User, STRAgreement, ClosurePermissions, AdditionalCharge, SettlementSummary } from '../types/str';

/**
 * Role-based access control for STR module
 * Implements principle of least privilege
 */

export function getSTRPermissions(userRoles: string[]): STRPermissions {
  const isAdmin = userRoles.includes('admin');
  const isAgent = userRoles.includes('agent') || isAdmin;
  const isCustomer = userRoles.includes('user') && !isAgent;

  return {
    // Core CRUD operations
    canCreate: isAgent, // Only agents/admins can create STR agreements
    canEdit: isAgent, // Only agents/admins can edit agreements
    canClose: isAgent, // Only agents/admins can close agreements
    
    // View permissions
    canViewAll: isAgent, // Agents can view all agreements
    canViewOwn: true, // Everyone can view their own agreements
    
    // Business operations
    canApproveExtensions: isAgent, // Only agents can approve extensions
    canOverridePricing: isAdmin, // Only admins can override pricing
    canWaiveFees: isAdmin, // Only admins can waive fees
    
    // Financial operations
    canAccessFinancials: isAgent, // Agents can access financial data
    canPostAccounting: isAdmin, // Only admins can post to accounting
    
    // Document management
    canManageDocuments: isAgent, // Agents can manage all documents
  };
}

export function canUserAccessAgreement(
  userRoles: string[], 
  userId: string, 
  agreement: STRAgreement
): boolean {
  const permissions = getSTRPermissions(userRoles);
  
  // Agents/admins can access all agreements
  if (permissions.canViewAll) {
    return true;
  }
  
  // Customers can only access their own agreements
  if (permissions.canViewOwn && agreement.customer?.id === userId) {
    return true;
  }
  
  return false;
}

export function canUserPerformAction(
  userRoles: string[],
  action: keyof STRPermissions
): boolean {
  const permissions = getSTRPermissions(userRoles);
  return permissions[action];
}

export function getAvailableActions(
  userRoles: string[],
  agreement: STRAgreement,
  userId: string
): string[] {
  const permissions = getSTRPermissions(userRoles);
  const actions: string[] = [];
  
  // Check if user can access this agreement
  if (!canUserAccessAgreement(userRoles, userId, agreement)) {
    return [];
  }
  
  const isAgent = permissions.canEdit;
  const isCustomer = !isAgent;
  
  // Agent actions
  if (isAgent) {
    switch (agreement.status) {
      case 'DRAFT':
        actions.push('edit', 'confirm', 'cancel');
        break;
      case 'CONFIRMED':
        actions.push('edit', 'start_handover', 'cancel');
        break;
      case 'ON_HIRE':
        actions.push('view_details', 'extend', 'start_return');
        break;
      case 'ACTIVE':
        actions.push('view_details', 'extend', 'start_return', 'add_charges');
        break;
      case 'CLOSURE_IN_PROGRESS':
        actions.push('view_details', 'complete_closure', 'cancel_closure');
        break;
      case 'CLOSED':
        actions.push('view_details', 'reopen');
        break;
    }
    
    // Always available for agents
    actions.push('view_audit', 'manage_documents', 'view_financials');
    
    if (permissions.canOverridePricing) {
      actions.push('override_pricing');
    }
    
    if (permissions.canWaiveFees) {
      actions.push('waive_fees');
    }
  }
  
  // Customer actions
  if (isCustomer) {
    switch (agreement.status) {
      case 'CONFIRMED':
      case 'ON_HIRE':
      case 'ACTIVE':
        if (agreement.allowExtension) {
          actions.push('request_extension');
        }
        if (agreement.allowEarlyReturn) {
          actions.push('request_return');
        }
        if (agreement.outstandingAmount > 0) {
          actions.push('make_payment');
        }
        break;
    }
    
    // Always available for customers
    actions.push('view_details', 'download_documents', 'upload_documents');
    
    // Payment actions
    if (agreement.outstandingAmount > 0) {
      actions.push('view_invoices', 'make_payment');
    }
  }
  
  return actions;
}

export function getActionLabel(action: string): string {
  const labels: Record<string, string> = {
    // Agent actions
    'edit': 'Edit Agreement',
    'confirm': 'Confirm Agreement',
    'cancel': 'Cancel Agreement',
    'start_handover': 'Start Handover',
    'extend': 'Extend Agreement',
    'start_return': 'Start Return Process',
    'add_charges': 'Add Charges',
    'complete_closure': 'Complete Closure',
    'cancel_closure': 'Cancel Closure',
    'reopen': 'Reopen Agreement',
    'view_audit': 'View Audit Trail',
    'manage_documents': 'Manage Documents',
    'view_financials': 'View Financials',
    'override_pricing': 'Override Pricing',
    'waive_fees': 'Waive Fees',
    
    // Customer actions
    'request_extension': 'Request Extension',
    'request_return': 'Start Return',
    'make_payment': 'Make Payment',
    'view_details': 'View Details',
    'download_documents': 'Download Documents',
    'upload_documents': 'Upload Documents',
    'view_invoices': 'View Invoices',
  };
  
  return labels[action] || action;
}

export function getActionIcon(action: string): string {
  const icons: Record<string, string> = {
    'edit': 'EditIcon',
    'confirm': 'CheckCircleIcon',
    'cancel': 'CancelIcon',
    'start_handover': 'HandshakeIcon',
    'extend': 'ExtensionIcon',
    'start_return': 'KeyboardReturnIcon',
    'add_charges': 'AddIcon',
    'complete_closure': 'TaskAltIcon',
    'cancel_closure': 'UndoIcon',
    'reopen': 'RestoreIcon',
    'view_audit': 'HistoryIcon',
    'manage_documents': 'FolderIcon',
    'view_financials': 'AccountBalanceIcon',
    'override_pricing': 'MonetizationOnIcon',
    'waive_fees': 'MoneyOffIcon',
    'request_extension': 'ScheduleIcon',
    'request_return': 'AssignmentReturnIcon',
    'make_payment': 'PaymentIcon',
    'view_details': 'VisibilityIcon',
    'download_documents': 'DownloadIcon',
    'upload_documents': 'UploadIcon',
    'view_invoices': 'ReceiptIcon',
  };
  
  return icons[action] || 'ActionIcon';
}

export function getStatusColor(status: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' {
  const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
    'DRAFT': 'default',
    'CONFIRMED': 'info',
    'ON_HIRE': 'primary',
    'ACTIVE': 'success',
    'CLOSURE_IN_PROGRESS': 'warning',
    'CLOSED': 'secondary',
    'CANCELLED': 'error',
  };
  
  return colors[status] || 'default';
}

export function getWorkflowStageLabel(stage: string): string {
  const labels: Record<string, string> = {
    'CREATED': 'Created',
    'CONFIRMED': 'Confirmed',
    'HANDOVER': 'Handover in Progress',
    'ACTIVE': 'Active Rental',
    'RETURN_REQUESTED': 'Return Requested',
    'INSPECTION': 'Under Inspection',
    'SETTLEMENT': 'Settlement in Progress',
    'CLOSED': 'Closed',
  };
  
  return labels[stage] || stage;
}

export function canTransitionToStatus(
  currentStatus: string,
  targetStatus: string,
  userRoles: string[]
): boolean {
  const permissions = getSTRPermissions(userRoles);
  
  // Only agents can change status
  if (!permissions.canEdit) {
    return false;
  }
  
  const validTransitions: Record<string, string[]> = {
    'DRAFT': ['CONFIRMED', 'CANCELLED'],
    'CONFIRMED': ['ON_HIRE', 'CANCELLED'],
    'ON_HIRE': ['ACTIVE', 'CLOSURE_IN_PROGRESS'],
    'ACTIVE': ['CLOSURE_IN_PROGRESS'],
    'CLOSURE_IN_PROGRESS': ['CLOSED', 'ACTIVE'],
    'CLOSED': [], // Closed agreements cannot be changed (except by admin reopen)
    'CANCELLED': [], // Cancelled agreements cannot be changed
  };
  
  // Admins can reopen closed agreements
  if (permissions.canOverridePricing && currentStatus === 'CLOSED' && targetStatus === 'ACTIVE') {
    return true;
  }
  
  return validTransitions[currentStatus]?.includes(targetStatus) || false;
}

export function validateBusinessRules(
  agreement: Partial<STRAgreement>,
  userRoles: string[]
): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  const permissions = getSTRPermissions(userRoles);
  
  // Date validation
  if (agreement.startDate && agreement.endDate) {
    const start = new Date(agreement.startDate);
    const end = new Date(agreement.endDate);
    
    if (end <= start) {
      errors.push('End date must be after start date');
    }
    
    if (start < new Date()) {
      errors.push('Start date cannot be in the past');
    }
  }
  
  // Customer validation
  if (agreement.customer?.status === 'HOLD') {
    errors.push('Customer is on hold and cannot rent vehicles');
  }
  
  if (agreement.customer?.status === 'BLOCKED') {
    errors.push('Customer is blocked and cannot rent vehicles');
  }
  
  if (agreement.customer?.kycStatus !== 'VERIFIED') {
    errors.push('Customer KYC must be verified before creating agreement');
  }
  
  // Vehicle validation
  if (agreement.vehicle?.status !== 'AVAILABLE') {
    errors.push('Vehicle is not available for rental');
  }
  
  if (agreement.vehicle?.businessType && !['STR', 'BOTH'].includes(agreement.vehicle.businessType)) {
    errors.push('Vehicle is not eligible for short-term rental');
  }
  
  // Pricing validation
  if (agreement.pricing?.totalAmount && agreement.pricing.totalAmount <= 0) {
    errors.push('Total amount must be greater than zero');
  }
  
  // Deposit validation
  if (agreement.requiresDeposit && (!agreement.depositAmount || agreement.depositAmount <= 0)) {
    errors.push('Deposit is required for this agreement');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

// Closure-specific permissions
export function getClosurePermissions(userRoles: string[]): ClosurePermissions {
  const isAdmin = userRoles.includes('admin');
  const isAgent = userRoles.includes('agent') || isAdmin;
  const isCustomer = userRoles.includes('user') && !isAgent;

  return {
    // Core closure operations
    canInitiateClosure: isAgent, // Only agents can initiate closure
    canViewAllClosures: isAgent, // Agents can view all closures
    canViewOwnClosures: true, // Everyone can view their own closures

    // Charges management
    canAddCharges: isAgent, // Agents can add additional charges
    canWaiveCharges: isAdmin, // Only admins can waive charges
    canOverrideCharges: isAdmin, // Only admins can override charges

    // Financial operations
    canProcessSettlement: isAgent, // Agents can process settlements
    canGenerateDocuments: isAgent, // Agents can generate documents
    canPostAccounting: isAdmin, // Only admins can post to accounting
    canApproveExceptions: isAdmin, // Only admins can approve exceptions
  };
}

export function canUserClosureAction(
  userRoles: string[],
  action: keyof ClosurePermissions
): boolean {
  const permissions = getClosurePermissions(userRoles);
  return permissions[action];
}

export function validateClosureBusinessRules(
  agreement: STRAgreement,
  closureData: any,
  userRoles: string[]
): { valid: boolean; errors: string[]; warnings: string[] } {
  const errors: string[] = [];
  const warnings: string[] = [];
  const permissions = getClosurePermissions(userRoles);

  // Agreement status validation
  if (!['ACTIVE', 'ON_HIRE'].includes(agreement.status)) {
    errors.push('Agreement must be active to initiate closure');
  }

  // Return date validation
  if (closureData.returnDate) {
    const returnDate = new Date(closureData.returnDate);
    const agreementEndDate = new Date(agreement.endDate);
    const now = new Date();

    if (returnDate > now) {
      errors.push('Return date cannot be in the future');
    }

    if (returnDate < new Date(agreement.startDate)) {
      errors.push('Return date cannot be before agreement start date');
    }

    // Late return warning
    if (returnDate > agreementEndDate) {
      const daysLate = Math.ceil((returnDate.getTime() - agreementEndDate.getTime()) / (1000 * 60 * 60 * 24));
      warnings.push(`Vehicle returned ${daysLate} day(s) late - late fees may apply`);
    }
  }

  // Odometer validation
  if (closureData.odometerReading !== undefined) {
    const handoverOdometer = agreement.handoverDetails?.odometerReading || 0;

    if (closureData.odometerReading < handoverOdometer) {
      errors.push('Return odometer reading cannot be less than handover reading');
    }

    // Excessive mileage warning
    const mileageDiff = closureData.odometerReading - handoverOdometer;
    const expectedMileage = agreement.duration * 200; // 200km per day average

    if (mileageDiff > expectedMileage * 1.5) {
      warnings.push('Excessive mileage detected - additional charges may apply');
    }
  }

  // Fuel level validation
  if (closureData.fuelLevel !== undefined) {
    if (closureData.fuelLevel < 0 || closureData.fuelLevel > 100) {
      errors.push('Fuel level must be between 0 and 100');
    }

    const handoverFuel = agreement.handoverDetails?.fuelLevel || 100;
    if (closureData.fuelLevel < handoverFuel * 0.8) {
      warnings.push('Low fuel level - fuel top-up charges may apply');
    }
  }

  // Outstanding balance validation
  if (agreement.outstandingAmount > 0) {
    if (agreement.customer?.type === 'CASH' && !permissions.canApproveExceptions) {
      errors.push('Cash customers cannot close with outstanding balance - manager approval required');
    }
  }

  // Damage validation
  if (closureData.damageReport && closureData.damageReport.length > 0) {
    const unapprovedDamages = closureData.damageReport.filter((d: any) => !d.approved);
    if (unapprovedDamages.length > 0 && !permissions.canApproveExceptions) {
      warnings.push('Unapproved damages detected - manager approval may be required');
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

export function calculateSettlement(
  agreement: STRAgreement,
  additionalCharges: AdditionalCharge[]
): SettlementSummary {
  const originalAmount = agreement.totalAmount;
  const paidAmount = agreement.paidAmount;

  // Calculate additional charges by type
  const chargesByType = additionalCharges.reduce((acc, charge) => {
    if (!charge.waived) {
      acc[charge.type] = (acc[charge.type] || 0) + charge.amount;
    }
    return acc;
  }, {} as Record<string, number>);

  const additionalChargesTotal = additionalCharges
    .filter(c => !c.waived)
    .reduce((sum, c) => sum + c.amount, 0);

  const waivedCharges = additionalCharges
    .filter(c => c.waived)
    .reduce((sum, c) => sum + c.amount, 0);

  const salikCharges = chargesByType['SALIK'] || 0;
  const fineCharges = chargesByType['FINE'] || 0;

  const securityDeposit = agreement.depositAmount || 0;
  const refundableDeposit = agreement.refundableDeposit || 0;

  const totalCharges = originalAmount + additionalChargesTotal;
  const totalCredits = paidAmount + refundableDeposit;
  const netAmount = totalCharges - totalCredits;

  // Tax calculation (5% VAT on taxable items)
  const taxableCharges = additionalCharges
    .filter(c => c.taxable && !c.waived)
    .reduce((sum, c) => sum + c.amount, 0);

  const taxAmount = taxableCharges * 0.05;
  const totalWithTax = netAmount + taxAmount;

  return {
    originalAmount,
    paidAmount,
    additionalCharges: additionalChargesTotal,
    salikCharges,
    fineCharges,
    discounts: 0, // TODO: Implement discount logic
    waivedCharges,
    securityDeposit,
    refundableDeposit,
    totalCharges,
    totalCredits,
    netAmount,
    taxableAmount: taxableCharges,
    taxAmount,
    totalWithTax
  };
}

export function getClosureStatusColor(status: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' {
  const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
    'SUBMITTED': 'info',
    'UNDER_REVIEW': 'warning',
    'APPROVED': 'primary',
    'PROCESSING': 'warning',
    'COMPLETED': 'success',
    'REJECTED': 'error',
  };

  return colors[status] || 'default';
}

export function getChargeTypeLabel(type: string): string {
  const labels: Record<string, string> = {
    'DAMAGE': 'Vehicle Damage',
    'FUEL': 'Fuel Top-up',
    'MILEAGE': 'Excess Mileage',
    'CLEANING': 'Cleaning Fee',
    'LATE_RETURN': 'Late Return Fee',
    'SALIK': 'Salik Charges',
    'FINE': 'Traffic Fines',
    'OTHER': 'Other Charges',
  };

  return labels[type] || type;
}
