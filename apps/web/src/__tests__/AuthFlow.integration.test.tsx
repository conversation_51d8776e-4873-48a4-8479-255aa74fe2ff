import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import App from '../App';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

// Mock the API
global.fetch = jest.fn();

const theme = createTheme();

const renderApp = () => {
  return render(
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <App />
      </ThemeProvider>
    </BrowserRouter>
  );
};

// Helper to create a mock JWT token
const createMockToken = (roles: string[] = ['user']) => {
  const payload = { roles, sub: 'test-user', exp: Date.now() / 1000 + 3600 };
  const base64Payload = btoa(JSON.stringify(payload));
  return `header.${base64Payload}.signature`;
};

describe('Authentication Flow Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    (global.fetch as jest.Mock).mockClear();
    
    // Mock window.location.href
    delete (window as any).location;
    (window as any).location = { href: '' };
  });

  describe('Complete Login Flow', () => {
    it('allows user to login and access protected routes', async () => {
      const mockResponse = { 
        accessToken: createMockToken(['user']), 
        refreshToken: 'refresh-token' 
      };
      
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      renderApp();

      // Should show login link initially
      expect(screen.getByRole('link', { name: /login/i })).toBeInTheDocument();
      expect(screen.queryByRole('link', { name: /agreements/i })).not.toBeInTheDocument();

      // Navigate to login page
      fireEvent.click(screen.getByRole('link', { name: /login/i }));

      // Fill out login form
      const usernameInput = screen.getByLabelText(/username/i);
      fireEvent.change(usernameInput, { target: { value: 'demo' } });

      // Submit login
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

      // Wait for login to complete
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ username: 'demo' }),
        });
      });

      // Should now show user navigation and hide login link
      await waitFor(() => {
        expect(screen.queryByRole('link', { name: /login/i })).not.toBeInTheDocument();
        expect(screen.getByRole('link', { name: /agreements/i })).toBeInTheDocument();
        expect(screen.getByRole('link', { name: /closure/i })).toBeInTheDocument();
      });

      // Should show user role
      expect(screen.getByText(/user/)).toBeInTheDocument();
    });

    it('allows admin to login and access admin routes', async () => {
      const mockResponse = { 
        accessToken: createMockToken(['admin']), 
        refreshToken: 'refresh-token' 
      };
      
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      renderApp();

      // Navigate to login page
      fireEvent.click(screen.getByRole('link', { name: /login/i }));

      // Use admin quick access button
      fireEvent.click(screen.getByRole('button', { name: /admin user/i }));
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

      // Wait for login to complete
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ username: 'admin' }),
        });
      });

      // Should show admin navigation
      await waitFor(() => {
        expect(screen.getByRole('link', { name: /billing/i })).toBeInTheDocument();
        expect(screen.getByRole('link', { name: /fleet/i })).toBeInTheDocument();
        expect(screen.getByRole('link', { name: /integrations/i })).toBeInTheDocument();
      });

      // Should show admin role
      expect(screen.getByText(/admin/)).toBeInTheDocument();
    });
  });

  describe('Complete Registration Flow', () => {
    it('allows user to register and get logged in', async () => {
      const mockResponse = { 
        accessToken: createMockToken(['user']), 
        refreshToken: 'refresh-token' 
      };
      
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      renderApp();

      // Navigate to login page first
      fireEvent.click(screen.getByRole('link', { name: /login/i }));

      // Click sign up link
      fireEvent.click(screen.getByRole('link', { name: /sign up here/i }));

      // Fill out registration form
      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'newuser' } });
      fireEvent.change(screen.getByLabelText(/email address/i), { target: { value: '<EMAIL>' } });
      fireEvent.change(screen.getByLabelText(/^password$/i), { target: { value: 'Password123' } });
      fireEvent.change(screen.getByLabelText(/confirm password/i), { target: { value: 'Password123' } });
      fireEvent.click(screen.getByRole('checkbox'));

      // Submit registration
      fireEvent.click(screen.getByRole('button', { name: /create account/i }));

      // Wait for registration to complete
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ username: 'newuser' }),
        });
      });

      // Should be logged in and show user navigation
      await waitFor(() => {
        expect(screen.queryByRole('link', { name: /login/i })).not.toBeInTheDocument();
        expect(screen.getByRole('link', { name: /agreements/i })).toBeInTheDocument();
      });
    });

    it('allows admin registration', async () => {
      const mockResponse = { 
        accessToken: createMockToken(['admin']), 
        refreshToken: 'refresh-token' 
      };
      
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      renderApp();

      // Navigate to registration page
      fireEvent.click(screen.getByRole('link', { name: /login/i }));
      fireEvent.click(screen.getByRole('link', { name: /sign up here/i }));

      // Fill out registration form with admin role
      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'newadmin' } });
      fireEvent.change(screen.getByLabelText(/email address/i), { target: { value: '<EMAIL>' } });
      fireEvent.change(screen.getByLabelText(/^password$/i), { target: { value: 'Password123' } });
      fireEvent.change(screen.getByLabelText(/confirm password/i), { target: { value: 'Password123' } });

      // Select admin role
      const roleSelect = screen.getByLabelText(/account type/i);
      fireEvent.mouseDown(roleSelect);
      fireEvent.click(screen.getByText('Administrator'));

      fireEvent.click(screen.getByRole('checkbox'));

      // Submit registration
      fireEvent.click(screen.getByRole('button', { name: /create account/i }));

      // Wait for registration to complete
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ username: 'newadmin-admin' }),
        });
      });

      // Should be logged in with admin privileges
      await waitFor(() => {
        expect(screen.getByRole('link', { name: /billing/i })).toBeInTheDocument();
        expect(screen.getByText(/admin/)).toBeInTheDocument();
      });
    });
  });

  describe('Logout Flow', () => {
    it('allows user to logout', async () => {
      // Start with authenticated user
      const token = createMockToken(['user']);
      localStorage.setItem('authToken', token);

      renderApp();

      // Should show authenticated state
      expect(screen.getByText(/user/)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /logout/i })).toBeInTheDocument();

      // Click logout
      fireEvent.click(screen.getByRole('button', { name: /logout/i }));

      // Should clear token and redirect
      expect(localStorage.getItem('authToken')).toBeNull();
      expect(window.location.href).toBe('/');

      // Should show logged out state
      expect(screen.getByText(/not signed in/i)).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /login/i })).toBeInTheDocument();
    });
  });

  describe('Protected Route Access', () => {
    it('redirects unauthenticated users to login', async () => {
      renderApp();

      // Try to access a protected route (this would be done through URL navigation in real app)
      // For this test, we verify that protected navigation items are not visible
      expect(screen.queryByRole('link', { name: /agreements/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('link', { name: /billing/i })).not.toBeInTheDocument();
    });

    it('allows authenticated users to access user routes', () => {
      const token = createMockToken(['user']);
      localStorage.setItem('authToken', token);

      renderApp();

      // Should show user routes
      expect(screen.getByRole('link', { name: /agreements/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /closure/i })).toBeInTheDocument();

      // Should not show admin routes
      expect(screen.queryByRole('link', { name: /billing/i })).not.toBeInTheDocument();
    });

    it('allows admin users to access all routes', () => {
      const token = createMockToken(['admin']);
      localStorage.setItem('authToken', token);

      renderApp();

      // Should show all routes
      expect(screen.getByRole('link', { name: /agreements/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /closure/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /billing/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /fleet/i })).toBeInTheDocument();
    });
  });

  describe('Remember Me Functionality', () => {
    it('persists login when remember me is checked', async () => {
      const mockResponse = { 
        accessToken: createMockToken(['user']), 
        refreshToken: 'refresh-token' 
      };
      
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      renderApp();

      // Navigate to login
      fireEvent.click(screen.getByRole('link', { name: /login/i }));

      // Fill form and check remember me
      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'demo' } });
      fireEvent.click(screen.getByRole('checkbox', { name: /remember me/i }));
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

      // Should save token to localStorage
      await waitFor(() => {
        expect(localStorage.getItem('authToken')).toBeTruthy();
      });
    });

    it('auto-logs in user with saved token', () => {
      const token = createMockToken(['user']);
      localStorage.setItem('authToken', token);

      renderApp();

      // Should be automatically logged in
      expect(screen.getByText(/user/)).toBeInTheDocument();
      expect(screen.queryByRole('link', { name: /login/i })).not.toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles login API errors gracefully', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      renderApp();

      // Navigate to login and submit
      fireEvent.click(screen.getByRole('link', { name: /login/i }));
      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'demo' } });
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/network error/i)).toBeInTheDocument();
      });

      // Should remain on login page
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
    });

    it('handles registration API errors gracefully', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Registration failed'));

      renderApp();

      // Navigate to registration and submit valid form
      fireEvent.click(screen.getByRole('link', { name: /login/i }));
      fireEvent.click(screen.getByRole('link', { name: /sign up here/i }));

      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'newuser' } });
      fireEvent.change(screen.getByLabelText(/email address/i), { target: { value: '<EMAIL>' } });
      fireEvent.change(screen.getByLabelText(/^password$/i), { target: { value: 'Password123' } });
      fireEvent.change(screen.getByLabelText(/confirm password/i), { target: { value: 'Password123' } });
      fireEvent.click(screen.getByRole('checkbox'));
      fireEvent.click(screen.getByRole('button', { name: /create account/i }));

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/registration failed/i)).toBeInTheDocument();
      });

      // Should remain on registration page
      expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument();
    });
  });

  describe('Navigation Between Auth Pages', () => {
    it('allows navigation from login to registration', () => {
      renderApp();

      // Go to login page
      fireEvent.click(screen.getByRole('link', { name: /login/i }));
      expect(screen.getByText('Welcome Back')).toBeInTheDocument();

      // Navigate to registration
      fireEvent.click(screen.getByRole('link', { name: /sign up here/i }));
      expect(screen.getByText('Create Account')).toBeInTheDocument();
    });

    it('allows navigation from registration to login', () => {
      renderApp();

      // Go to login then registration
      fireEvent.click(screen.getByRole('link', { name: /login/i }));
      fireEvent.click(screen.getByRole('link', { name: /sign up here/i }));
      expect(screen.getByText('Create Account')).toBeInTheDocument();

      // Navigate back to login
      fireEvent.click(screen.getByRole('link', { name: /sign in instead/i }));
      expect(screen.getByText('Welcome Back')).toBeInTheDocument();
    });

    it('redirects authenticated users away from auth pages', () => {
      const token = createMockToken(['user']);
      localStorage.setItem('authToken', token);

      renderApp();

      // Should not be able to access login page when authenticated
      // (This would be tested through URL navigation in a real app)
      expect(screen.queryByText('Welcome Back')).not.toBeInTheDocument();
      expect(screen.queryByText('Create Account')).not.toBeInTheDocument();
    });
  });
});
