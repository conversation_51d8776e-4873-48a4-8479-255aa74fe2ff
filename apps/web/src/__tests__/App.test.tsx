import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON><PERSON>Router } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import App from '../App';

// Mock all the page components
jest.mock('../pages/Home', () => {
  return function MockHome({ token, roles }: any) {
    return <div data-testid="home-page">Home - Token: {token ? 'present' : 'none'} - Roles: {roles.join(',')}</div>;
  };
});

jest.mock('../pages/Login', () => {
  return function MockLogin({ onAuth }: any) {
    return (
      <div data-testid="login-page">
        <button onClick={() => onAuth('test-token')}>Mock Login</button>
      </div>
    );
  };
});

jest.mock('../pages/STR', () => {
  return function MockSTR() {
    return <div data-testid="str-page">STR Page</div>;
  };
});

jest.mock('../pages/Closure', () => {
  return function MockClosure() {
    return <div data-testid="closure-page">Closure Page</div>;
  };
});

jest.mock('../pages/Billing', () => {
  return function MockBilling() {
    return <div data-testid="billing-page">Billing Page</div>;
  };
});

jest.mock('../pages/LeaseAdmin', () => {
  return function MockLeaseAdmin() {
    return <div data-testid="lease-admin-page">Lease Admin Page</div>;
  };
});

jest.mock('../pages/IntegrationsAdmin', () => {
  return function MockIntegrationsAdmin() {
    return <div data-testid="integrations-admin-page">Integrations Admin Page</div>;
  };
});

jest.mock('../pages/SalikAdmin', () => {
  return function MockSalikAdmin() {
    return <div data-testid="salik-admin-page">Salik Admin Page</div>;
  };
});

jest.mock('../pages/FinesAdmin', () => {
  return function MockFinesAdmin() {
    return <div data-testid="fines-admin-page">Fines Admin Page</div>;
  };
});

// Mock ProtectedRoute
jest.mock('../components/ProtectedRoute', () => {
  return function MockProtectedRoute({ children, isAuthenticated, requiredRoles, userRoles }: any) {
    if (!isAuthenticated) {
      return <div data-testid="redirect-to-login">Redirecting to login</div>;
    }
    if (requiredRoles && requiredRoles.length > 0) {
      const hasRole = requiredRoles.some((role: string) => userRoles.includes(role));
      if (!hasRole) {
        return <div data-testid="redirect-to-home">Redirecting to home (insufficient permissions)</div>;
      }
    }
    return <>{children}</>;
  };
});

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

const theme = createTheme();

const renderApp = () => {
  return render(
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <App />
      </ThemeProvider>
    </BrowserRouter>
  );
};

// Helper to create a mock JWT token
const createMockToken = (roles: string[] = ['user']) => {
  const payload = { roles, sub: 'test-user', exp: Date.now() / 1000 + 3600 };
  const base64Payload = btoa(JSON.stringify(payload));
  return `header.${base64Payload}.signature`;
};

describe('App Component', () => {
  beforeEach(() => {
    localStorage.clear();
    // Mock window.location.href
    delete (window as any).location;
    (window as any).location = { href: '' };
  });

  describe('Initial Rendering', () => {
    it('renders the app with navigation and home page', () => {
      renderApp();
      
      expect(screen.getByText('A‑ReALM')).toBeInTheDocument();
      expect(screen.getByTestId('home-page')).toBeInTheDocument();
      expect(screen.getByText(/not signed in/i)).toBeInTheDocument();
    });

    it('shows login link when not authenticated', () => {
      renderApp();
      
      expect(screen.getByRole('link', { name: /login/i })).toBeInTheDocument();
    });

    it('does not show protected navigation items when not authenticated', () => {
      renderApp();
      
      expect(screen.queryByRole('link', { name: /agreements/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('link', { name: /closure/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('link', { name: /billing/i })).not.toBeInTheDocument();
    });
  });

  describe('Authentication State', () => {
    it('loads saved token from localStorage on mount', () => {
      const token = createMockToken(['admin']);
      localStorage.setItem('authToken', token);
      
      renderApp();
      
      expect(screen.getByText(/admin/)).toBeInTheDocument();
      expect(screen.queryByText(/not signed in/i)).not.toBeInTheDocument();
    });

    it('shows user role in navigation when authenticated', () => {
      const token = createMockToken(['admin', 'user']);
      localStorage.setItem('authToken', token);
      
      renderApp();
      
      expect(screen.getByText(/admin, user/)).toBeInTheDocument();
    });

    it('shows logout button when authenticated', () => {
      const token = createMockToken(['user']);
      localStorage.setItem('authToken', token);
      
      renderApp();
      
      expect(screen.getByRole('button', { name: /logout/i })).toBeInTheDocument();
    });

    it('handles logout correctly', () => {
      const token = createMockToken(['user']);
      localStorage.setItem('authToken', token);
      
      renderApp();
      
      const logoutButton = screen.getByRole('button', { name: /logout/i });
      fireEvent.click(logoutButton);
      
      expect(localStorage.getItem('authToken')).toBeNull();
      expect(window.location.href).toBe('/');
    });
  });

  describe('Navigation for Authenticated Users', () => {
    it('shows user navigation items when authenticated', () => {
      const token = createMockToken(['user']);
      localStorage.setItem('authToken', token);
      
      renderApp();
      
      expect(screen.getByRole('link', { name: /agreements/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /closure/i })).toBeInTheDocument();
      expect(screen.queryByRole('link', { name: /login/i })).not.toBeInTheDocument();
    });

    it('shows admin navigation items when user has admin role', () => {
      const token = createMockToken(['admin']);
      localStorage.setItem('authToken', token);
      
      renderApp();
      
      expect(screen.getByRole('link', { name: /billing/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /fleet/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /integrations/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /salik/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /fines/i })).toBeInTheDocument();
    });

    it('does not show admin navigation items for regular users', () => {
      const token = createMockToken(['user']);
      localStorage.setItem('authToken', token);
      
      renderApp();
      
      expect(screen.queryByRole('link', { name: /billing/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('link', { name: /fleet/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('link', { name: /integrations/i })).not.toBeInTheDocument();
    });
  });

  describe('Mobile Navigation', () => {
    it('shows mobile menu items when authenticated', () => {
      const token = createMockToken(['admin']);
      localStorage.setItem('authToken', token);
      
      renderApp();
      
      // Open mobile menu (this would require more complex testing for actual mobile behavior)
      // For now, we can check that the mobile navigation structure exists
      expect(screen.getByRole('button', { name: /menu/i })).toBeInTheDocument();
    });
  });

  describe('Role Decoding', () => {
    it('handles invalid tokens gracefully', () => {
      localStorage.setItem('authToken', 'invalid-token');
      
      renderApp();
      
      expect(screen.getByText(/not signed in/i)).toBeInTheDocument();
    });

    it('handles tokens without roles', () => {
      const payload = { sub: 'test-user', exp: Date.now() / 1000 + 3600 };
      const base64Payload = btoa(JSON.stringify(payload));
      const token = `header.${base64Payload}.signature`;
      
      localStorage.setItem('authToken', token);
      
      renderApp();
      
      // Should show as authenticated but with no specific roles
      expect(screen.queryByText(/not signed in/i)).not.toBeInTheDocument();
    });

    it('handles malformed JWT payload', () => {
      const token = 'header.invalid-base64.signature';
      localStorage.setItem('authToken', token);
      
      renderApp();
      
      expect(screen.getByText(/not signed in/i)).toBeInTheDocument();
    });
  });

  describe('Route Protection', () => {
    it('redirects to login for protected routes when not authenticated', () => {
      renderApp();
      
      // Navigate to a protected route (this would be done through router in real scenario)
      // For testing purposes, we can verify the ProtectedRoute behavior
      expect(screen.queryByTestId('redirect-to-login')).not.toBeInTheDocument();
    });

    it('allows access to protected routes when authenticated', () => {
      const token = createMockToken(['user']);
      localStorage.setItem('authToken', token);
      
      renderApp();
      
      // User should be able to access user-level protected routes
      expect(screen.getByRole('link', { name: /agreements/i })).toBeInTheDocument();
    });

    it('restricts admin routes for non-admin users', () => {
      const token = createMockToken(['user']);
      localStorage.setItem('authToken', token);
      
      renderApp();
      
      // Admin routes should not be visible
      expect(screen.queryByRole('link', { name: /billing/i })).not.toBeInTheDocument();
    });
  });

  describe('Login Flow', () => {
    it('redirects authenticated users away from login page', () => {
      const token = createMockToken(['user']);
      localStorage.setItem('authToken', token);
      
      renderApp();
      
      // If user tries to access login while authenticated, they should be redirected
      // This would be tested through routing in a real scenario
      expect(screen.queryByTestId('login-page')).not.toBeInTheDocument();
    });
  });

  describe('Footer', () => {
    it('renders footer with current year', () => {
      renderApp();
      
      const currentYear = new Date().getFullYear();
      expect(screen.getByText(`© ${currentYear} A‑ReALM`)).toBeInTheDocument();
    });
  });
});
