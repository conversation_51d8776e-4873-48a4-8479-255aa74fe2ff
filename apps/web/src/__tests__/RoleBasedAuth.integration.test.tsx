import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import App from '../App';

const theme = createTheme();

// Mock the API module
jest.mock('../api', () => ({
  api: {
    login: jest.fn()
  }
}));

import { api } from '../api';
const mockApi = api as jest.Mocked<typeof api>;

const renderApp = () => {
  return render(
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <App />
      </ThemeProvider>
    </BrowserRouter>
  );
};

// Helper to create mock JWT tokens
const createMockToken = (roles: string[]) => {
  const payload = {
    sub: 'testuser',
    roles: roles,
    orgId: '00000000-0000-0000-0000-000000000001',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600
  };
  
  // Create a simple mock JWT (header.payload.signature)
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const payloadEncoded = btoa(JSON.stringify(payload));
  const signature = 'mock-signature';
  
  return `${header}.${payloadEncoded}.${signature}`;
};

describe('Role-Based Authentication Integration', () => {
  beforeEach(() => {
    localStorage.clear();
    jest.clearAllMocks();
  });

  describe('Admin User Flow', () => {
    it('shows admin dashboard when admin logs in', async () => {
      const user = userEvent.setup();
      const adminToken = createMockToken(['admin', 'user']);
      
      mockApi.login.mockResolvedValue({
        accessToken: adminToken,
        refreshToken: 'mock-refresh-token',
        user: { id: 1, username: 'admin', role: 'admin', roles: ['admin', 'user'] }
      });

      renderApp();

      // Navigate to login
      const loginButton = screen.getByText('Login');
      await user.click(loginButton);

      // Fill in admin credentials
      const usernameField = screen.getByLabelText(/username/i);
      await user.type(usernameField, 'admin');

      // Submit login
      const signInButton = screen.getByRole('button', { name: /sign in/i });
      await user.click(signInButton);

      // Wait for login to complete and dashboard to load
      await waitFor(() => {
        expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
      });

      // Verify admin-specific content
      expect(screen.getByText('Welcome back, Administrator! You have full system access.')).toBeInTheDocument();
      expect(screen.getByText('System Overview')).toBeInTheDocument();
      expect(screen.getByText('Administrative Actions')).toBeInTheDocument();
      expect(screen.getByText('Fleet Management')).toBeInTheDocument();
      expect(screen.getByText('User Management')).toBeInTheDocument();
    });

    it('shows admin menu items for admin users', async () => {
      const adminToken = createMockToken(['admin', 'user']);
      localStorage.setItem('authToken', adminToken);

      renderApp();

      // Verify admin menu items are visible
      expect(screen.getByText('Billing')).toBeInTheDocument();
      expect(screen.getByText('Fleet')).toBeInTheDocument();
      expect(screen.getByText('Integrations')).toBeInTheDocument();
      expect(screen.getByText('Salik')).toBeInTheDocument();
      expect(screen.getByText('Fines')).toBeInTheDocument();
    });
  });

  describe('Regular User Flow', () => {
    it('shows user dashboard when regular user logs in', async () => {
      const user = userEvent.setup();
      const userToken = createMockToken(['user']);
      
      mockApi.login.mockResolvedValue({
        accessToken: userToken,
        refreshToken: 'mock-refresh-token',
        user: { id: 1, username: 'demo', role: 'user', roles: ['user'] }
      });

      renderApp();

      // Navigate to login
      const loginButton = screen.getByText('Login');
      await user.click(loginButton);

      // Fill in user credentials
      const usernameField = screen.getByLabelText(/username/i);
      await user.type(usernameField, 'demo');

      // Submit login
      const signInButton = screen.getByRole('button', { name: /sign in/i });
      await user.click(signInButton);

      // Wait for login to complete and dashboard to load
      await waitFor(() => {
        expect(screen.getByText('User Dashboard')).toBeInTheDocument();
      });

      // Verify user-specific content
      expect(screen.getByText('Welcome back! Manage your agreements and track your progress.')).toBeInTheDocument();
      expect(screen.getByText('Your Performance')).toBeInTheDocument();
      expect(screen.getByText('Quick Actions')).toBeInTheDocument();
      expect(screen.getByText('Recent Activity')).toBeInTheDocument();
    });

    it('hides admin menu items for regular users', async () => {
      const userToken = createMockToken(['user']);
      localStorage.setItem('authToken', userToken);

      renderApp();

      // Verify admin menu items are NOT visible
      expect(screen.queryByText('Billing')).not.toBeInTheDocument();
      expect(screen.queryByText('Fleet')).not.toBeInTheDocument();
      expect(screen.queryByText('Integrations')).not.toBeInTheDocument();
      expect(screen.queryByText('Salik')).not.toBeInTheDocument();
      expect(screen.queryByText('Fines')).not.toBeInTheDocument();

      // But regular menu items should be visible
      expect(screen.getByText('STR Agreements')).toBeInTheDocument();
      expect(screen.getByText('Closure')).toBeInTheDocument();
    });
  });

  describe('Role-Based Route Protection', () => {
    it('prevents regular users from accessing admin routes', async () => {
      const userToken = createMockToken(['user']);
      localStorage.setItem('authToken', userToken);

      // Try to navigate directly to admin route
      window.history.pushState({}, 'Admin Page', '/admin/lease');
      
      renderApp();

      // Should be redirected to home page, not see admin content
      await waitFor(() => {
        expect(screen.getByText('User Dashboard')).toBeInTheDocument();
      });
      
      expect(screen.queryByText('Fleet Management Admin')).not.toBeInTheDocument();
    });

    it('allows admin users to access admin routes', async () => {
      const adminToken = createMockToken(['admin', 'user']);
      localStorage.setItem('authToken', adminToken);

      renderApp();

      // Admin should see admin dashboard
      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
    });
  });

  describe('Unauthenticated Users', () => {
    it('shows landing page for unauthenticated users', () => {
      renderApp();

      // Should show landing page content
      expect(screen.getByText('Not signed in')).toBeInTheDocument();
      expect(screen.getByText('Login')).toBeInTheDocument();
      
      // Should not show any dashboard
      expect(screen.queryByText('Admin Dashboard')).not.toBeInTheDocument();
      expect(screen.queryByText('User Dashboard')).not.toBeInTheDocument();
    });
  });
});
