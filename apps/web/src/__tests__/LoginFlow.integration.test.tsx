import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import Login from '../pages/Login';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

// Mock the API module
jest.mock('../api', () => ({
  api: {
    login: jest.fn()
  }
}));

// Mock react-router-dom hooks
const mockNavigate = jest.fn();
const mockLocation = { state: null };

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => mockLocation,
}));

const theme = createTheme();

const renderLogin = (onAuth = jest.fn()) => {
  return render(
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <Login onAuth={onAuth} />
      </ThemeProvider>
    </BrowserRouter>
  );
};

// Import the mocked API
import { api } from '../api';
const mockApi = api as jest.Mocked<typeof api>;

describe('Login Flow Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    mockApi.login.mockClear();
  });

  describe('Successful Login Scenarios', () => {
    it('successfully logs in demo user with fallback API', async () => {
      const mockOnAuth = jest.fn();
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************.test';
      
      mockApi.login.mockResolvedValueOnce({
        accessToken: mockToken,
        refreshToken: 'refresh-token'
      });

      renderLogin(mockOnAuth);

      // Fill in username
      const usernameInput = screen.getByLabelText(/username/i);
      fireEvent.change(usernameInput, { target: { value: 'demo' } });

      // Submit form
      const submitButton = screen.getByRole('button', { name: /sign in/i });
      fireEvent.click(submitButton);

      // Check loading state
      expect(screen.getByText(/signing in/i)).toBeInTheDocument();

      // Wait for success
      await waitFor(() => {
        expect(mockApi.login).toHaveBeenCalledWith('demo');
      });

      await waitFor(() => {
        expect(screen.getByText(/success/i)).toBeInTheDocument();
      });

      // Wait for navigation
      await waitFor(() => {
        expect(mockOnAuth).toHaveBeenCalledWith(mockToken);
        expect(mockNavigate).toHaveBeenCalledWith('/', { replace: true });
      });

      // Check token storage
      expect(localStorage.getItem('authToken')).toBe(mockToken);
    });

    it('successfully logs in admin user', async () => {
      const mockOnAuth = jest.fn();
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsInJvbGVzIjpbImFkbWluIl0sIm9yZ0lkIjoiMDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAxIiwiaWF0IjoxNjkwMDAwMDAwLCJleHAiOjE2OTAwMDg2NDB9.test';
      
      mockApi.login.mockResolvedValueOnce({
        accessToken: mockToken,
        refreshToken: 'refresh-token'
      });

      renderLogin(mockOnAuth);

      // Use admin quick access button
      const adminButton = screen.getByRole('button', { name: /admin user/i });
      fireEvent.click(adminButton);

      // Submit form
      const submitButton = screen.getByRole('button', { name: /sign in/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockApi.login).toHaveBeenCalledWith('admin');
      });

      await waitFor(() => {
        expect(mockOnAuth).toHaveBeenCalledWith(mockToken);
      });
    });

    it('handles remember me functionality', async () => {
      const mockOnAuth = jest.fn();
      const mockResponse = {
        accessToken: 'access-token',
        refreshToken: 'refresh-token'
      };
      
      mockApi.login.mockResolvedValueOnce(mockResponse);

      renderLogin(mockOnAuth);

      // Fill form and check remember me
      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'demo' } });
      fireEvent.click(screen.getByRole('checkbox', { name: /remember me/i }));
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(localStorage.getItem('authToken')).toBe('access-token');
        expect(localStorage.getItem('refreshToken')).toBe('refresh-token');
      });
    });
  });

  describe('Error Handling', () => {
    it('handles API timeout errors gracefully', async () => {
      const timeoutError = new Error('The operation was aborted due to timeout');
      mockApi.login.mockRejectedValueOnce(timeoutError);

      renderLogin();

      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'demo' } });
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(screen.getByText(/login request timed out/i)).toBeInTheDocument();
      });

      // Should not navigate on error
      expect(mockNavigate).not.toHaveBeenCalled();
    });

    it('handles authentication service unavailable', async () => {
      const unavailableError = new Error('Authentication service unavailable. Please try again.');
      mockApi.login.mockRejectedValueOnce(unavailableError);

      renderLogin();

      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'demo' } });
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(screen.getByText(/authentication service is temporarily unavailable/i)).toBeInTheDocument();
      });
    });

    it('handles 401 unauthorized errors', async () => {
      const unauthorizedError = new Error('401 Unauthorized');
      mockApi.login.mockRejectedValueOnce(unauthorizedError);

      renderLogin();

      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'invalid' } });
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(screen.getByText(/invalid username/i)).toBeInTheDocument();
      });
    });

    it('handles 500 server errors', async () => {
      const serverError = new Error('500 Internal Server Error');
      mockApi.login.mockRejectedValueOnce(serverError);

      renderLogin();

      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'demo' } });
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(screen.getByText(/server error/i)).toBeInTheDocument();
      });
    });

    it('clears errors when user starts typing again', async () => {
      const error = new Error('Login failed');
      mockApi.login.mockRejectedValueOnce(error);

      renderLogin();

      // Trigger error
      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'demo' } });
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(screen.getByText(/login failed/i)).toBeInTheDocument();
      });

      // Start typing again
      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'demo2' } });

      // Error should be cleared
      await waitFor(() => {
        expect(screen.queryByText(/login failed/i)).not.toBeInTheDocument();
      });
    });
  });

  describe('Form Validation', () => {
    it('shows error for empty username', async () => {
      renderLogin();

      const submitButton = screen.getByRole('button', { name: /sign in/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/username is required/i)).toBeInTheDocument();
      });

      // Should not call API
      expect(mockApi.login).not.toHaveBeenCalled();
    });

    it('shows error for username that is too short', async () => {
      renderLogin();

      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'ab' } });
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(screen.getByText(/username must be at least 3 characters/i)).toBeInTheDocument();
      });

      expect(mockApi.login).not.toHaveBeenCalled();
    });
  });

  describe('Quick Access Buttons', () => {
    it('populates demo user credentials', () => {
      renderLogin();

      const demoButton = screen.getByRole('button', { name: /demo user/i });
      fireEvent.click(demoButton);

      const usernameInput = screen.getByLabelText(/username/i) as HTMLInputElement;
      expect(usernameInput.value).toBe('demo');
    });

    it('populates admin user credentials', () => {
      renderLogin();

      const adminButton = screen.getByRole('button', { name: /admin user/i });
      fireEvent.click(adminButton);

      const usernameInput = screen.getByLabelText(/username/i) as HTMLInputElement;
      expect(usernameInput.value).toBe('admin');
    });
  });

  describe('Navigation and Redirects', () => {
    it('redirects to intended page after login', async () => {
      // Mock location state with intended destination
      mockLocation.state = { from: { pathname: '/admin/billing' } };

      const mockOnAuth = jest.fn();
      mockApi.login.mockResolvedValueOnce({
        accessToken: 'token',
        refreshToken: 'refresh'
      });

      renderLogin(mockOnAuth);

      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'admin' } });
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/admin/billing', { replace: true });
      });
    });

    it('redirects to home page by default', async () => {
      const mockOnAuth = jest.fn();
      mockApi.login.mockResolvedValueOnce({
        accessToken: 'token',
        refreshToken: 'refresh'
      });

      renderLogin(mockOnAuth);

      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'demo' } });
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/', { replace: true });
      });
    });
  });

  describe('Loading States', () => {
    it('shows loading state during login', async () => {
      let resolveLogin: (value: any) => void;
      const loginPromise = new Promise(resolve => {
        resolveLogin = resolve;
      });
      
      mockApi.login.mockReturnValueOnce(loginPromise);

      renderLogin();

      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'demo' } });
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

      // Should show loading state
      expect(screen.getByText(/signing in/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /signing in/i })).toBeDisabled();

      // Resolve the promise
      resolveLogin!({
        accessToken: 'token',
        refreshToken: 'refresh'
      });

      await waitFor(() => {
        expect(screen.getByText(/success/i)).toBeInTheDocument();
      });
    });

    it('disables form during loading', async () => {
      let resolveLogin: (value: any) => void;
      const loginPromise = new Promise(resolve => {
        resolveLogin = resolve;
      });
      
      mockApi.login.mockReturnValueOnce(loginPromise);

      renderLogin();

      fireEvent.change(screen.getByLabelText(/username/i), { target: { value: 'demo' } });
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

      // Form should be disabled
      expect(screen.getByLabelText(/username/i)).toBeDisabled();
      expect(screen.getByRole('checkbox')).toBeDisabled();

      // Resolve the promise
      resolveLogin!({
        accessToken: 'token',
        refreshToken: 'refresh'
      });

      await waitFor(() => {
        expect(screen.getByLabelText(/username/i)).not.toBeDisabled();
      });
    });
  });
});
