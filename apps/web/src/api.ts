type FetchOpts = {
  method?: string;
  body?: any;
  token?: string | null;
  orgId?: string;
};

async function request(base: string, path: string, opts: FetchOpts = {}) {
  const headers: Record<string, string> = { 'content-type': 'application/json' };
  if (opts.token) headers['authorization'] = `Bearer ${opts.token}`;
  if (opts.orgId) headers['x-org-id'] = opts.orgId;

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

  try {
    const res = await fetch(`${base}${path}`, {
      method: opts.method || 'GET',
      headers,
      body: opts.body ? JSON.stringify(opts.body) : undefined,
      signal: controller.signal
    });
    clearTimeout(timeoutId);
    if (!res.ok) throw new Error(`${res.status} ${res.statusText}`);
    return res.json();
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

export const env = {
  gateway: import.meta.env.VITE_GATEWAY_URL as string,
  orgId: import.meta.env.VITE_ORG_ID as string
};

// Robust login function with fallback
async function loginWithFallback(username: string) {
  try {
    // Try direct auth service (temporarily bypassing gateway)
    console.log('Attempting login via direct auth service...');
    return await request(env.gateway, '/auth/login', { method: 'POST', body: { username } });
  } catch (error) {
    console.warn('Direct auth service failed, trying with password...', error);
    try {
      // Fallback with demo password
      return await request(env.gateway, '/auth/login', { method: 'POST', body: { username, password: 'demo' } });
    } catch (fallbackError) {
      console.error('Both login attempts failed:', fallbackError);
      throw new Error('Authentication service unavailable. Please try again.');
    }
  }
}

export const api = {
  login: loginWithFallback,
  createCustomer: (token: string, body: any) =>
    request(env.gateway, '/api/sales/customers', { method: 'POST', body, token, orgId: env.orgId }),

  createVehicle: (token: string, body: any) =>
    request(env.gateway, '/api/fleet/vehicles', { method: 'POST', body, token, orgId: env.orgId }),

  // Align to current backend flow: reservation -> confirm -> agreement from reservation
  createAgreementSTR: async (
    token: string,
    body: { customerId: string; vehicleId: string; startDate: string; endDate?: string }
  ) => {
    const fromDate = body.startDate;
    const toDate = body.endDate || new Date(new Date(body.startDate).getTime() + 24 * 60 * 60 * 1000).toISOString();

    // 1) Create reservation
    const reservation = await request(env.gateway, '/api/sales/reservations', {
      method: 'POST',
      token,
      orgId: env.orgId,
      body: {
        customerId: body.customerId,
        vehicleId: body.vehicleId,
        fromDate,
        toDate,
        notes: 'web-ui STR reservation'
      }
    });

    // 2) Confirm reservation
    await request(env.gateway, `/api/sales/reservations/${reservation.id}/confirm`, {
      method: 'POST',
      token,
      orgId: env.orgId
    });

    // 3) Create agreement from reservation (STR)
    const created = await request(env.gateway, '/api/sales/agreements/from-reservation', {
      method: 'POST',
      token,
      orgId: env.orgId,
      body: {
        reservationId: reservation.id,
        agreementType: 'STR',
        securityDeposit: 0,
        insuranceRequired: true
      }
    });

    // Endpoint returns { agreement, pricing }
    return created;
  },

  // Map to handover (on-hire) with details collected from UI
  onHireAgreement: (
    token: string,
    id: string,
    body: {
      vehicleId: string;
      customerId: string;
      handoverDate: string;
      odometerReading: number;
      fuelLevel: number;
      overallRating?: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR';
      documentsProvided?: string[];
      handoverNotes?: string;
    }
  ) =>
    request(env.gateway, `/api/sales/agreements/${id}/handover`, {
      method: 'POST',
      token,
      orgId: env.orgId,
      body: {
        vehicleId: body.vehicleId,
        customerId: body.customerId,
        handoverDate: body.handoverDate,
        odometerReading: body.odometerReading,
        fuelLevel: body.fuelLevel,
        vehicleCondition: {
          exterior: [],
          interior: [],
          mechanical: [],
          accessories: [],
          overallRating: body.overallRating || 'EXCELLENT'
        },
        documentsProvided: body.documentsProvided || [],
        handoverNotes: body.handoverNotes || ''
      }
    }),

  // Map to return/close with details collected from UI
  closeAgreement: (
    token: string,
    id: string,
    body: {
      endDate: string;
      vehicleId?: string;
      odometerReading: number;
      fuelLevel: number;
      overallRating?: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR';
      damageReport?: any[];
      returnNotes?: string;
    }
  ) =>
    request(env.gateway, `/api/sales/agreements/${id}/return`, {
      method: 'POST',
      token,
      orgId: env.orgId,
      body: {
        vehicleId: body.vehicleId || '',
        returnDate: body.endDate,
        odometerReading: body.odometerReading,
        fuelLevel: body.fuelLevel,
        vehicleCondition: {
          exterior: [],
          interior: [],
          mechanical: [],
          accessories: [],
          overallRating: body.overallRating || 'GOOD'
        },
        damageReport: body.damageReport || [],
        returnNotes: body.returnNotes || ''
      }
    }),
  createInvoice: (token: string, body: any) => request(env.gateway, '/api/billing/invoices', { method: 'POST', body, token, orgId: env.orgId }),
  createReceipt: (token: string, body: any) => request(env.gateway, '/api/billing/receipts', { method: 'POST', body, token, orgId: env.orgId }),
  applyReceipt: (token: string, id: string, body: any) => request(env.gateway, `/api/billing/receipts/${id}/apply`, { method: 'POST', body, token, orgId: env.orgId }),

  // Admin: Salik and Fines
  salikImport: (token: string, records: any[]) => request(env.gateway, '/api/billing/salik/import', { method: 'POST', body: { source: 'WEB', records }, token, orgId: env.orgId }),
  salikList: (token: string) => request(env.gateway, '/api/billing/salik/charges', { method: 'GET', token, orgId: env.orgId }),
  finesImport: (token: string, records: any[]) => request(env.gateway, '/api/billing/fines/import', { method: 'POST', body: { source: 'WEB', records }, token, orgId: env.orgId }),
  finesList: (token: string) => request(env.gateway, '/api/billing/fines', { method: 'GET', token, orgId: env.orgId }),

  // Lease (LTR)
  leaseCreateQuote: (token: string, body: any) => request(env.gateway, '/api/sales/lease/quotes', { method: 'POST', body, token, orgId: env.orgId }),
  leaseList: (token: string) => request(env.gateway, '/api/sales/lease/quotes', { method: 'GET', token, orgId: env.orgId }),
  leaseApprove: (token: string, id: string) => request(env.gateway, `/api/sales/lease/quotes/${id}/approve`, { method: 'POST', token, orgId: env.orgId }),
  leaseConvert: (token: string, id: string) => request(env.gateway, `/api/sales/lease/quotes/${id}/convert`, { method: 'POST', token, orgId: env.orgId }),
  billingRecurringRun: (token: string) => request(env.gateway, '/api/billing/recurring/run', { method: 'POST', token, orgId: env.orgId })
  ,
  // Integrations exports
  listExports: (token: string, status?: string) => request(env.gateway, `/api/integrations/exports${status ? `?status=${encodeURIComponent(status)}` : ''}`, { method: 'GET', token, orgId: env.orgId }),
  runExports: (token: string) => request(env.gateway, '/api/integrations/exports/run', { method: 'POST', token, orgId: env.orgId })
};
