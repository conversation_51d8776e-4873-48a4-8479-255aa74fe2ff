// STR Module Type Definitions

export interface User {
  id: string;
  username: string;
  roles: string[];
  orgId: string;
}

export interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  type: 'CASH' | 'CORPORATE';
  status: 'ACTIVE' | 'HOLD' | 'BLOCKED';
  kycStatus: 'PENDING' | 'VERIFIED' | 'REJECTED';
  documents: Document[];
  createdAt: string;
  updatedAt: string;
}

export interface Vehicle {
  id: string;
  plateNumber: string;
  make: string;
  model: string;
  year: number;
  category: string;
  status: 'AVAILABLE' | 'ON_HIRE' | 'MAINTENANCE' | 'RETIRED';
  location: string;
  businessType: 'STR' | 'LTR' | 'BOTH';
  dailyRate: number;
  weeklyRate?: number;
  monthlyRate?: number;
  features: string[];
  images: string[];
}

export interface STRAgreement {
  id: string;
  agreementNumber: string;
  customerId: string;
  customer?: Customer;
  vehicleId: string;
  vehicle?: Vehicle;
  
  // Dates and Duration
  startDate: string;
  endDate: string;
  actualStartDate?: string;
  actualEndDate?: string;
  duration: number; // in days
  
  // Status and Workflow
  status: 'DRAFT' | 'CONFIRMED' | 'ON_HIRE' | 'ACTIVE' | 'CLOSURE_IN_PROGRESS' | 'CLOSED' | 'CANCELLED';
  workflowStage: 'CREATED' | 'CONFIRMED' | 'HANDOVER' | 'ACTIVE' | 'RETURN_REQUESTED' | 'INSPECTION' | 'SETTLEMENT' | 'CLOSED';
  
  // Pricing
  pricing: PricingBreakdown;
  
  // Handover Details
  handoverDetails?: HandoverDetails;
  returnDetails?: ReturnDetails;
  
  // Documents and Compliance
  documents: Document[];
  requiredDocuments: string[];
  
  // Audit and Metadata
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
  auditLog: AuditEntry[];
  
  // Business Rules
  allowExtension: boolean;
  allowEarlyReturn: boolean;
  requiresDeposit: boolean;
  
  // Financial
  totalAmount: number;
  paidAmount: number;
  outstandingAmount: number;
  depositAmount: number;
  refundableDeposit: number;
}

export interface PricingBreakdown {
  baseAmount: number;
  additionalCharges: number;
  discounts: number;
  taxes: number;
  totalAmount: number;
  currency: string;
  breakdown: PricingLine[];
}

export interface PricingLine {
  description: string;
  category: 'BASE' | 'INSURANCE' | 'ACCESSORIES' | 'EXTRAS' | 'FEES' | 'TAXES';
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  taxable: boolean;
}

export interface HandoverDetails {
  handoverDate: string;
  handoverLocation: string;
  odometerReading: number;
  fuelLevel: number; // 0-100
  vehicleCondition: VehicleCondition;
  documentsProvided: string[];
  handoverNotes: string;
  handoverPhotos: string[];
  handoverBy: string;
  customerSignature?: string;
  agentSignature?: string;
}

export interface ReturnDetails {
  returnDate: string;
  returnLocation: string;
  odometerReading: number;
  fuelLevel: number;
  vehicleCondition: VehicleCondition;
  damageReport: DamageItem[];
  returnNotes: string;
  returnPhotos: string[];
  returnBy: string;
  inspectedBy?: string;
  customerSignature?: string;
  agentSignature?: string;
}

export interface VehicleCondition {
  exterior: ConditionItem[];
  interior: ConditionItem[];
  mechanical: ConditionItem[];
  accessories: ConditionItem[];
  overallRating: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR';
  notes?: string;
}

export interface ConditionItem {
  area: string;
  condition: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR' | 'DAMAGED';
  notes?: string;
  photos?: string[];
}

export interface DamageItem {
  id: string;
  area: string;
  description: string;
  severity: 'MINOR' | 'MODERATE' | 'MAJOR';
  estimatedCost: number;
  photos: string[];
  approved: boolean;
  waived: boolean;
  notes?: string;
}

export interface Document {
  id: string;
  type: 'RA_PDF' | 'ON_HIRE_CHECKLIST' | 'OFF_HIRE_CHECKLIST' | 'INVOICE' | 'RECEIPT' | 'ID_COPY' | 'LICENSE_COPY' | 'INSURANCE' | 'OTHER';
  name: string;
  url: string;
  uploadedBy: string;
  uploadedAt: string;
  required: boolean;
  verified: boolean;
}

export interface AuditEntry {
  id: string;
  timestamp: string;
  userId: string;
  userName: string;
  action: string;
  details: string;
  oldValue?: any;
  newValue?: any;
  ipAddress?: string;
  userAgent?: string;
}

export interface Payment {
  id: string;
  agreementId: string;
  type: 'CASH' | 'CARD' | 'BANK_TRANSFER' | 'LPO';
  amount: number;
  currency: string;
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'REFUNDED';
  reference: string;
  processedAt?: string;
  processedBy?: string;
  notes?: string;
}

export interface ExtensionRequest {
  id: string;
  agreementId: string;
  requestedBy: string;
  requestedAt: string;
  newEndDate: string;
  reason: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  approvedBy?: string;
  approvedAt?: string;
  rejectionReason?: string;
  additionalCharges?: number;
}

export interface ReturnRequest {
  id: string;
  agreementId: string;
  requestedBy: string;
  requestedAt: string;
  proposedReturnDate: string;
  returnLocation: string;
  estimatedOdometer: number;
  estimatedFuelLevel: number;
  photos: string[];
  notes: string;
  status: 'PENDING' | 'APPROVED' | 'PROCESSING' | 'COMPLETED';
  processedBy?: string;
  processedAt?: string;
}

// UI State Types
export interface STRFormData {
  // Step 1: Customer
  customerId: string;
  customerData?: Partial<Customer>;
  
  // Step 2: Vehicle
  vehicleId: string;
  vehicleData?: Partial<Vehicle>;
  
  // Step 3: Agreement Details
  startDate: string;
  endDate: string;
  pickupLocation: string;
  returnLocation: string;
  handoverNotes: string;
  
  // Step 4: Pricing
  pricingData?: PricingBreakdown;
  paymentMethod: 'CASH' | 'CARD' | 'LPO';
  depositAmount: number;
  
  // Step 5: Review
  termsAccepted: boolean;
  documentsUploaded: string[];
}

export interface STRFilters {
  status?: string[];
  dateRange?: {
    start: string;
    end: string;
  };
  customerId?: string;
  vehicleId?: string;
  location?: string;
  searchTerm?: string;
}

export interface STRListItem {
  id: string;
  agreementNumber: string;
  customerName: string;
  vehiclePlate: string;
  vehicleModel: string;
  startDate: string;
  endDate: string;
  status: string;
  workflowStage: string;
  totalAmount: number;
  outstandingAmount: number;
  canExtend: boolean;
  canReturn: boolean;
  canPay: boolean;
}

// Role-based permissions
export interface STRPermissions {
  canCreate: boolean;
  canEdit: boolean;
  canClose: boolean;
  canViewAll: boolean;
  canViewOwn: boolean;
  canApproveExtensions: boolean;
  canOverridePricing: boolean;
  canWaiveFees: boolean;
  canAccessFinancials: boolean;
  canManageDocuments: boolean;
  canPostAccounting: boolean;
}

// Component Props Types
export interface STRWizardProps {
  onComplete: (agreement: STRAgreement) => void;
  onCancel: () => void;
  initialData?: Partial<STRFormData>;
}

export interface AgreementDetailProps {
  agreementId: string;
  mode: 'view' | 'edit';
  userRole: 'customer' | 'agent' | 'admin';
  onUpdate?: (agreement: STRAgreement) => void;
}

export interface ClosureWorkspaceProps {
  agreementId: string;
  onComplete: (agreement: STRAgreement) => void;
  onCancel: () => void;
}

export interface CustomerPortalProps {
  customerId: string;
  agreements: STRListItem[];
  onViewAgreement: (id: string) => void;
  onRequestExtension: (id: string) => void;
  onStartReturn: (id: string) => void;
  onMakePayment: (id: string) => void;
}

// Closure-specific types
export interface ClosureFormData {
  // Step 1: Agreement Lookup & Validation
  agreementId: string;
  agreement?: STRAgreement;

  // Step 2: Vehicle Check
  returnDate: string;
  returnTime: string;
  returnLocation: string;
  odometerReading: number;
  fuelLevel: number;
  vehicleCondition: VehicleCondition;
  damageReport: DamageItem[];
  returnPhotos: string[];

  // Step 3: Additional Charges
  additionalCharges: AdditionalCharge[];
  salikCharges: SalikCharge[];
  fineCharges: FineCharge[];

  // Step 4: Settlement
  settlementSummary?: SettlementSummary;
  paymentMethod?: 'CASH' | 'CARD' | 'BANK_TRANSFER';
  refundMethod?: 'CASH' | 'BANK_TRANSFER' | 'ORIGINAL_METHOD';

  // Step 5: Final Closure
  documentsGenerated: string[];
  closureNotes: string;
  managerApproval?: boolean;
}

export interface AdditionalCharge {
  id: string;
  type: 'DAMAGE' | 'FUEL' | 'MILEAGE' | 'CLEANING' | 'LATE_RETURN' | 'SALIK' | 'FINE' | 'OTHER';
  description: string;
  amount: number;
  quantity: number;
  unitPrice: number;
  taxable: boolean;
  approved: boolean;
  approvedBy?: string;
  waived: boolean;
  waivedBy?: string;
  waivedReason?: string;
  evidence?: string[]; // Photo URLs
  notes?: string;
}

export interface SalikCharge {
  id: string;
  plateNumber: string;
  gateLocation: string;
  passageDate: string;
  amount: number;
  status: 'PENDING' | 'CONFIRMED' | 'DISPUTED';
  reference: string;
}

export interface FineCharge {
  id: string;
  plateNumber: string;
  violationType: string;
  violationDate: string;
  amount: number;
  location: string;
  status: 'PENDING' | 'CONFIRMED' | 'DISPUTED' | 'PAID';
  reference: string;
  dueDate: string;
}

export interface SettlementSummary {
  // Original Agreement
  originalAmount: number;
  paidAmount: number;

  // Additional Charges
  additionalCharges: number;
  salikCharges: number;
  fineCharges: number;

  // Deductions
  discounts: number;
  waivedCharges: number;

  // Deposits
  securityDeposit: number;
  refundableDeposit: number;

  // Final Calculation
  totalCharges: number;
  totalCredits: number;
  netAmount: number; // Positive = customer owes, Negative = refund due

  // Tax Breakdown
  taxableAmount: number;
  taxAmount: number;
  totalWithTax: number;
}

export interface ClosureDocument {
  id: string;
  type: 'OFF_HIRE_CHECKLIST' | 'CLOSURE_INVOICE' | 'SETTLEMENT_RECEIPT' | 'REFUND_RECEIPT';
  name: string;
  url: string;
  generatedAt: string;
  generatedBy: string;
}

export interface CustomerReturnRequest {
  id: string;
  agreementId: string;
  customerId: string;

  // Return Details
  proposedReturnDate: string;
  proposedReturnTime: string;
  returnLocation: string;

  // Vehicle Condition
  estimatedOdometer: number;
  estimatedFuelLevel: number;
  reportedDamages: string[];
  returnPhotos: string[];
  returnVideos: string[];

  // Customer Declaration
  customerNotes: string;
  termsAccepted: boolean;
  declarationSigned: boolean;

  // Status and Processing
  status: 'SUBMITTED' | 'UNDER_REVIEW' | 'APPROVED' | 'PROCESSING' | 'COMPLETED' | 'REJECTED';
  submittedAt: string;
  processedBy?: string;
  processedAt?: string;
  rejectionReason?: string;

  // Estimated Charges (for customer preview)
  estimatedCharges: {
    fuelTopUp: number;
    mileageOverage: number;
    estimatedTotal: number;
  };
}

// Closure Permissions
export interface ClosurePermissions {
  canInitiateClosure: boolean;
  canViewAllClosures: boolean;
  canViewOwnClosures: boolean;
  canAddCharges: boolean;
  canWaiveCharges: boolean;
  canOverrideCharges: boolean;
  canProcessSettlement: boolean;
  canGenerateDocuments: boolean;
  canPostAccounting: boolean;
  canApproveExceptions: boolean;
}

// Component Props
export interface ClosureWorkspaceProps {
  agreementId?: string;
  userRoles: string[];
  token: string;
  onComplete: (closedAgreement: STRAgreement) => void;
  onCancel: () => void;
}

export interface CustomerReturnWizardProps {
  agreementId: string;
  customerId: string;
  token: string;
  onComplete: (returnRequest: CustomerReturnRequest) => void;
  onCancel: () => void;
}

export interface DamageChecklistProps {
  vehicleCondition: VehicleCondition;
  damageReport: DamageItem[];
  onUpdate: (condition: VehicleCondition, damages: DamageItem[]) => void;
  editable: boolean;
  showPhotos?: boolean;
}

export interface ChargesTableProps {
  charges: AdditionalCharge[];
  onUpdate: (charges: AdditionalCharge[]) => void;
  editable: boolean;
  userRoles: string[];
  showApproval?: boolean;
}

export interface SettlementPanelProps {
  settlement: SettlementSummary;
  onUpdate: (settlement: SettlementSummary) => void;
  editable: boolean;
  showPaymentMethods?: boolean;
}

export interface DocumentGeneratorProps {
  agreementId: string;
  closureData: ClosureFormData;
  onGenerate: (documents: ClosureDocument[]) => void;
  userRoles: string[];
}
