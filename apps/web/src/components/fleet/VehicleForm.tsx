import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Grid,
  TextField,
  Button,
  Stack,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Divider,
  Switch,
  FormControlLabel,
  Autocomplete,
  InputAdornment
} from '@mui/material';
import {
  Save,
  Cancel,
  DirectionsCar,
  AttachMoney,
  CalendarToday,
  LocationOn,
  Build,
  Description
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface VehicleFormProps {
  token: string;
  userRoles: string[];
  onCancel: () => void;
  onSave: () => void;
  editVehicle?: any; // For editing existing vehicles
}

const VehicleForm: React.FC<VehicleFormProps> = ({ 
  token, 
  userRoles, 
  onCancel, 
  onSave, 
  editVehicle 
}) => {
  const [formData, setFormData] = useState({
    vehicleId: editVehicle?.id || '',
    registrationPlate: editVehicle?.registrationPlate || '',
    chassisNumber: editVehicle?.chassisNumber || '',
    make: editVehicle?.make || '',
    model: editVehicle?.model || '',
    year: editVehicle?.year || new Date().getFullYear(),
    category: editVehicle?.category || '',
    businessType: editVehicle?.businessType || 'STR',
    branch: editVehicle?.branch || '',
    color: editVehicle?.color || '',
    fuelType: editVehicle?.fuelType || 'Petrol',
    transmission: editVehicle?.transmission || 'Automatic',
    engineCapacity: editVehicle?.engineCapacity || '',
    seatingCapacity: editVehicle?.seatingCapacity || 5,
    
    // Financial Information
    acquisitionCost: editVehicle?.acquisitionCost || '',
    acquisitionDate: editVehicle?.acquisitionDate || '',
    depreciationRate: editVehicle?.depreciationRate || 20,
    monthlyInsuranceCost: editVehicle?.monthlyInsuranceCost || '',
    
    // Documents
    insuranceProvider: editVehicle?.insuranceProvider || '',
    insurancePolicyNumber: editVehicle?.insurancePolicyNumber || '',
    insuranceStartDate: editVehicle?.insuranceStartDate || '',
    insuranceExpiryDate: editVehicle?.insuranceExpiryDate || '',
    registrationExpiryDate: editVehicle?.registrationExpiryDate || '',
    
    // Operational
    initialMileage: editVehicle?.initialMileage || 0,
    status: editVehicle?.status || 'available',
    notes: editVehicle?.notes || '',
    
    // Features
    hasGPS: editVehicle?.hasGPS || true,
    hasSalik: editVehicle?.hasSalik || true,
    hasInsurance: editVehicle?.hasInsurance || true,
    isActive: editVehicle?.isActive !== false
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);

  const isEditing = !!editVehicle;

  // Mock data - in real implementation, these would come from API
  const makes = ['Toyota', 'Honda', 'Nissan', 'Ford', 'Hyundai', 'Kia', 'BMW', 'Mercedes-Benz', 'Audi', 'Volkswagen'];
  const categories = ['Sedan', 'SUV', 'Hatchback', 'Coupe', 'Convertible', 'Pickup', 'Van', 'Luxury'];
  const branches = ['Dubai Main', 'Abu Dhabi', 'Sharjah', 'Ajman', 'Ras Al Khaimah', 'Fujairah', 'Umm Al Quwain'];
  const insuranceProviders = ['AXA', 'Oman Insurance', 'Dubai Insurance', 'Orient Insurance', 'Al Sagr Insurance'];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Required fields validation
    if (!formData.vehicleId) newErrors.vehicleId = 'Vehicle ID is required';
    if (!formData.registrationPlate) newErrors.registrationPlate = 'Registration plate is required';
    if (!formData.chassisNumber) newErrors.chassisNumber = 'Chassis number is required';
    if (!formData.make) newErrors.make = 'Make is required';
    if (!formData.model) newErrors.model = 'Model is required';
    if (!formData.category) newErrors.category = 'Category is required';
    if (!formData.branch) newErrors.branch = 'Branch is required';

    // Date validations
    if (formData.insuranceExpiryDate) {
      const expiryDate = new Date(formData.insuranceExpiryDate);
      const today = new Date();
      if (expiryDate <= today) {
        newErrors.insuranceExpiryDate = 'Insurance expiry date must be in the future';
      }
    }

    if (formData.registrationExpiryDate) {
      const expiryDate = new Date(formData.registrationExpiryDate);
      const today = new Date();
      if (expiryDate <= today) {
        newErrors.registrationExpiryDate = 'Registration expiry date must be in the future';
      }
    }

    // Numeric validations
    if (formData.year < 1990 || formData.year > new Date().getFullYear() + 1) {
      newErrors.year = 'Please enter a valid year';
    }

    if (formData.seatingCapacity < 1 || formData.seatingCapacity > 50) {
      newErrors.seatingCapacity = 'Seating capacity must be between 1 and 50';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      // API call to save vehicle
      console.log(isEditing ? 'Updating vehicle...' : 'Creating vehicle...', formData);
      // await api.saveVehicle(token, formData);
      onSave();
    } catch (error) {
      console.error('Error saving vehicle:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: string) => {
    const num = parseFloat(amount);
    return isNaN(num) ? '' : new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: 'AED'
    }).format(num);
  };

  return (
    <Box>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
          <Typography variant="h5" sx={{ fontWeight: 600 }}>
            {isEditing ? `Edit Vehicle - ${formData.vehicleId}` : 'Add New Vehicle'}
          </Typography>
          <Stack direction="row" spacing={2}>
            <Button
              variant="outlined"
              startIcon={<Cancel />}
              onClick={onCancel}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              startIcon={<Save />}
              onClick={handleSubmit}
              disabled={loading}
            >
              {loading ? 'Saving...' : (isEditing ? 'Update Vehicle' : 'Add Vehicle')}
            </Button>
          </Stack>
        </Stack>

        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 3 }}>
                  <DirectionsCar color="primary" />
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Basic Information
                  </Typography>
                </Stack>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      label="Vehicle ID"
                      value={formData.vehicleId}
                      onChange={(e) => setFormData({ ...formData, vehicleId: e.target.value })}
                      fullWidth
                      required
                      error={!!errors.vehicleId}
                      helperText={errors.vehicleId}
                      disabled={isEditing}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      label="Registration Plate"
                      value={formData.registrationPlate}
                      onChange={(e) => setFormData({ ...formData, registrationPlate: e.target.value })}
                      fullWidth
                      required
                      error={!!errors.registrationPlate}
                      helperText={errors.registrationPlate}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      label="Chassis Number"
                      value={formData.chassisNumber}
                      onChange={(e) => setFormData({ ...formData, chassisNumber: e.target.value })}
                      fullWidth
                      required
                      error={!!errors.chassisNumber}
                      helperText={errors.chassisNumber}
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={4}>
                    <Autocomplete
                      options={makes}
                      value={formData.make}
                      onChange={(_, value) => setFormData({ ...formData, make: value || '' })}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Make"
                          required
                          error={!!errors.make}
                          helperText={errors.make}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      label="Model"
                      value={formData.model}
                      onChange={(e) => setFormData({ ...formData, model: e.target.value })}
                      fullWidth
                      required
                      error={!!errors.model}
                      helperText={errors.model}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      label="Year"
                      type="number"
                      value={formData.year}
                      onChange={(e) => setFormData({ ...formData, year: parseInt(e.target.value) })}
                      fullWidth
                      required
                      error={!!errors.year}
                      helperText={errors.year}
                      inputProps={{ min: 1990, max: new Date().getFullYear() + 1 }}
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={4}>
                    <FormControl fullWidth required error={!!errors.category}>
                      <InputLabel>Category</InputLabel>
                      <Select
                        value={formData.category}
                        onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                        label="Category"
                      >
                        {categories.map(category => (
                          <MenuItem key={category} value={category}>{category}</MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>Business Type</InputLabel>
                      <Select
                        value={formData.businessType}
                        onChange={(e) => setFormData({ ...formData, businessType: e.target.value })}
                        label="Business Type"
                      >
                        <MenuItem value="STR">STR (Short Term Rental)</MenuItem>
                        <MenuItem value="LTR">LTR (Long Term Rental)</MenuItem>
                        <MenuItem value="BOTH">Both STR & LTR</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <FormControl fullWidth required error={!!errors.branch}>
                      <InputLabel>Branch</InputLabel>
                      <Select
                        value={formData.branch}
                        onChange={(e) => setFormData({ ...formData, branch: e.target.value })}
                        label="Branch"
                      >
                        {branches.map(branch => (
                          <MenuItem key={branch} value={branch}>{branch}</MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      label="Color"
                      value={formData.color}
                      onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                      fullWidth
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>Fuel Type</InputLabel>
                      <Select
                        value={formData.fuelType}
                        onChange={(e) => setFormData({ ...formData, fuelType: e.target.value })}
                        label="Fuel Type"
                      >
                        <MenuItem value="Petrol">Petrol</MenuItem>
                        <MenuItem value="Diesel">Diesel</MenuItem>
                        <MenuItem value="Hybrid">Hybrid</MenuItem>
                        <MenuItem value="Electric">Electric</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>Transmission</InputLabel>
                      <Select
                        value={formData.transmission}
                        onChange={(e) => setFormData({ ...formData, transmission: e.target.value })}
                        label="Transmission"
                      >
                        <MenuItem value="Automatic">Automatic</MenuItem>
                        <MenuItem value="Manual">Manual</MenuItem>
                        <MenuItem value="CVT">CVT</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      label="Engine Capacity"
                      value={formData.engineCapacity}
                      onChange={(e) => setFormData({ ...formData, engineCapacity: e.target.value })}
                      fullWidth
                      placeholder="e.g., 2.0L"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      label="Seating Capacity"
                      type="number"
                      value={formData.seatingCapacity}
                      onChange={(e) => setFormData({ ...formData, seatingCapacity: parseInt(e.target.value) })}
                      fullWidth
                      error={!!errors.seatingCapacity}
                      helperText={errors.seatingCapacity}
                      inputProps={{ min: 1, max: 50 }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      label="Initial Mileage"
                      type="number"
                      value={formData.initialMileage}
                      onChange={(e) => setFormData({ ...formData, initialMileage: parseInt(e.target.value) })}
                      fullWidth
                      InputProps={{
                        endAdornment: <InputAdornment position="end">km</InputAdornment>
                      }}
                      inputProps={{ min: 0 }}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Financial Information */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 3 }}>
                  <AttachMoney color="primary" />
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Financial Information
                  </Typography>
                </Stack>

                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      label="Acquisition Cost"
                      type="number"
                      value={formData.acquisitionCost}
                      onChange={(e) => setFormData({ ...formData, acquisitionCost: e.target.value })}
                      fullWidth
                      InputProps={{
                        startAdornment: <InputAdornment position="start">AED</InputAdornment>
                      }}
                      inputProps={{ min: 0, step: 0.01 }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      label="Acquisition Date"
                      type="date"
                      value={formData.acquisitionDate}
                      onChange={(e) => setFormData({ ...formData, acquisitionDate: e.target.value })}
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      label="Depreciation Rate"
                      type="number"
                      value={formData.depreciationRate}
                      onChange={(e) => setFormData({ ...formData, depreciationRate: parseFloat(e.target.value) })}
                      fullWidth
                      InputProps={{
                        endAdornment: <InputAdornment position="end">% per year</InputAdornment>
                      }}
                      inputProps={{ min: 0, max: 100, step: 0.1 }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      label="Monthly Insurance Cost"
                      type="number"
                      value={formData.monthlyInsuranceCost}
                      onChange={(e) => setFormData({ ...formData, monthlyInsuranceCost: e.target.value })}
                      fullWidth
                      InputProps={{
                        startAdornment: <InputAdornment position="start">AED</InputAdornment>
                      }}
                      inputProps={{ min: 0, step: 0.01 }}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Documents & Insurance */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 3 }}>
                  <Description color="primary" />
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Documents & Insurance
                  </Typography>
                </Stack>

                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6} md={4}>
                    <Autocomplete
                      options={insuranceProviders}
                      value={formData.insuranceProvider}
                      onChange={(_, value) => setFormData({ ...formData, insuranceProvider: value || '' })}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Insurance Provider"
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      label="Insurance Policy Number"
                      value={formData.insurancePolicyNumber}
                      onChange={(e) => setFormData({ ...formData, insurancePolicyNumber: e.target.value })}
                      fullWidth
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      label="Insurance Start Date"
                      type="date"
                      value={formData.insuranceStartDate}
                      onChange={(e) => setFormData({ ...formData, insuranceStartDate: e.target.value })}
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      label="Insurance Expiry Date"
                      type="date"
                      value={formData.insuranceExpiryDate}
                      onChange={(e) => setFormData({ ...formData, insuranceExpiryDate: e.target.value })}
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      error={!!errors.insuranceExpiryDate}
                      helperText={errors.insuranceExpiryDate}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      label="Registration Expiry Date"
                      type="date"
                      value={formData.registrationExpiryDate}
                      onChange={(e) => setFormData({ ...formData, registrationExpiryDate: e.target.value })}
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      error={!!errors.registrationExpiryDate}
                      helperText={errors.registrationExpiryDate}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Features & Status */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 3 }}>
                  <Build color="primary" />
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Features & Status
                  </Typography>
                </Stack>

                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>Status</InputLabel>
                      <Select
                        value={formData.status}
                        onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                        label="Status"
                      >
                        <MenuItem value="available">Available</MenuItem>
                        <MenuItem value="maintenance">Maintenance</MenuItem>
                        <MenuItem value="accident">Accident</MenuItem>
                        <MenuItem value="disposed">Disposed</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <Stack spacing={2}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.hasGPS}
                            onChange={(e) => setFormData({ ...formData, hasGPS: e.target.checked })}
                          />
                        }
                        label="GPS Tracking"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.hasSalik}
                            onChange={(e) => setFormData({ ...formData, hasSalik: e.target.checked })}
                          />
                        }
                        label="Salik Tag"
                      />
                    </Stack>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <Stack spacing={2}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.hasInsurance}
                            onChange={(e) => setFormData({ ...formData, hasInsurance: e.target.checked })}
                          />
                        }
                        label="Insurance Coverage"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.isActive}
                            onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                          />
                        }
                        label="Active Vehicle"
                      />
                    </Stack>
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Notes"
                      multiline
                      rows={4}
                      value={formData.notes}
                      onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                      fullWidth
                      placeholder="Additional notes about the vehicle..."
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </motion.div>
    </Box>
  );
};

export default VehicleForm;
