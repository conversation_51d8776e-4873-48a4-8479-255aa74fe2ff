import React from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  <PERSON>ack,
  Avatar,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
  useTheme,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  DirectionsCar,
  Build,
  Warning,
  CheckCircle,
  Schedule,
  TrendingUp,
  TrendingDown,
  Refresh,
  Notifications,
  LocalGasStation,
  Speed,
  Assignment,
  AttachMoney
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface FleetDashboardProps {
  token: string;
  userRoles: string[];
  alerts: {
    expiringInsurance: number;
    expiringRegistration: number;
    maintenanceDue: number;
    accidentReports: number;
  };
}

const FleetDashboard: React.FC<FleetDashboardProps> = ({ token, userRoles, alerts }) => {
  const theme = useTheme();
  const isAdmin = userRoles.includes('admin');

  // Mock data - in real implementation, this would come from API
  const fleetMetrics = {
    totalVehicles: 156,
    availableVehicles: 89,
    onHireVehicles: 52,
    maintenanceVehicles: 12,
    accidentVehicles: 3,
    utilizationRate: 73.5,
    averageAge: 2.8,
    totalMileage: 2450000,
    monthlyRevenue: 485000
  };

  const statusBreakdown = [
    { status: 'Available', count: fleetMetrics.availableVehicles, color: 'success', percentage: 57 },
    { status: 'On Hire', count: fleetMetrics.onHireVehicles, color: 'primary', percentage: 33 },
    { status: 'Maintenance', count: fleetMetrics.maintenanceVehicles, color: 'warning', percentage: 8 },
    { status: 'Accident', count: fleetMetrics.accidentVehicles, color: 'error', percentage: 2 }
  ];

  const recentActivity = [
    { type: 'status_change', vehicle: 'VEH-001', action: 'Available → On Hire', time: '2 hours ago', user: 'admin' },
    { type: 'maintenance', vehicle: 'VEH-045', action: 'Scheduled maintenance completed', time: '4 hours ago', user: 'mechanic' },
    { type: 'insurance', vehicle: 'VEH-023', action: 'Insurance renewed', time: '6 hours ago', user: 'admin' },
    { type: 'accident', vehicle: 'VEH-078', action: 'Accident report filed', time: '1 day ago', user: 'agent' }
  ];

  const upcomingExpirations = [
    { vehicle: 'VEH-012', type: 'Insurance', expiryDate: '2024-02-15', daysLeft: 5 },
    { vehicle: 'VEH-034', type: 'Registration', expiryDate: '2024-02-18', daysLeft: 8 },
    { vehicle: 'VEH-056', type: 'Insurance', expiryDate: '2024-02-20', daysLeft: 10 },
    { vehicle: 'VEH-089', type: 'Registration', expiryDate: '2024-02-25', daysLeft: 15 }
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'status_change': return <DirectionsCar />;
      case 'maintenance': return <Build />;
      case 'insurance': return <CheckCircle />;
      case 'accident': return <Warning />;
      default: return <Schedule />;
    }
  };

  const getExpiryColor = (daysLeft: number) => {
    if (daysLeft <= 7) return 'error';
    if (daysLeft <= 14) return 'warning';
    return 'success';
  };

  const StatCard = ({ title, value, subtitle, icon, color, trend }: any) => (
    <Card>
      <CardContent>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Avatar sx={{ bgcolor: `${color}.main`, width: 56, height: 56 }}>
            {icon}
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h4" sx={{ fontWeight: 600 }}>
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="caption" color="text.secondary">
                {subtitle}
              </Typography>
            )}
            {trend && (
              <Stack direction="row" alignItems="center" spacing={1} sx={{ mt: 1 }}>
                {trend > 0 ? <TrendingUp color="success" fontSize="small" /> : <TrendingDown color="error" fontSize="small" />}
                <Typography 
                  variant="caption" 
                  color={trend > 0 ? 'success.main' : 'error.main'}
                  sx={{ fontWeight: 500 }}
                >
                  {Math.abs(trend)}% from last month
                </Typography>
              </Stack>
            )}
          </Box>
        </Stack>
      </CardContent>
    </Card>
  );

  return (
    <Box>
      {/* Alerts Section */}
      {(alerts.expiringInsurance > 0 || alerts.expiringRegistration > 0 || alerts.maintenanceDue > 0 || alerts.accidentReports > 0) && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Alert 
            severity="warning" 
            icon={<Notifications />}
            sx={{ mb: 3 }}
            action={
              <Tooltip title="View all alerts">
                <IconButton size="small" color="inherit">
                  <Refresh />
                </IconButton>
              </Tooltip>
            }
          >
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              Fleet Alerts: {alerts.expiringInsurance} insurance expiring, {alerts.expiringRegistration} registration expiring, 
              {alerts.maintenanceDue} maintenance due, {alerts.accidentReports} accident reports pending
            </Typography>
          </Alert>
        </motion.div>
      )}

      {/* Key Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          Fleet Overview
        </Typography>
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Vehicles"
              value={fleetMetrics.totalVehicles}
              subtitle="Active fleet size"
              icon={<DirectionsCar />}
              color="primary"
              trend={5.2}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Utilization Rate"
              value={`${fleetMetrics.utilizationRate}%`}
              subtitle="Vehicles on hire"
              icon={<TrendingUp />}
              color="success"
              trend={2.8}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Available Now"
              value={fleetMetrics.availableVehicles}
              subtitle="Ready for rental"
              icon={<CheckCircle />}
              color="info"
              trend={-1.5}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Monthly Revenue"
              value={`AED ${fleetMetrics.monthlyRevenue.toLocaleString()}`}
              subtitle="From fleet operations"
              icon={<AttachMoney />}
              color="warning"
              trend={8.3}
            />
          </Grid>
        </Grid>
      </motion.div>

      <Grid container spacing={3}>
        {/* Fleet Status Breakdown */}
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Fleet Status Distribution
                  </Typography>
                  <Tooltip title="Refresh data">
                    <IconButton size="small">
                      <Refresh />
                    </IconButton>
                  </Tooltip>
                </Stack>
                <Stack spacing={3}>
                  {statusBreakdown.map((status, index) => (
                    <Box key={index}>
                      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Chip 
                            label={status.status} 
                            color={status.color as any} 
                            size="small" 
                          />
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {status.count} vehicles
                          </Typography>
                        </Stack>
                        <Typography variant="body2" color="text.secondary">
                          {status.percentage}%
                        </Typography>
                      </Stack>
                      <LinearProgress 
                        variant="determinate" 
                        value={status.percentage} 
                        sx={{ 
                          height: 8, 
                          borderRadius: 4,
                          bgcolor: 'grey.200',
                          '& .MuiLinearProgress-bar': {
                            borderRadius: 4,
                            bgcolor: `${status.color}.main`
                          }
                        }}
                      />
                    </Box>
                  ))}
                </Stack>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                  Recent Activity
                </Typography>
                <List dense>
                  {recentActivity.map((activity, index) => (
                    <React.Fragment key={index}>
                      <ListItem>
                        <ListItemIcon>
                          <Avatar sx={{ 
                            bgcolor: activity.type === 'accident' ? 'error.main' : 
                                   activity.type === 'maintenance' ? 'warning.main' : 'primary.main',
                            width: 32,
                            height: 32
                          }}>
                            {getActivityIcon(activity.type)}
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {activity.vehicle}: {activity.action}
                            </Typography>
                          }
                          secondary={
                            <Typography variant="caption" color="text.secondary">
                              {activity.time} • by {activity.user}
                            </Typography>
                          }
                        />
                      </ListItem>
                      {index < recentActivity.length - 1 && <Divider variant="inset" component="li" />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Upcoming Expirations */}
        <Grid item xs={12}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                  Upcoming Document Expirations
                </Typography>
                <Grid container spacing={2}>
                  {upcomingExpirations.map((item, index) => (
                    <Grid item xs={12} sm={6} md={3} key={index}>
                      <Box sx={{ 
                        p: 2, 
                        border: 1, 
                        borderColor: `${getExpiryColor(item.daysLeft)}.main`,
                        borderRadius: 1,
                        bgcolor: `${getExpiryColor(item.daysLeft)}.light`,
                        opacity: 0.1
                      }}>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {item.vehicle}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {item.type} expires in {item.daysLeft} days
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {item.expiryDate}
                        </Typography>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>
    </Box>
  );
};

export default FleetDashboard;
