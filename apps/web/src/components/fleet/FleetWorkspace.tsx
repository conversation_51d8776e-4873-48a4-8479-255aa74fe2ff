import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Paper,
  useTheme,
  Chip,
  Stack,
  IconButton,
  Tooltip,
  Badge
} from '@mui/material';
import {
  DirectionsCar,
  Dashboard,
  Add,
  Assignment,
  History,
  Assessment,
  Warning,
  Settings,
  Help
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import FleetDashboard from './FleetDashboard';
import VehicleList from './VehicleList';
import VehicleForm from './VehicleForm';
import FleetReports from './FleetReports';
import FleetHistory from './FleetHistory';

interface FleetWorkspaceProps {
  token: string;
  userRoles: string[];
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`fleet-tabpanel-${index}`}
      aria-labelledby={`fleet-tab-${index}`}
    >
      {value === index && (
        <AnimatePresence mode="wait">
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Box sx={{ py: 3 }}>
              {children}
            </Box>
          </motion.div>
        </AnimatePresence>
      )}
    </div>
  );
}

const FleetWorkspace: React.FC<FleetWorkspaceProps> = ({ token, userRoles }) => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const [showAddVehicle, setShowAddVehicle] = useState(false);
  const isAdmin = userRoles.includes('admin');

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    setShowAddVehicle(false); // Close add vehicle form when switching tabs
  };

  // Mock data for alerts - in real implementation, this would come from API
  const alerts = {
    expiringInsurance: 3,
    expiringRegistration: 2,
    maintenanceDue: 5,
    accidentReports: 1
  };

  const totalAlerts = Object.values(alerts).reduce((sum, count) => sum + count, 0);

  const tabs = [
    {
      label: 'Dashboard',
      icon: <Dashboard />,
      component: <FleetDashboard token={token} userRoles={userRoles} alerts={alerts} />,
      adminOnly: false
    },
    {
      label: 'Fleet List',
      icon: <DirectionsCar />,
      component: showAddVehicle ? 
        <VehicleForm 
          token={token} 
          userRoles={userRoles} 
          onCancel={() => setShowAddVehicle(false)}
          onSave={() => setShowAddVehicle(false)}
        /> : 
        <VehicleList 
          token={token} 
          userRoles={userRoles} 
          onAddVehicle={() => setShowAddVehicle(true)}
        />,
      adminOnly: false
    },
    {
      label: 'Reports',
      icon: <Assessment />,
      component: <FleetReports token={token} userRoles={userRoles} />,
      adminOnly: true
    },
    {
      label: 'History',
      icon: <History />,
      component: <FleetHistory token={token} userRoles={userRoles} />,
      adminOnly: false
    }
  ];

  const availableTabs = tabs.filter(tab => !tab.adminOnly || isAdmin);

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 3 }}>
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Paper
            sx={{
              p: 3,
              mb: 3,
              background: `linear-gradient(135deg, ${theme.palette.primary.main}15, ${theme.palette.primary.main}05)`,
              border: `1px solid ${theme.palette.primary.main}30`
            }}
          >
            <Stack direction="row" alignItems="center" justifyContent="space-between">
              <Box>
                <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
                  Fleet Management
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Comprehensive vehicle lifecycle and operations management
                </Typography>
              </Box>
              <Stack direction="row" spacing={1} alignItems="center">
                <Chip
                  label={isAdmin ? 'Full Access' : 'View Only'}
                  color={isAdmin ? 'primary' : 'default'}
                  variant="outlined"
                />
                {totalAlerts > 0 && (
                  <Badge badgeContent={totalAlerts} color="error">
                    <Tooltip title={`${totalAlerts} alerts requiring attention`}>
                      <IconButton size="small" color="warning">
                        <Warning />
                      </IconButton>
                    </Tooltip>
                  </Badge>
                )}
                <Tooltip title="Help & Documentation">
                  <IconButton size="small">
                    <Help />
                  </IconButton>
                </Tooltip>
                {isAdmin && (
                  <Tooltip title="Fleet Settings">
                    <IconButton size="small">
                      <Settings />
                    </IconButton>
                  </Tooltip>
                )}
              </Stack>
            </Stack>
          </Paper>
        </motion.div>

        {/* Navigation Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Paper sx={{ mb: 3 }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                borderBottom: 1,
                borderColor: 'divider',
                '& .MuiTab-root': {
                  minHeight: 72,
                  textTransform: 'none',
                  fontSize: '0.95rem',
                  fontWeight: 500
                }
              }}
            >
              {availableTabs.map((tab, index) => (
                <Tab
                  key={index}
                  icon={tab.icon}
                  label={tab.label}
                  iconPosition="start"
                  sx={{
                    '& .MuiSvgIcon-root': {
                      mr: 1
                    }
                  }}
                />
              ))}
            </Tabs>
          </Paper>
        </motion.div>

        {/* Tab Content */}
        {availableTabs.map((tab, index) => (
          <TabPanel key={index} value={activeTab} index={index}>
            {tab.component}
          </TabPanel>
        ))}
      </Box>
    </Container>
  );
};

export default FleetWorkspace;
