import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  Button,
  Stack,
  Chip,
  IconButton,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  Avatar,
  Menu,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Search,
  FilterList,
  Add,
  Visibility,
  Edit,
  Assignment,
  Build,
  Warning,
  MoreVert,
  DirectionsCar,
  LocalGasStation,
  Speed,
  CalendarToday,
  LocationOn,
  Download
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface VehicleListProps {
  token: string;
  userRoles: string[];
  onAddVehicle: () => void;
}

interface Vehicle {
  id: string;
  registrationPlate: string;
  make: string;
  model: string;
  year: number;
  category: string;
  status: 'available' | 'on_hire' | 'maintenance' | 'accident' | 'disposed';
  branch: string;
  mileage: number;
  lastService: string;
  insuranceExpiry: string;
  registrationExpiry: string;
  currentAgreement?: string;
  acquisitionCost: number;
  monthlyRevenue: number;
}

const VehicleList: React.FC<VehicleListProps> = ({ token, userRoles, onAddVehicle }) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [branchFilter, setBranchFilter] = useState('all');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);

  const isAdmin = userRoles.includes('admin');

  // Mock data - in real implementation, this would come from API
  const vehicles: Vehicle[] = [
    {
      id: 'VEH-001',
      registrationPlate: 'A-12345',
      make: 'Toyota',
      model: 'Camry',
      year: 2022,
      category: 'Sedan',
      status: 'available',
      branch: 'Dubai Main',
      mileage: 25000,
      lastService: '2024-01-15',
      insuranceExpiry: '2024-12-31',
      registrationExpiry: '2024-11-30',
      acquisitionCost: 85000,
      monthlyRevenue: 2500
    },
    {
      id: 'VEH-002',
      registrationPlate: 'B-67890',
      make: 'Honda',
      model: 'CR-V',
      year: 2023,
      category: 'SUV',
      status: 'on_hire',
      branch: 'Abu Dhabi',
      mileage: 15000,
      lastService: '2024-01-20',
      insuranceExpiry: '2024-06-15',
      registrationExpiry: '2024-08-20',
      currentAgreement: 'AGR-2024-001',
      acquisitionCost: 120000,
      monthlyRevenue: 3200
    },
    {
      id: 'VEH-003',
      registrationPlate: 'C-11111',
      make: 'Nissan',
      model: 'Altima',
      year: 2021,
      category: 'Sedan',
      status: 'maintenance',
      branch: 'Sharjah',
      mileage: 45000,
      lastService: '2024-01-10',
      insuranceExpiry: '2024-03-15',
      registrationExpiry: '2024-05-10',
      acquisitionCost: 75000,
      monthlyRevenue: 2200
    },
    {
      id: 'VEH-004',
      registrationPlate: 'D-22222',
      make: 'Ford',
      model: 'Explorer',
      year: 2023,
      category: 'SUV',
      status: 'accident',
      branch: 'Dubai Main',
      mileage: 8000,
      lastService: '2024-01-25',
      insuranceExpiry: '2024-09-30',
      registrationExpiry: '2024-10-15',
      acquisitionCost: 140000,
      monthlyRevenue: 3500
    },
    {
      id: 'VEH-005',
      registrationPlate: 'E-33333',
      make: 'Hyundai',
      model: 'Elantra',
      year: 2022,
      category: 'Sedan',
      status: 'available',
      branch: 'Ajman',
      mileage: 32000,
      lastService: '2024-01-18',
      insuranceExpiry: '2024-07-20',
      registrationExpiry: '2024-09-25',
      acquisitionCost: 70000,
      monthlyRevenue: 2000
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'success';
      case 'on_hire': return 'primary';
      case 'maintenance': return 'warning';
      case 'accident': return 'error';
      case 'disposed': return 'default';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available': return <DirectionsCar />;
      case 'on_hire': return <Assignment />;
      case 'maintenance': return <Build />;
      case 'accident': return <Warning />;
      default: return <DirectionsCar />;
    }
  };

  const isExpiringWithin30Days = (dateString: string) => {
    const expiryDate = new Date(dateString);
    const today = new Date();
    const diffTime = expiryDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 30 && diffDays > 0;
  };

  const isExpired = (dateString: string) => {
    const expiryDate = new Date(dateString);
    const today = new Date();
    return expiryDate < today;
  };

  const filteredVehicles = vehicles.filter(vehicle => {
    const matchesSearch = 
      vehicle.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.registrationPlate.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.make.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.model.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || vehicle.status === statusFilter;
    const matchesCategory = categoryFilter === 'all' || vehicle.category === categoryFilter;
    const matchesBranch = branchFilter === 'all' || vehicle.branch === branchFilter;

    return matchesSearch && matchesStatus && matchesCategory && matchesBranch;
  });

  const paginatedVehicles = filteredVehicles.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, vehicle: Vehicle) => {
    setAnchorEl(event.currentTarget);
    setSelectedVehicle(vehicle);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedVehicle(null);
  };

  const handleViewDetails = () => {
    setDetailsOpen(true);
    handleMenuClose();
  };

  const handleEdit = () => {
    console.log('Editing vehicle:', selectedVehicle);
    handleMenuClose();
  };

  const handleAssignAgreement = () => {
    console.log('Assigning agreement to vehicle:', selectedVehicle);
    handleMenuClose();
  };

  const handleMaintenance = () => {
    console.log('Scheduling maintenance for vehicle:', selectedVehicle);
    handleMenuClose();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: 'AED'
    }).format(amount);
  };

  const categories = [...new Set(vehicles.map(v => v.category))];
  const branches = [...new Set(vehicles.map(v => v.branch))];

  return (
    <Box>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
          <Typography variant="h5" sx={{ fontWeight: 600 }}>
            Fleet Vehicles
          </Typography>
          {isAdmin && (
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={onAddVehicle}
            >
              Add Vehicle
            </Button>
          )}
        </Stack>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Search & Filters
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder="Search by ID, plate, make, or model..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={4} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    label="Status"
                  >
                    <MenuItem value="all">All Statuses</MenuItem>
                    <MenuItem value="available">Available</MenuItem>
                    <MenuItem value="on_hire">On Hire</MenuItem>
                    <MenuItem value="maintenance">Maintenance</MenuItem>
                    <MenuItem value="accident">Accident</MenuItem>
                    <MenuItem value="disposed">Disposed</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={4} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Category</InputLabel>
                  <Select
                    value={categoryFilter}
                    onChange={(e) => setCategoryFilter(e.target.value)}
                    label="Category"
                  >
                    <MenuItem value="all">All Categories</MenuItem>
                    {categories.map(category => (
                      <MenuItem key={category} value={category}>{category}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={4} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Branch</InputLabel>
                  <Select
                    value={branchFilter}
                    onChange={(e) => setBranchFilter(e.target.value)}
                    label="Branch"
                  >
                    <MenuItem value="all">All Branches</MenuItem>
                    {branches.map(branch => (
                      <MenuItem key={branch} value={branch}>{branch}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={4} md={2}>
                <Button
                  variant="outlined"
                  startIcon={<Download />}
                  fullWidth
                  sx={{ height: '100%' }}
                >
                  Export
                </Button>
              </Grid>
            </Grid>
            <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
              <Button
                variant="outlined"
                startIcon={<FilterList />}
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                  setCategoryFilter('all');
                  setBranchFilter('all');
                }}
              >
                Clear Filters
              </Button>
            </Stack>
          </CardContent>
        </Card>

        {/* Results Summary */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="body1">
            Showing {paginatedVehicles.length} of {filteredVehicles.length} vehicles
          </Typography>
        </Paper>

        {/* Vehicles Table */}
        <Card>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Vehicle</TableCell>
                  <TableCell>Details</TableCell>
                  <TableCell align="center">Status</TableCell>
                  <TableCell>Location</TableCell>
                  <TableCell align="right">Mileage</TableCell>
                  <TableCell>Documents</TableCell>
                  <TableCell align="right">Revenue</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedVehicles.map((vehicle) => (
                  <TableRow key={vehicle.id} hover>
                    <TableCell>
                      <Stack direction="row" alignItems="center" spacing={2}>
                        <Avatar sx={{ 
                          bgcolor: `${getStatusColor(vehicle.status)}.main`,
                          width: 40,
                          height: 40
                        }}>
                          {getStatusIcon(vehicle.status)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {vehicle.id}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {vehicle.registrationPlate}
                          </Typography>
                        </Box>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {vehicle.make} {vehicle.model}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {vehicle.year} • {vehicle.category}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="center">
                      <Chip 
                        label={vehicle.status.replace('_', ' ')} 
                        size="small" 
                        color={getStatusColor(vehicle.status)}
                        sx={{ textTransform: 'capitalize' }}
                      />
                      {vehicle.currentAgreement && (
                        <Typography variant="caption" display="block" color="text.secondary">
                          {vehicle.currentAgreement}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <LocationOn fontSize="small" color="action" />
                        <Typography variant="body2">
                          {vehicle.branch}
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell align="right">
                      <Stack direction="row" alignItems="center" spacing={1} justifyContent="flex-end">
                        <Speed fontSize="small" color="action" />
                        <Typography variant="body2">
                          {vehicle.mileage.toLocaleString()} km
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Stack spacing={0.5}>
                        <Typography 
                          variant="caption" 
                          color={isExpired(vehicle.insuranceExpiry) ? 'error' : 
                                 isExpiringWithin30Days(vehicle.insuranceExpiry) ? 'warning.main' : 'text.secondary'}
                        >
                          Insurance: {vehicle.insuranceExpiry}
                        </Typography>
                        <Typography 
                          variant="caption" 
                          color={isExpired(vehicle.registrationExpiry) ? 'error' : 
                                 isExpiringWithin30Days(vehicle.registrationExpiry) ? 'warning.main' : 'text.secondary'}
                        >
                          Registration: {vehicle.registrationExpiry}
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {formatCurrency(vehicle.monthlyRevenue)}/mo
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Cost: {formatCurrency(vehicle.acquisitionCost)}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Tooltip title="More actions">
                        <IconButton
                          size="small"
                          onClick={(e) => handleMenuOpen(e, vehicle)}
                        >
                          <MoreVert />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          
          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50]}
            component="div"
            count={filteredVehicles.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            onRowsPerPageChange={(e) => {
              setRowsPerPage(parseInt(e.target.value, 10));
              setPage(0);
            }}
          />
        </Card>

        {/* Actions Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={handleViewDetails}>
            <ListItemIcon>
              <Visibility fontSize="small" />
            </ListItemIcon>
            <ListItemText>View Details</ListItemText>
          </MenuItem>
          {isAdmin && (
            <>
              <MenuItem onClick={handleEdit}>
                <ListItemIcon>
                  <Edit fontSize="small" />
                </ListItemIcon>
                <ListItemText>Edit Vehicle</ListItemText>
              </MenuItem>
              <MenuItem onClick={handleAssignAgreement}>
                <ListItemIcon>
                  <Assignment fontSize="small" />
                </ListItemIcon>
                <ListItemText>Assign Agreement</ListItemText>
              </MenuItem>
              <MenuItem onClick={handleMaintenance}>
                <ListItemIcon>
                  <Build fontSize="small" />
                </ListItemIcon>
                <ListItemText>Schedule Maintenance</ListItemText>
              </MenuItem>
            </>
          )}
        </Menu>

        {/* Vehicle Details Dialog */}
        <Dialog open={detailsOpen} onClose={() => setDetailsOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>Vehicle Details - {selectedVehicle?.id}</DialogTitle>
          <DialogContent>
            {selectedVehicle && (
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" gutterBottom>Basic Information</Typography>
                  <Typography variant="body2">Make: {selectedVehicle.make}</Typography>
                  <Typography variant="body2">Model: {selectedVehicle.model}</Typography>
                  <Typography variant="body2">Year: {selectedVehicle.year}</Typography>
                  <Typography variant="body2">Category: {selectedVehicle.category}</Typography>
                  <Typography variant="body2">Plate: {selectedVehicle.registrationPlate}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" gutterBottom>Status & Location</Typography>
                  <Typography variant="body2">Status: {selectedVehicle.status}</Typography>
                  <Typography variant="body2">Branch: {selectedVehicle.branch}</Typography>
                  <Typography variant="body2">Mileage: {selectedVehicle.mileage.toLocaleString()} km</Typography>
                  <Typography variant="body2">Last Service: {selectedVehicle.lastService}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" gutterBottom>Documents</Typography>
                  <Typography variant="body2">Insurance Expiry: {selectedVehicle.insuranceExpiry}</Typography>
                  <Typography variant="body2">Registration Expiry: {selectedVehicle.registrationExpiry}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" gutterBottom>Financials</Typography>
                  <Typography variant="body2">Acquisition Cost: {formatCurrency(selectedVehicle.acquisitionCost)}</Typography>
                  <Typography variant="body2">Monthly Revenue: {formatCurrency(selectedVehicle.monthlyRevenue)}</Typography>
                </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDetailsOpen(false)}>Close</Button>
            {isAdmin && <Button variant="contained">Edit Vehicle</Button>}
          </DialogActions>
        </Dialog>
      </motion.div>
    </Box>
  );
};

export default VehicleList;
