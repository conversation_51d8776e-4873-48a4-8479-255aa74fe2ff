import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  <PERSON><PERSON>graphy,
  Grid,
  Button,
  Stack,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Assessment,
  Download,
  Print,
  Refresh,
  TrendingUp,
  TrendingDown,
  DirectionsCar,
  AttachMoney,
  Speed,
  Build,
  Warning
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface FleetReportsProps {
  token: string;
  userRoles: string[];
}

const FleetReports: React.FC<FleetReportsProps> = ({ token, userRoles }) => {
  const [reportType, setReportType] = useState('utilization');
  const [dateRange, setDateRange] = useState({ from: '2024-01-01', to: '2024-01-31' });
  const [branch, setBranch] = useState('all');
  const [category, setCategory] = useState('all');

  // Mock data - in real implementation, this would come from API
  const utilizationData = [
    { vehicle: 'VEH-001', category: 'Sedan', totalDays: 31, rentedDays: 28, utilization: 90.3, revenue: 8400 },
    { vehicle: 'VEH-002', category: 'SUV', totalDays: 31, rentedDays: 25, utilization: 80.6, revenue: 10000 },
    { vehicle: 'VEH-003', category: 'Sedan', totalDays: 31, rentedDays: 22, utilization: 71.0, revenue: 6600 },
    { vehicle: 'VEH-004', category: 'SUV', totalDays: 31, rentedDays: 18, utilization: 58.1, revenue: 7200 },
    { vehicle: 'VEH-005', category: 'Hatchback', totalDays: 31, rentedDays: 26, utilization: 83.9, revenue: 5200 }
  ];

  const maintenanceData = [
    { vehicle: 'VEH-001', lastService: '2024-01-15', nextService: '2024-04-15', cost: 850, type: 'Regular Service' },
    { vehicle: 'VEH-002', lastService: '2024-01-20', nextService: '2024-04-20', cost: 1200, type: 'Major Service' },
    { vehicle: 'VEH-003', lastService: '2024-01-10', nextService: '2024-04-10', cost: 650, type: 'Regular Service' },
    { vehicle: 'VEH-004', lastService: '2024-01-25', nextService: '2024-04-25', cost: 2100, type: 'Repair' },
    { vehicle: 'VEH-005', lastService: '2024-01-18', nextService: '2024-04-18', cost: 750, type: 'Regular Service' }
  ];

  const financialData = [
    { vehicle: 'VEH-001', revenue: 8400, costs: 2100, profit: 6300, margin: 75.0 },
    { vehicle: 'VEH-002', revenue: 10000, costs: 2800, profit: 7200, margin: 72.0 },
    { vehicle: 'VEH-003', revenue: 6600, costs: 1950, profit: 4650, margin: 70.5 },
    { vehicle: 'VEH-004', revenue: 7200, costs: 3200, profit: 4000, margin: 55.6 },
    { vehicle: 'VEH-005', revenue: 5200, costs: 1560, profit: 3640, margin: 70.0 }
  ];

  const branches = ['Dubai Main', 'Abu Dhabi', 'Sharjah', 'Ajman'];
  const categories = ['Sedan', 'SUV', 'Hatchback', 'Luxury'];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: 'AED'
    }).format(amount);
  };

  const getUtilizationColor = (utilization: number) => {
    if (utilization >= 80) return 'success';
    if (utilization >= 60) return 'warning';
    return 'error';
  };

  const handleExport = () => {
    console.log('Exporting report...', { reportType, dateRange, branch, category });
  };

  const handlePrint = () => {
    console.log('Printing report...', { reportType, dateRange, branch, category });
  };

  const renderUtilizationReport = () => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Vehicle</TableCell>
            <TableCell>Category</TableCell>
            <TableCell align="center">Total Days</TableCell>
            <TableCell align="center">Rented Days</TableCell>
            <TableCell align="center">Utilization</TableCell>
            <TableCell align="right">Revenue</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {utilizationData.map((row) => (
            <TableRow key={row.vehicle}>
              <TableCell sx={{ fontWeight: 500 }}>{row.vehicle}</TableCell>
              <TableCell>{row.category}</TableCell>
              <TableCell align="center">{row.totalDays}</TableCell>
              <TableCell align="center">{row.rentedDays}</TableCell>
              <TableCell align="center">
                <Stack alignItems="center" spacing={1}>
                  <Chip 
                    label={`${row.utilization}%`} 
                    color={getUtilizationColor(row.utilization)}
                    size="small"
                  />
                  <LinearProgress 
                    variant="determinate" 
                    value={row.utilization} 
                    sx={{ width: 60, height: 4 }}
                    color={getUtilizationColor(row.utilization)}
                  />
                </Stack>
              </TableCell>
              <TableCell align="right" sx={{ fontWeight: 500 }}>
                {formatCurrency(row.revenue)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  const renderMaintenanceReport = () => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Vehicle</TableCell>
            <TableCell>Last Service</TableCell>
            <TableCell>Next Service</TableCell>
            <TableCell>Service Type</TableCell>
            <TableCell align="right">Cost</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {maintenanceData.map((row) => (
            <TableRow key={row.vehicle}>
              <TableCell sx={{ fontWeight: 500 }}>{row.vehicle}</TableCell>
              <TableCell>{row.lastService}</TableCell>
              <TableCell>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Typography variant="body2">{row.nextService}</Typography>
                  {new Date(row.nextService) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) && (
                    <Warning color="warning" fontSize="small" />
                  )}
                </Stack>
              </TableCell>
              <TableCell>
                <Chip 
                  label={row.type} 
                  size="small" 
                  color={row.type === 'Repair' ? 'error' : 'default'}
                />
              </TableCell>
              <TableCell align="right" sx={{ fontWeight: 500 }}>
                {formatCurrency(row.cost)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  const renderFinancialReport = () => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Vehicle</TableCell>
            <TableCell align="right">Revenue</TableCell>
            <TableCell align="right">Costs</TableCell>
            <TableCell align="right">Profit</TableCell>
            <TableCell align="center">Margin</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {financialData.map((row) => (
            <TableRow key={row.vehicle}>
              <TableCell sx={{ fontWeight: 500 }}>{row.vehicle}</TableCell>
              <TableCell align="right">{formatCurrency(row.revenue)}</TableCell>
              <TableCell align="right">{formatCurrency(row.costs)}</TableCell>
              <TableCell align="right" sx={{ fontWeight: 500, color: 'success.main' }}>
                {formatCurrency(row.profit)}
              </TableCell>
              <TableCell align="center">
                <Chip 
                  label={`${row.margin}%`} 
                  color={row.margin >= 70 ? 'success' : row.margin >= 60 ? 'warning' : 'error'}
                  size="small"
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  const reportSummary = {
    utilization: {
      title: 'Fleet Utilization Report',
      description: 'Vehicle usage and rental performance analysis',
      totalVehicles: utilizationData.length,
      avgUtilization: utilizationData.reduce((sum, v) => sum + v.utilization, 0) / utilizationData.length,
      totalRevenue: utilizationData.reduce((sum, v) => sum + v.revenue, 0)
    },
    maintenance: {
      title: 'Maintenance Report',
      description: 'Service history and upcoming maintenance schedule',
      totalVehicles: maintenanceData.length,
      totalCost: maintenanceData.reduce((sum, v) => sum + v.cost, 0),
      upcomingServices: maintenanceData.filter(v => 
        new Date(v.nextService) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      ).length
    },
    financial: {
      title: 'Financial Performance Report',
      description: 'Revenue, costs, and profitability analysis',
      totalVehicles: financialData.length,
      totalRevenue: financialData.reduce((sum, v) => sum + v.revenue, 0),
      totalProfit: financialData.reduce((sum, v) => sum + v.profit, 0),
      avgMargin: financialData.reduce((sum, v) => sum + v.margin, 0) / financialData.length
    }
  };

  const currentSummary = reportSummary[reportType as keyof typeof reportSummary];

  return (
    <Box>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          Fleet Reports & Analytics
        </Typography>

        {/* Report Controls */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Report Configuration
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Report Type</InputLabel>
                  <Select
                    value={reportType}
                    onChange={(e) => setReportType(e.target.value)}
                    label="Report Type"
                  >
                    <MenuItem value="utilization">Utilization Report</MenuItem>
                    <MenuItem value="maintenance">Maintenance Report</MenuItem>
                    <MenuItem value="financial">Financial Report</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  label="From Date"
                  type="date"
                  value={dateRange.from}
                  onChange={(e) => setDateRange({ ...dateRange, from: e.target.value })}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  label="To Date"
                  type="date"
                  value={dateRange.to}
                  onChange={(e) => setDateRange({ ...dateRange, to: e.target.value })}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Branch</InputLabel>
                  <Select
                    value={branch}
                    onChange={(e) => setBranch(e.target.value)}
                    label="Branch"
                  >
                    <MenuItem value="all">All Branches</MenuItem>
                    {branches.map(b => (
                      <MenuItem key={b} value={b}>{b}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Category</InputLabel>
                  <Select
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                    label="Category"
                  >
                    <MenuItem value="all">All Categories</MenuItem>
                    {categories.map(c => (
                      <MenuItem key={c} value={c}>{c}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={1}>
                <Stack direction="row" spacing={1}>
                  <Tooltip title="Refresh data">
                    <IconButton>
                      <Refresh />
                    </IconButton>
                  </Tooltip>
                </Stack>
              </Grid>
            </Grid>
            <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
              <Button
                variant="outlined"
                startIcon={<Download />}
                onClick={handleExport}
              >
                Export
              </Button>
              <Button
                variant="outlined"
                startIcon={<Print />}
                onClick={handlePrint}
              >
                Print
              </Button>
            </Stack>
          </CardContent>
        </Card>

        {/* Report Summary */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
              <Assessment color="primary" />
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {currentSummary.title}
              </Typography>
            </Stack>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {currentSummary.description}
            </Typography>
            
            <Grid container spacing={3} sx={{ mt: 2 }}>
              <Grid item xs={12} sm={6} md={3}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h4" color="primary" sx={{ fontWeight: 600 }}>
                    {currentSummary.totalVehicles}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Vehicles
                  </Typography>
                </Paper>
              </Grid>
              
              {reportType === 'utilization' && (
                <>
                  <Grid item xs={12} sm={6} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="success.main" sx={{ fontWeight: 600 }}>
                        {currentSummary.avgUtilization?.toFixed(1)}%
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Avg Utilization
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="warning.main" sx={{ fontWeight: 600 }}>
                        {formatCurrency(currentSummary.totalRevenue || 0)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Revenue
                      </Typography>
                    </Paper>
                  </Grid>
                </>
              )}
              
              {reportType === 'maintenance' && (
                <>
                  <Grid item xs={12} sm={6} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="error.main" sx={{ fontWeight: 600 }}>
                        {formatCurrency(currentSummary.totalCost || 0)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Cost
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="warning.main" sx={{ fontWeight: 600 }}>
                        {currentSummary.upcomingServices}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Due in 30 Days
                      </Typography>
                    </Paper>
                  </Grid>
                </>
              )}
              
              {reportType === 'financial' && (
                <>
                  <Grid item xs={12} sm={6} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="success.main" sx={{ fontWeight: 600 }}>
                        {formatCurrency(currentSummary.totalProfit || 0)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Profit
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="info.main" sx={{ fontWeight: 600 }}>
                        {currentSummary.avgMargin?.toFixed(1)}%
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Avg Margin
                      </Typography>
                    </Paper>
                  </Grid>
                </>
              )}
            </Grid>
          </CardContent>
        </Card>

        {/* Report Data */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
              Report Data
            </Typography>
            {reportType === 'utilization' && renderUtilizationReport()}
            {reportType === 'maintenance' && renderMaintenanceReport()}
            {reportType === 'financial' && renderFinancialReport()}
          </CardContent>
        </Card>
      </motion.div>
    </Box>
  );
};

export default FleetReports;
