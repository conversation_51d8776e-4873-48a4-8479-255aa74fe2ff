import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  Button,
  Stack,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  Avatar
} from '@mui/material';
import {
  Search,
  FilterList,
  DirectionsCar,
  Build,
  Assignment,
  Warning,
  CheckCircle,
  AttachMoney,
  Person,
  CalendarToday,
  Download
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface FleetHistoryProps {
  token: string;
  userRoles: string[];
}

interface HistoryRecord {
  id: string;
  vehicleId: string;
  type: 'status_change' | 'maintenance' | 'agreement' | 'accident' | 'financial' | 'document';
  action: string;
  description: string;
  timestamp: string;
  user: string;
  oldValue?: string;
  newValue?: string;
  cost?: number;
  agreementId?: string;
}

const FleetHistory: React.FC<FleetHistoryProps> = ({ token, userRoles }) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [vehicleFilter, setVehicleFilter] = useState('all');
  const [dateRange, setDateRange] = useState({ from: '', to: '' });
  const [viewMode, setViewMode] = useState<'table' | 'timeline'>('table');

  // Mock data - in real implementation, this would come from API
  const historyRecords: HistoryRecord[] = [
    {
      id: 'HIST-001',
      vehicleId: 'VEH-001',
      type: 'status_change',
      action: 'Status Changed',
      description: 'Vehicle status changed from Available to On Hire',
      timestamp: '2024-01-28T10:30:00Z',
      user: 'admin',
      oldValue: 'Available',
      newValue: 'On Hire',
      agreementId: 'AGR-2024-001'
    },
    {
      id: 'HIST-002',
      vehicleId: 'VEH-002',
      type: 'maintenance',
      action: 'Maintenance Completed',
      description: 'Regular service completed - oil change, filter replacement',
      timestamp: '2024-01-27T14:15:00Z',
      user: 'mechanic_01',
      cost: 850
    },
    {
      id: 'HIST-003',
      vehicleId: 'VEH-003',
      type: 'agreement',
      action: 'Agreement Assigned',
      description: 'Vehicle assigned to new rental agreement',
      timestamp: '2024-01-26T09:45:00Z',
      user: 'agent_02',
      agreementId: 'AGR-2024-002'
    },
    {
      id: 'HIST-004',
      vehicleId: 'VEH-001',
      type: 'document',
      action: 'Insurance Renewed',
      description: 'Vehicle insurance policy renewed for 12 months',
      timestamp: '2024-01-25T16:20:00Z',
      user: 'admin',
      cost: 2400
    },
    {
      id: 'HIST-005',
      vehicleId: 'VEH-004',
      type: 'accident',
      action: 'Accident Reported',
      description: 'Minor accident reported - front bumper damage',
      timestamp: '2024-01-24T11:30:00Z',
      user: 'agent_01',
      cost: 3200
    },
    {
      id: 'HIST-006',
      vehicleId: 'VEH-002',
      type: 'financial',
      action: 'Revenue Posted',
      description: 'Monthly rental revenue posted to accounts',
      timestamp: '2024-01-23T17:00:00Z',
      user: 'finance_user',
      cost: 3200
    },
    {
      id: 'HIST-007',
      vehicleId: 'VEH-005',
      type: 'status_change',
      action: 'Status Changed',
      description: 'Vehicle status changed from Maintenance to Available',
      timestamp: '2024-01-22T08:15:00Z',
      user: 'admin',
      oldValue: 'Maintenance',
      newValue: 'Available'
    },
    {
      id: 'HIST-008',
      vehicleId: 'VEH-003',
      type: 'maintenance',
      action: 'Maintenance Scheduled',
      description: 'Scheduled maintenance appointment for next week',
      timestamp: '2024-01-21T13:45:00Z',
      user: 'admin'
    }
  ];

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'status_change': return <DirectionsCar />;
      case 'maintenance': return <Build />;
      case 'agreement': return <Assignment />;
      case 'accident': return <Warning />;
      case 'financial': return <AttachMoney />;
      case 'document': return <CheckCircle />;
      default: return <DirectionsCar />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'status_change': return 'primary';
      case 'maintenance': return 'warning';
      case 'agreement': return 'success';
      case 'accident': return 'error';
      case 'financial': return 'info';
      case 'document': return 'secondary';
      default: return 'default';
    }
  };

  const formatDateTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('en-AE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: 'AED'
    }).format(amount);
  };

  const filteredRecords = historyRecords.filter(record => {
    const matchesSearch = 
      record.vehicleId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.user.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = typeFilter === 'all' || record.type === typeFilter;
    const matchesVehicle = vehicleFilter === 'all' || record.vehicleId === vehicleFilter;
    
    const matchesDateRange = 
      (!dateRange.from || record.timestamp >= dateRange.from) &&
      (!dateRange.to || record.timestamp <= dateRange.to);

    return matchesSearch && matchesType && matchesVehicle && matchesDateRange;
  });

  const paginatedRecords = filteredRecords.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  const vehicles = [...new Set(historyRecords.map(r => r.vehicleId))];

  const renderTableView = () => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Type</TableCell>
            <TableCell>Vehicle</TableCell>
            <TableCell>Action</TableCell>
            <TableCell>Description</TableCell>
            <TableCell>User</TableCell>
            <TableCell>Timestamp</TableCell>
            <TableCell align="right">Cost</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {paginatedRecords.map((record) => (
            <TableRow key={record.id} hover>
              <TableCell>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Avatar sx={{ 
                    bgcolor: `${getTypeColor(record.type)}.main`,
                    width: 32,
                    height: 32
                  }}>
                    {getTypeIcon(record.type)}
                  </Avatar>
                  <Chip 
                    label={record.type.replace('_', ' ')} 
                    size="small" 
                    color={getTypeColor(record.type)}
                    sx={{ textTransform: 'capitalize' }}
                  />
                </Stack>
              </TableCell>
              <TableCell>
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  {record.vehicleId}
                </Typography>
              </TableCell>
              <TableCell>
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  {record.action}
                </Typography>
                {record.agreementId && (
                  <Typography variant="caption" color="text.secondary" display="block">
                    Agreement: {record.agreementId}
                  </Typography>
                )}
              </TableCell>
              <TableCell>
                <Typography variant="body2">
                  {record.description}
                </Typography>
                {record.oldValue && record.newValue && (
                  <Typography variant="caption" color="text.secondary" display="block">
                    {record.oldValue} → {record.newValue}
                  </Typography>
                )}
              </TableCell>
              <TableCell>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Person fontSize="small" color="action" />
                  <Typography variant="body2">
                    {record.user}
                  </Typography>
                </Stack>
              </TableCell>
              <TableCell>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <CalendarToday fontSize="small" color="action" />
                  <Typography variant="body2">
                    {formatDateTime(record.timestamp)}
                  </Typography>
                </Stack>
              </TableCell>
              <TableCell align="right">
                {record.cost ? (
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {formatCurrency(record.cost)}
                  </Typography>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    -
                  </Typography>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  const renderTimelineView = () => (
    <Stack spacing={2}>
      {paginatedRecords.map((record, index) => (
        <Paper key={record.id} sx={{ p: 3, position: 'relative' }}>
          <Stack direction="row" spacing={3}>
            {/* Timeline indicator */}
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', minWidth: 60 }}>
              <Avatar sx={{
                bgcolor: `${getTypeColor(record.type)}.main`,
                width: 40,
                height: 40,
                mb: 1
              }}>
                {getTypeIcon(record.type)}
              </Avatar>
              <Typography variant="caption" color="text.secondary" textAlign="center">
                {formatDateTime(record.timestamp)}
              </Typography>
            </Box>

            {/* Content */}
            <Box sx={{ flex: 1 }}>
              <Stack direction="row" justifyContent="space-between" alignItems="flex-start" sx={{ mb: 1 }}>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {record.vehicleId} - {record.action}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {record.description}
                  </Typography>
                  {record.oldValue && record.newValue && (
                    <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 0.5 }}>
                      Changed from "{record.oldValue}" to "{record.newValue}"
                    </Typography>
                  )}
                  {record.agreementId && (
                    <Typography variant="caption" color="primary" display="block" sx={{ mt: 0.5 }}>
                      Agreement: {record.agreementId}
                    </Typography>
                  )}
                </Box>
                <Stack alignItems="flex-end" spacing={1}>
                  <Chip
                    label={record.type.replace('_', ' ')}
                    size="small"
                    color={getTypeColor(record.type)}
                    sx={{ textTransform: 'capitalize' }}
                  />
                  {record.cost && (
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {formatCurrency(record.cost)}
                    </Typography>
                  )}
                </Stack>
              </Stack>

              <Stack direction="row" alignItems="center" spacing={1} sx={{ mt: 2 }}>
                <Person fontSize="small" color="action" />
                <Typography variant="caption" color="text.secondary">
                  Performed by {record.user}
                </Typography>
              </Stack>
            </Box>
          </Stack>

          {/* Connection line to next item */}
          {index < paginatedRecords.length - 1 && (
            <Box sx={{
              position: 'absolute',
              left: 49,
              bottom: -16,
              width: 2,
              height: 32,
              bgcolor: 'divider'
            }} />
          )}
        </Paper>
      ))}
    </Stack>
  );

  return (
    <Box>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          Fleet History & Audit Trail
        </Typography>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Search & Filters
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder="Search by vehicle, action, description, or user..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Type</InputLabel>
                  <Select
                    value={typeFilter}
                    onChange={(e) => setTypeFilter(e.target.value)}
                    label="Type"
                  >
                    <MenuItem value="all">All Types</MenuItem>
                    <MenuItem value="status_change">Status Change</MenuItem>
                    <MenuItem value="maintenance">Maintenance</MenuItem>
                    <MenuItem value="agreement">Agreement</MenuItem>
                    <MenuItem value="accident">Accident</MenuItem>
                    <MenuItem value="financial">Financial</MenuItem>
                    <MenuItem value="document">Document</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Vehicle</InputLabel>
                  <Select
                    value={vehicleFilter}
                    onChange={(e) => setVehicleFilter(e.target.value)}
                    label="Vehicle"
                  >
                    <MenuItem value="all">All Vehicles</MenuItem>
                    {vehicles.map(vehicle => (
                      <MenuItem key={vehicle} value={vehicle}>{vehicle}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  fullWidth
                  label="From Date"
                  type="date"
                  value={dateRange.from}
                  onChange={(e) => setDateRange({ ...dateRange, from: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  fullWidth
                  label="To Date"
                  type="date"
                  value={dateRange.to}
                  onChange={(e) => setDateRange({ ...dateRange, to: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
            </Grid>
            <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
              <Button
                variant="outlined"
                startIcon={<FilterList />}
                onClick={() => {
                  setSearchTerm('');
                  setTypeFilter('all');
                  setVehicleFilter('all');
                  setDateRange({ from: '', to: '' });
                }}
              >
                Clear Filters
              </Button>
              <Button
                variant="outlined"
                startIcon={<Download />}
              >
                Export History
              </Button>
              <Button
                variant={viewMode === 'table' ? 'contained' : 'outlined'}
                onClick={() => setViewMode('table')}
              >
                Table View
              </Button>
              <Button
                variant={viewMode === 'timeline' ? 'contained' : 'outlined'}
                onClick={() => setViewMode('timeline')}
              >
                Timeline View
              </Button>
            </Stack>
          </CardContent>
        </Card>

        {/* Results Summary */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="body1">
            Showing {paginatedRecords.length} of {filteredRecords.length} history records
          </Typography>
        </Paper>

        {/* History Content */}
        <Card>
          <CardContent>
            {viewMode === 'table' ? renderTableView() : renderTimelineView()}
            
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, 50]}
              component="div"
              count={filteredRecords.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={(_, newPage) => setPage(newPage)}
              onRowsPerPageChange={(e) => {
                setRowsPerPage(parseInt(e.target.value, 10));
                setPage(0);
              }}
            />
          </CardContent>
        </Card>
      </motion.div>
    </Box>
  );
};

export default FleetHistory;
