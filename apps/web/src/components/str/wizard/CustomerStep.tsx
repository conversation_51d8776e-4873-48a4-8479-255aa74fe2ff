import {
  Box,
  Grid,
  TextField,
  Autocomplete,
  Button,
  Card,
  CardContent,
  Typography,
  Stack,
  Chip,
  Avatar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Person as PersonIcon,
  Add as AddIcon,
  Search as SearchIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { useState, useEffect } from 'react';
import { STRFormData, Customer } from '../../../types/str';

interface CustomerStepProps {
  formData: STRFormData;
  customers: Customer[];
  onUpdate: (updates: Partial<STRFormData>) => void;
  token: string;
}

export default function CustomerStep({
  formData,
  customers,
  onUpdate,
  token
}: CustomerStepProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [showCreateDial<PERSON>, setShowCreateDialog] = useState(false);
  const [newCustomer, setNewCustomer] = useState({
    name: '',
    email: '',
    phone: '',
    type: 'CASH' as 'CASH' | 'CORPORATE'
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (formData.customerId) {
      const customer = customers.find(c => c.id === formData.customerId);
      if (customer) {
        setSelectedCustomer(customer);
        onUpdate({ customerData: customer });
      }
    }
  }, [formData.customerId, customers]);

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.phone?.includes(searchTerm)
  );

  const handleCustomerSelect = (customer: Customer | null) => {
    setSelectedCustomer(customer);
    onUpdate({
      customerId: customer?.id || '',
      customerData: customer || undefined
    });
  };

  const handleCreateCustomer = async () => {
    setLoading(true);
    try {
      // TODO: Implement API call to create customer
      // const response = await api.createCustomer(token, newCustomer);
      
      // Mock customer creation
      const mockCustomer: Customer = {
        id: `CUST-${Date.now()}`,
        name: newCustomer.name,
        email: newCustomer.email,
        phone: newCustomer.phone,
        type: newCustomer.type,
        status: 'ACTIVE',
        kycStatus: 'PENDING',
        documents: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      handleCustomerSelect(mockCustomer);
      setShowCreateDialog(false);
      setNewCustomer({ name: '', email: '', phone: '', type: 'CASH' });
    } catch (error) {
      console.error('Failed to create customer:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'success';
      case 'HOLD': return 'warning';
      case 'BLOCKED': return 'error';
      default: return 'default';
    }
  };

  const getKYCStatusColor = (status: string) => {
    switch (status) {
      case 'VERIFIED': return 'success';
      case 'PENDING': return 'warning';
      case 'REJECTED': return 'error';
      default: return 'default';
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Select Customer
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Search for an existing customer or create a new one
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Stack spacing={2}>
            {/* Search and Create */}
            <Stack direction="row" spacing={2}>
              <Autocomplete
                fullWidth
                options={filteredCustomers}
                getOptionLabel={(option) => `${option.name} (${option.email || option.phone || 'No contact'})`}
                value={selectedCustomer}
                onChange={(_, value) => handleCustomerSelect(value)}
                onInputChange={(_, value) => setSearchTerm(value)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Search Customers"
                    placeholder="Search by name, email, or phone"
                    InputProps={{
                      ...params.InputProps,
                      startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                    }}
                  />
                )}
                renderOption={(props, option) => (
                  <Box component="li" {...props}>
                    <Stack direction="row" spacing={2} alignItems="center" sx={{ width: '100%' }}>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        <PersonIcon />
                      </Avatar>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="subtitle2">{option.name}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {option.email || option.phone || 'No contact info'}
                        </Typography>
                      </Box>
                      <Stack direction="row" spacing={1}>
                        <Chip
                          label={option.status}
                          size="small"
                          color={getStatusColor(option.status) as any}
                        />
                        <Chip
                          label={option.kycStatus}
                          size="small"
                          color={getKYCStatusColor(option.kycStatus) as any}
                        />
                      </Stack>
                    </Stack>
                  </Box>
                )}
              />
              
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={() => setShowCreateDialog(true)}
                sx={{ minWidth: 140 }}
              >
                New Customer
              </Button>
            </Stack>

            {/* Customer Details Card */}
            {selectedCustomer && (
              <Card variant="outlined">
                <CardContent>
                  <Stack direction="row" spacing={2} alignItems="flex-start">
                    <Avatar sx={{ bgcolor: 'primary.main', width: 56, height: 56 }}>
                      <PersonIcon />
                    </Avatar>
                    
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="h6">{selectedCustomer.name}</Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Customer ID: {selectedCustomer.id}
                      </Typography>
                      
                      <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
                        <Chip
                          label={selectedCustomer.status}
                          size="small"
                          color={getStatusColor(selectedCustomer.status) as any}
                        />
                        <Chip
                          label={`KYC: ${selectedCustomer.kycStatus}`}
                          size="small"
                          color={getKYCStatusColor(selectedCustomer.kycStatus) as any}
                        />
                        <Chip
                          label={selectedCustomer.type}
                          size="small"
                          variant="outlined"
                        />
                      </Stack>
                      
                      <Grid container spacing={2}>
                        {selectedCustomer.email && (
                          <Grid item xs={12} sm={6}>
                            <Typography variant="caption" color="text.secondary">Email</Typography>
                            <Typography variant="body2">{selectedCustomer.email}</Typography>
                          </Grid>
                        )}
                        {selectedCustomer.phone && (
                          <Grid item xs={12} sm={6}>
                            <Typography variant="caption" color="text.secondary">Phone</Typography>
                            <Typography variant="body2">{selectedCustomer.phone}</Typography>
                          </Grid>
                        )}
                      </Grid>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            )}
          </Stack>
        </Grid>

        <Grid item xs={12} md={4}>
          {/* Validation Messages */}
          {selectedCustomer && (
            <Stack spacing={2}>
              {selectedCustomer.status === 'HOLD' && (
                <Alert severity="warning" icon={<WarningIcon />}>
                  Customer is on hold. Contact management before proceeding.
                </Alert>
              )}
              
              {selectedCustomer.status === 'BLOCKED' && (
                <Alert severity="error" icon={<WarningIcon />}>
                  Customer is blocked and cannot rent vehicles.
                </Alert>
              )}
              
              {selectedCustomer.kycStatus !== 'VERIFIED' && (
                <Alert severity="warning" icon={<WarningIcon />}>
                  Customer KYC is not verified. This may prevent agreement creation.
                </Alert>
              )}
              
              {selectedCustomer.status === 'ACTIVE' && selectedCustomer.kycStatus === 'VERIFIED' && (
                <Alert severity="success" icon={<CheckCircleIcon />}>
                  Customer is eligible for rental agreements.
                </Alert>
              )}
            </Stack>
          )}
        </Grid>
      </Grid>

      {/* Create Customer Dialog */}
      <Dialog open={showCreateDialog} onClose={() => setShowCreateDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Create New Customer</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              label="Customer Name"
              value={newCustomer.name}
              onChange={(e) => setNewCustomer(prev => ({ ...prev, name: e.target.value }))}
              fullWidth
              required
            />
            
            <TextField
              label="Email Address"
              type="email"
              value={newCustomer.email}
              onChange={(e) => setNewCustomer(prev => ({ ...prev, email: e.target.value }))}
              fullWidth
            />
            
            <TextField
              label="Phone Number"
              value={newCustomer.phone}
              onChange={(e) => setNewCustomer(prev => ({ ...prev, phone: e.target.value }))}
              fullWidth
            />
            
            <FormControl fullWidth>
              <InputLabel>Customer Type</InputLabel>
              <Select
                value={newCustomer.type}
                label="Customer Type"
                onChange={(e) => setNewCustomer(prev => ({ ...prev, type: e.target.value as 'CASH' | 'CORPORATE' }))}
              >
                <MenuItem value="CASH">Cash Customer</MenuItem>
                <MenuItem value="CORPORATE">Corporate Customer</MenuItem>
              </Select>
            </FormControl>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowCreateDialog(false)}>Cancel</Button>
          <Button
            onClick={handleCreateCustomer}
            variant="contained"
            disabled={!newCustomer.name || loading}
          >
            {loading ? 'Creating...' : 'Create Customer'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
