import { Chip, ChipProps, Tooltip } from '@mui/material';
import { getStatusColor, getWorkflowStageLabel } from '../../utils/strPermissions';

interface StatusTagProps extends Omit<ChipProps, 'color'> {
  status: string;
  workflowStage?: string;
  showTooltip?: boolean;
  variant?: 'filled' | 'outlined';
}

export default function StatusTag({ 
  status, 
  workflowStage, 
  showTooltip = true, 
  variant = 'filled',
  ...props 
}: StatusTagProps) {
  const color = getStatusColor(status);
  const label = status.replace(/_/g, ' ');
  
  const chip = (
    <Chip
      label={label}
      color={color}
      variant={variant}
      size="small"
      sx={{
        fontWeight: 600,
        textTransform: 'capitalize',
        ...props.sx
      }}
      {...props}
    />
  );

  if (showTooltip && workflowStage) {
    return (
      <Tooltip title={`Workflow Stage: ${getWorkflowStageLabel(workflowStage)}`}>
        {chip}
      </Tooltip>
    );
  }

  return chip;
}
