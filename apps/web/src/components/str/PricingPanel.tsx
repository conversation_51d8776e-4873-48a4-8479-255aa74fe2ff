import {
  Box,
  Card,
  Card<PERSON>ontent,
  Card<PERSON>eader,
  <PERSON>vider,
  <PERSON>ack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  IconButton,
  Tooltip,
  TextField,
  Button,
  Alert
} from '@mui/material';
import { Edit as EditIcon, Save as SaveIcon, Cancel as CancelIcon } from '@mui/icons-material';
import { useState } from 'react';
import { PricingBreakdown, PricingLine } from '../../types/str';

interface PricingPanelProps {
  pricing: PricingBreakdown;
  editable?: boolean;
  onUpdate?: (pricing: PricingBreakdown) => void;
  showBreakdown?: boolean;
  compact?: boolean;
}

export default function PricingPanel({
  pricing,
  editable = false,
  onUpdate,
  showBreakdown = true,
  compact = false
}: PricingPanelProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedPricing, setEditedPricing] = useState<PricingBreakdown>(pricing);
  const [errors, setErrors] = useState<string[]>([]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: pricing.currency || 'AED',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const handleEdit = () => {
    setIsEditing(true);
    setEditedPricing({ ...pricing });
    setErrors([]);
  };

  const handleSave = () => {
    const validationErrors = validatePricing(editedPricing);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    onUpdate?.(editedPricing);
    setIsEditing(false);
    setErrors([]);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditedPricing({ ...pricing });
    setErrors([]);
  };

  const validatePricing = (pricingData: PricingBreakdown): string[] => {
    const errors: string[] = [];
    
    if (pricingData.baseAmount < 0) {
      errors.push('Base amount cannot be negative');
    }
    
    if (pricingData.totalAmount <= 0) {
      errors.push('Total amount must be greater than zero');
    }
    
    // Validate breakdown totals
    const calculatedTotal = pricingData.breakdown.reduce((sum, line) => sum + line.totalPrice, 0);
    const expectedTotal = pricingData.baseAmount + pricingData.additionalCharges - pricingData.discounts + pricingData.taxes;
    
    if (Math.abs(calculatedTotal - expectedTotal) > 0.01) {
      errors.push('Pricing breakdown does not match totals');
    }
    
    return errors;
  };

  const updateLineItem = (index: number, field: keyof PricingLine, value: any) => {
    const newBreakdown = [...editedPricing.breakdown];
    newBreakdown[index] = { ...newBreakdown[index], [field]: value };
    
    // Recalculate total price for the line
    if (field === 'quantity' || field === 'unitPrice') {
      newBreakdown[index].totalPrice = newBreakdown[index].quantity * newBreakdown[index].unitPrice;
    }
    
    // Recalculate totals
    const newTotalAmount = newBreakdown.reduce((sum, line) => sum + line.totalPrice, 0);
    
    setEditedPricing({
      ...editedPricing,
      breakdown: newBreakdown,
      totalAmount: newTotalAmount
    });
  };

  if (compact) {
    return (
      <Box>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography variant="subtitle2" color="text.secondary">
            Total Amount
          </Typography>
          <Typography variant="h6" color="primary" fontWeight={600}>
            {formatCurrency(pricing.totalAmount)}
          </Typography>
        </Stack>
        {pricing.outstandingAmount && pricing.outstandingAmount > 0 && (
          <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mt: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Outstanding
            </Typography>
            <Typography variant="body2" color="error.main" fontWeight={600}>
              {formatCurrency(pricing.outstandingAmount)}
            </Typography>
          </Stack>
        )}
      </Box>
    );
  }

  return (
    <Card>
      <CardHeader
        title="Pricing Details"
        action={
          editable && !isEditing ? (
            <Tooltip title="Edit Pricing">
              <IconButton onClick={handleEdit} size="small">
                <EditIcon />
              </IconButton>
            </Tooltip>
          ) : isEditing ? (
            <Stack direction="row" spacing={1}>
              <Tooltip title="Save Changes">
                <IconButton onClick={handleSave} size="small" color="primary">
                  <SaveIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Cancel">
                <IconButton onClick={handleCancel} size="small">
                  <CancelIcon />
                </IconButton>
              </Tooltip>
            </Stack>
          ) : null
        }
      />
      <CardContent>
        {errors.length > 0 && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {errors.map((error, index) => (
              <div key={index}>{error}</div>
            ))}
          </Alert>
        )}

        {/* Summary */}
        <Stack spacing={1} sx={{ mb: 3 }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="body2">Base Amount:</Typography>
            <Typography variant="body2" fontWeight={500}>
              {formatCurrency(isEditing ? editedPricing.baseAmount : pricing.baseAmount)}
            </Typography>
          </Stack>
          
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="body2">Additional Charges:</Typography>
            <Typography variant="body2" fontWeight={500}>
              {formatCurrency(isEditing ? editedPricing.additionalCharges : pricing.additionalCharges)}
            </Typography>
          </Stack>
          
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="body2">Discounts:</Typography>
            <Typography variant="body2" fontWeight={500} color="success.main">
              -{formatCurrency(isEditing ? editedPricing.discounts : pricing.discounts)}
            </Typography>
          </Stack>
          
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="body2">Taxes:</Typography>
            <Typography variant="body2" fontWeight={500}>
              {formatCurrency(isEditing ? editedPricing.taxes : pricing.taxes)}
            </Typography>
          </Stack>
          
          <Divider />
          
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6">Total Amount:</Typography>
            <Typography variant="h6" color="primary" fontWeight={600}>
              {formatCurrency(isEditing ? editedPricing.totalAmount : pricing.totalAmount)}
            </Typography>
          </Stack>
        </Stack>

        {/* Detailed Breakdown */}
        {showBreakdown && pricing.breakdown.length > 0 && (
          <>
            <Typography variant="subtitle2" gutterBottom>
              Detailed Breakdown
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Description</TableCell>
                    <TableCell align="center">Qty</TableCell>
                    <TableCell align="right">Unit Price</TableCell>
                    <TableCell align="right">Total</TableCell>
                    <TableCell>Category</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {(isEditing ? editedPricing.breakdown : pricing.breakdown).map((line, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        {isEditing ? (
                          <TextField
                            value={line.description}
                            onChange={(e) => updateLineItem(index, 'description', e.target.value)}
                            size="small"
                            fullWidth
                          />
                        ) : (
                          line.description
                        )}
                      </TableCell>
                      <TableCell align="center">
                        {isEditing ? (
                          <TextField
                            type="number"
                            value={line.quantity}
                            onChange={(e) => updateLineItem(index, 'quantity', Number(e.target.value))}
                            size="small"
                            inputProps={{ min: 0, step: 1 }}
                            sx={{ width: 80 }}
                          />
                        ) : (
                          line.quantity
                        )}
                      </TableCell>
                      <TableCell align="right">
                        {isEditing ? (
                          <TextField
                            type="number"
                            value={line.unitPrice}
                            onChange={(e) => updateLineItem(index, 'unitPrice', Number(e.target.value))}
                            size="small"
                            inputProps={{ min: 0, step: 0.01 }}
                            sx={{ width: 100 }}
                          />
                        ) : (
                          formatCurrency(line.unitPrice)
                        )}
                      </TableCell>
                      <TableCell align="right" sx={{ fontWeight: 500 }}>
                        {formatCurrency(line.totalPrice)}
                      </TableCell>
                      <TableCell>
                        <Typography variant="caption" color="text.secondary">
                          {line.category}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        )}
      </CardContent>
    </Card>
  );
}
