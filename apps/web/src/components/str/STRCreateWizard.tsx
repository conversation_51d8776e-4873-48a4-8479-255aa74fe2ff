import {
  <PERSON>,
  Card,
  Card<PERSON>ontent,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ack,
  Alert,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import { useState, useEffect } from 'react';
import { STRFormData, STRAgreement, Customer, Vehicle } from '../../types/str';
import { validateBusinessRules } from '../../utils/strPermissions';

// Step Components
import CustomerStep from './wizard/CustomerStep';
// import VehicleStep from './wizard/VehicleStep';
// import AgreementDetailsStep from './wizard/AgreementDetailsStep';
// import PricingStep from './wizard/PricingStep';
// import ReviewStep from './wizard/ReviewStep';

interface STRCreateWizardProps {
  onComplete: (agreement: STRAgreement) => void;
  onCancel: () => void;
  initialData?: Partial<STRFormData>;
  userRoles: string[];
  token: string;
}

const steps = [
  {
    label: 'Customer',
    description: 'Select or create customer'
  },
  {
    label: 'Vehicle',
    description: 'Choose available vehicle'
  },
  {
    label: 'Agreement Details',
    description: 'Set dates and terms'
  },
  {
    label: 'Pricing & Payment',
    description: 'Configure pricing and payment'
  },
  {
    label: 'Review & Confirm',
    description: 'Review and create agreement'
  }
];

export default function STRCreateWizard({
  onComplete,
  onCancel,
  initialData,
  userRoles,
  token
}: STRCreateWizardProps) {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState<STRFormData>({
    customerId: '',
    vehicleId: '',
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    pickupLocation: '',
    returnLocation: '',
    handoverNotes: '',
    paymentMethod: 'CASH',
    depositAmount: 0,
    termsAccepted: false,
    documentsUploaded: [],
    ...initialData
  });
  
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [showCancelDialog, setShowCancelDialog] = useState(false);

  useEffect(() => {
    // Load initial data
    loadCustomers();
    loadVehicles();
  }, []);

  const loadCustomers = async () => {
    try {
      // TODO: Implement API call to load customers
      // const response = await api.getCustomers(token);
      // setCustomers(response);
    } catch (error) {
      console.error('Failed to load customers:', error);
    }
  };

  const loadVehicles = async () => {
    try {
      // TODO: Implement API call to load available vehicles
      // const response = await api.getAvailableVehicles(token, {
      //   businessType: 'STR',
      //   startDate: formData.startDate,
      //   endDate: formData.endDate
      // });
      // setVehicles(response);
    } catch (error) {
      console.error('Failed to load vehicles:', error);
    }
  };

  const updateFormData = (updates: Partial<STRFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
    setErrors([]); // Clear errors when form data changes
  };

  const validateStep = (step: number): boolean => {
    const stepErrors: string[] = [];

    switch (step) {
      case 0: // Customer
        if (!formData.customerId) {
          stepErrors.push('Please select a customer');
        }
        if (formData.customerData?.status === 'HOLD') {
          stepErrors.push('Selected customer is on hold');
        }
        if (formData.customerData?.kycStatus !== 'VERIFIED') {
          stepErrors.push('Customer KYC must be verified');
        }
        break;

      case 1: // Vehicle
        if (!formData.vehicleId) {
          stepErrors.push('Please select a vehicle');
        }
        if (formData.vehicleData?.status !== 'AVAILABLE') {
          stepErrors.push('Selected vehicle is not available');
        }
        break;

      case 2: // Agreement Details
        if (!formData.startDate) {
          stepErrors.push('Start date is required');
        }
        if (!formData.endDate) {
          stepErrors.push('End date is required');
        }
        if (new Date(formData.endDate) <= new Date(formData.startDate)) {
          stepErrors.push('End date must be after start date');
        }
        if (new Date(formData.startDate) < new Date()) {
          stepErrors.push('Start date cannot be in the past');
        }
        if (!formData.pickupLocation) {
          stepErrors.push('Pickup location is required');
        }
        break;

      case 3: // Pricing
        if (!formData.pricingData) {
          stepErrors.push('Pricing calculation is required');
        }
        if (formData.pricingData && formData.pricingData.totalAmount <= 0) {
          stepErrors.push('Total amount must be greater than zero');
        }
        if (!formData.paymentMethod) {
          stepErrors.push('Payment method is required');
        }
        break;

      case 4: // Review
        if (!formData.termsAccepted) {
          stepErrors.push('You must accept the terms and conditions');
        }
        break;
    }

    setErrors(stepErrors);
    return stepErrors.length === 0;
  };

  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
    setErrors([]);
  };

  const handleSubmit = async () => {
    if (!validateStep(activeStep)) {
      return;
    }

    setLoading(true);
    try {
      // Validate business rules
      const validation = validateBusinessRules({
        customerId: formData.customerId,
        vehicleId: formData.vehicleId,
        startDate: formData.startDate,
        endDate: formData.endDate,
        customer: formData.customerData,
        vehicle: formData.vehicleData,
        pricing: formData.pricingData,
        requiresDeposit: formData.depositAmount > 0,
        depositAmount: formData.depositAmount
      }, userRoles);

      if (!validation.valid) {
        setErrors(validation.errors);
        return;
      }

      // Create the agreement
      // TODO: Implement API call to create STR agreement
      // const agreement = await api.createSTRAgreement(token, formData);
      
      // Mock success for now
      const mockAgreement: STRAgreement = {
        id: `STR-${Date.now()}`,
        agreementNumber: `STR-${Date.now()}`,
        customerId: formData.customerId,
        vehicleId: formData.vehicleId,
        startDate: formData.startDate,
        endDate: formData.endDate,
        duration: Math.ceil((new Date(formData.endDate).getTime() - new Date(formData.startDate).getTime()) / (1000 * 60 * 60 * 24)),
        status: 'DRAFT',
        workflowStage: 'CREATED',
        pricing: formData.pricingData!,
        documents: [],
        requiredDocuments: ['ID_COPY', 'LICENSE_COPY'],
        createdBy: 'current-user',
        createdAt: new Date().toISOString(),
        updatedBy: 'current-user',
        updatedAt: new Date().toISOString(),
        auditLog: [{
          id: '1',
          timestamp: new Date().toISOString(),
          userId: 'current-user',
          userName: 'Current User',
          action: 'agreement_created',
          details: 'STR Agreement created via wizard'
        }],
        allowExtension: true,
        allowEarlyReturn: true,
        requiresDeposit: formData.depositAmount > 0,
        totalAmount: formData.pricingData?.totalAmount || 0,
        paidAmount: 0,
        outstandingAmount: formData.pricingData?.totalAmount || 0,
        depositAmount: formData.depositAmount,
        refundableDeposit: formData.depositAmount
      };

      onComplete(mockAgreement);
    } catch (error: any) {
      setErrors([error.message || 'Failed to create agreement']);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setShowCancelDialog(true);
  };

  const confirmCancel = () => {
    setShowCancelDialog(false);
    onCancel();
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <CustomerStep
            formData={formData}
            customers={customers}
            onUpdate={updateFormData}
            token={token}
          />
        );
      case 1:
        return (
          <Box>
            <Typography>Vehicle Step - Coming Soon</Typography>
          </Box>
        );
      case 2:
        return (
          <Box>
            <Typography>Agreement Details Step - Coming Soon</Typography>
          </Box>
        );
      case 3:
        return (
          <Box>
            <Typography>Pricing Step - Coming Soon</Typography>
          </Box>
        );
      case 4:
        return (
          <Box>
            <Typography>Review Step - Coming Soon</Typography>
          </Box>
        );
      default:
        return null;
    }
  };

  return (
    <>
      <Card>
        <CardContent>
          <Box sx={{ mb: 4 }}>
            <Typography variant="h5" gutterBottom>
              Create STR Agreement
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Follow the steps below to create a new Short Term Rental agreement
            </Typography>
          </Box>

          {loading && <LinearProgress sx={{ mb: 2 }} />}

          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            {steps.map((step, index) => (
              <Step key={step.label}>
                <StepLabel>
                  <Typography variant="subtitle2">{step.label}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {step.description}
                  </Typography>
                </StepLabel>
              </Step>
            ))}
          </Stepper>

          {errors.length > 0 && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {errors.map((error, index) => (
                <div key={index}>{error}</div>
              ))}
            </Alert>
          )}

          <Box sx={{ mb: 4 }}>
            {renderStepContent(activeStep)}
          </Box>

          <Stack direction="row" spacing={2} justifyContent="space-between">
            <Button
              variant="outlined"
              onClick={handleCancel}
              disabled={loading}
            >
              Cancel
            </Button>

            <Stack direction="row" spacing={2}>
              <Button
                onClick={handleBack}
                disabled={activeStep === 0 || loading}
              >
                Back
              </Button>
              
              {activeStep === steps.length - 1 ? (
                <Button
                  variant="contained"
                  onClick={handleSubmit}
                  disabled={loading}
                >
                  Create Agreement
                </Button>
              ) : (
                <Button
                  variant="contained"
                  onClick={handleNext}
                  disabled={loading}
                >
                  Next
                </Button>
              )}
            </Stack>
          </Stack>
        </CardContent>
      </Card>

      {/* Cancel Confirmation Dialog */}
      <Dialog open={showCancelDialog} onClose={() => setShowCancelDialog(false)}>
        <DialogTitle>Cancel Agreement Creation</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to cancel? All entered data will be lost.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowCancelDialog(false)}>
            Continue Editing
          </Button>
          <Button onClick={confirmCancel} color="error">
            Cancel Agreement
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
