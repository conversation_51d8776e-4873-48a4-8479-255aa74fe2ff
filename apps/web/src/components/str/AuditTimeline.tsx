import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent
} from '@mui/lab';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Avatar,
  Collapse,
  IconButton,
  Stack
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Person as PersonIcon,
  Edit as EditIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Payment as PaymentIcon,
  Assignment as AssignmentIcon
} from '@mui/icons-material';
import { useState } from 'react';
import { AuditEntry } from '../../types/str';

interface AuditTimelineProps {
  auditLog: AuditEntry[];
  userRole: 'customer' | 'agent' | 'admin';
  maxItems?: number;
  showDetails?: boolean;
  compact?: boolean;
}

export default function AuditTimeline({
  auditLog,
  userRole,
  maxItems = 10,
  showDetails = true,
  compact = false
}: AuditTimelineProps) {
  const [expanded, setExpanded] = useState(false);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  // Filter audit log based on user role
  const filteredLog = userRole === 'customer' 
    ? auditLog.filter(entry => isCustomerRelevant(entry))
    : auditLog;

  const displayedLog = expanded ? filteredLog : filteredLog.slice(0, maxItems);

  const getActionIcon = (action: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      'created': <AssignmentIcon />,
      'updated': <EditIcon />,
      'confirmed': <CheckCircleIcon />,
      'cancelled': <CancelIcon />,
      'payment': <PaymentIcon />,
      'handover': <PersonIcon />,
      'return': <PersonIcon />,
      'closed': <CheckCircleIcon />,
    };

    const actionType = action.toLowerCase().split('_')[0];
    return iconMap[actionType] || <PersonIcon />;
  };

  const getActionColor = (action: string): 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info' => {
    const actionType = action.toLowerCase();
    
    if (actionType.includes('created') || actionType.includes('confirmed')) return 'success';
    if (actionType.includes('cancelled') || actionType.includes('rejected')) return 'error';
    if (actionType.includes('payment') || actionType.includes('paid')) return 'primary';
    if (actionType.includes('pending') || actionType.includes('requested')) return 'warning';
    if (actionType.includes('updated') || actionType.includes('modified')) return 'info';
    
    return 'secondary';
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return `Today at ${date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })}`;
    } else if (diffDays === 1) {
      return `Yesterday at ${date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })}`;
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else {
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  const toggleItemExpansion = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const isCustomerRelevant = (entry: AuditEntry): boolean => {
    const customerActions = [
      'agreement_created',
      'agreement_confirmed',
      'payment_received',
      'handover_completed',
      'return_requested',
      'return_completed',
      'agreement_closed',
      'extension_requested',
      'extension_approved',
      'extension_rejected',
      'document_uploaded'
    ];
    
    return customerActions.some(action => entry.action.toLowerCase().includes(action.toLowerCase()));
  };

  if (compact) {
    return (
      <Box>
        <Typography variant="subtitle2" gutterBottom>
          Recent Activity
        </Typography>
        <Stack spacing={1}>
          {displayedLog.slice(0, 3).map((entry) => (
            <Box key={entry.id} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Avatar sx={{ width: 24, height: 24, bgcolor: `${getActionColor(entry.action)}.main` }}>
                {getActionIcon(entry.action)}
              </Avatar>
              <Typography variant="body2" sx={{ flex: 1 }}>
                {entry.details}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {formatTimestamp(entry.timestamp)}
              </Typography>
            </Box>
          ))}
        </Stack>
      </Box>
    );
  }

  return (
    <Card>
      <CardHeader
        title="Activity Timeline"
        subheader={`${filteredLog.length} activities`}
        action={
          filteredLog.length > maxItems && (
            <IconButton onClick={() => setExpanded(!expanded)}>
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          )
        }
      />
      <CardContent>
        <Timeline position="right">
          {displayedLog.map((entry, index) => (
            <TimelineItem key={entry.id}>
              <TimelineOppositeContent sx={{ flex: 0.3 }}>
                <Typography variant="caption" color="text.secondary">
                  {formatTimestamp(entry.timestamp)}
                </Typography>
                <Typography variant="caption" display="block" color="text.secondary">
                  {entry.userName}
                </Typography>
              </TimelineOppositeContent>
              
              <TimelineSeparator>
                <TimelineDot color={getActionColor(entry.action)}>
                  {getActionIcon(entry.action)}
                </TimelineDot>
                {index < displayedLog.length - 1 && <TimelineConnector />}
              </TimelineSeparator>
              
              <TimelineContent>
                <Box sx={{ mb: 1 }}>
                  <Typography variant="body2" fontWeight={500}>
                    {entry.details}
                  </Typography>
                  
                  {showDetails && (entry.oldValue || entry.newValue) && (
                    <Box sx={{ mt: 1 }}>
                      <IconButton
                        size="small"
                        onClick={() => toggleItemExpansion(entry.id)}
                      >
                        {expandedItems.has(entry.id) ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                      </IconButton>
                      
                      <Collapse in={expandedItems.has(entry.id)}>
                        <Box sx={{ mt: 1, p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>
                          {entry.oldValue && (
                            <Typography variant="caption" display="block">
                              <strong>Previous:</strong> {JSON.stringify(entry.oldValue)}
                            </Typography>
                          )}
                          {entry.newValue && (
                            <Typography variant="caption" display="block">
                              <strong>New:</strong> {JSON.stringify(entry.newValue)}
                            </Typography>
                          )}
                          {userRole !== 'customer' && entry.ipAddress && (
                            <Typography variant="caption" display="block" color="text.secondary">
                              IP: {entry.ipAddress}
                            </Typography>
                          )}
                        </Box>
                      </Collapse>
                    </Box>
                  )}
                </Box>
              </TimelineContent>
            </TimelineItem>
          ))}
        </Timeline>
        
        {!expanded && filteredLog.length > maxItems && (
          <Box sx={{ textAlign: 'center', mt: 2 }}>
            <Chip
              label={`Show ${filteredLog.length - maxItems} more activities`}
              onClick={() => setExpanded(true)}
              variant="outlined"
              size="small"
            />
          </Box>
        )}
      </CardContent>
    </Card>
  );
}
