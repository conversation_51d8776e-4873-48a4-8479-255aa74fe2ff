import {
  Box,
  Card,
  Card<PERSON>ontent,
  <PERSON>per,
  <PERSON>,
  <PERSON><PERSON>abel,
  <PERSON>ton,
  <PERSON><PERSON><PERSON>,
  <PERSON>ack,
  Alert,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  FormControlLabel,
  Checkbox,
  Chip,
  IconButton,
  Paper
} from '@mui/material';
import {
  PhotoCamera as PhotoCameraIcon,
  Videocam as VideocamIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { useState, useRef } from 'react';
import { CustomerReturnRequest, STRAgreement } from '../../types/str';

interface CustomerReturnWizardProps {
  agreementId: string;
  customerId: string;
  token: string;
  onComplete: (returnRequest: CustomerReturnRequest) => void;
  onCancel: () => void;
}

const steps = [
  {
    label: 'Return Details',
    description: 'When and where to return'
  },
  {
    label: 'Vehicle Condition',
    description: 'Photos and condition report'
  },
  {
    label: 'Odometer & Fuel',
    description: 'Current readings'
  },
  {
    label: 'Review & Submit',
    description: 'Confirm and submit request'
  }
];

export default function CustomerReturnWizard({
  agreementId,
  customerId,
  token,
  onComplete,
  onCancel
}: CustomerReturnWizardProps) {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  
  const [formData, setFormData] = useState({
    proposedReturnDate: new Date().toISOString().split('T')[0],
    proposedReturnTime: '10:00',
    returnLocation: '',
    estimatedOdometer: 0,
    estimatedFuelLevel: 100,
    reportedDamages: [] as string[],
    returnPhotos: [] as string[],
    returnVideos: [] as string[],
    customerNotes: '',
    termsAccepted: false,
    declarationSigned: false
  });

  const photoInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  const updateFormData = (updates: any) => {
    setFormData(prev => ({ ...prev, ...updates }));
    setErrors([]);
  };

  const validateStep = (step: number): boolean => {
    const stepErrors: string[] = [];

    switch (step) {
      case 0: // Return Details
        if (!formData.proposedReturnDate) {
          stepErrors.push('Return date is required');
        }
        if (!formData.proposedReturnTime) {
          stepErrors.push('Return time is required');
        }
        if (!formData.returnLocation) {
          stepErrors.push('Return location is required');
        }
        
        // Date validation
        const returnDateTime = new Date(`${formData.proposedReturnDate}T${formData.proposedReturnTime}`);
        const now = new Date();
        if (returnDateTime <= now) {
          stepErrors.push('Return date and time must be in the future');
        }
        break;

      case 1: // Vehicle Condition
        if (formData.returnPhotos.length === 0) {
          stepErrors.push('At least one photo is required');
        }
        break;

      case 2: // Odometer & Fuel
        if (formData.estimatedOdometer <= 0) {
          stepErrors.push('Valid odometer reading is required');
        }
        if (formData.estimatedFuelLevel < 0 || formData.estimatedFuelLevel > 100) {
          stepErrors.push('Fuel level must be between 0 and 100');
        }
        break;

      case 3: // Review & Submit
        if (!formData.termsAccepted) {
          stepErrors.push('You must accept the terms and conditions');
        }
        if (!formData.declarationSigned) {
          stepErrors.push('You must sign the declaration');
        }
        break;
    }

    setErrors(stepErrors);
    return stepErrors.length === 0;
  };

  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
    setErrors([]);
  };

  const handlePhotoCapture = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      Array.from(files).forEach(file => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const photoUrl = e.target?.result as string;
          updateFormData({
            returnPhotos: [...formData.returnPhotos, photoUrl]
          });
        };
        reader.readAsDataURL(file);
      });
    }
  };

  const handleVideoCapture = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      Array.from(files).forEach(file => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const videoUrl = e.target?.result as string;
          updateFormData({
            returnVideos: [...formData.returnVideos, videoUrl]
          });
        };
        reader.readAsDataURL(file);
      });
    }
  };

  const removePhoto = (index: number) => {
    const newPhotos = formData.returnPhotos.filter((_, i) => i !== index);
    updateFormData({ returnPhotos: newPhotos });
  };

  const removeVideo = (index: number) => {
    const newVideos = formData.returnVideos.filter((_, i) => i !== index);
    updateFormData({ returnVideos: newVideos });
  };

  const addDamage = (damage: string) => {
    if (damage && !formData.reportedDamages.includes(damage)) {
      updateFormData({
        reportedDamages: [...formData.reportedDamages, damage]
      });
    }
  };

  const removeDamage = (damage: string) => {
    updateFormData({
      reportedDamages: formData.reportedDamages.filter(d => d !== damage)
    });
  };

  const handleSubmit = async () => {
    if (!validateStep(activeStep)) {
      return;
    }

    setLoading(true);
    try {
      // Calculate estimated charges
      const estimatedCharges = {
        fuelTopUp: formData.estimatedFuelLevel < 80 ? (80 - formData.estimatedFuelLevel) * 2 : 0,
        mileageOverage: 0, // Would need agreement details to calculate
        estimatedTotal: 0
      };
      estimatedCharges.estimatedTotal = estimatedCharges.fuelTopUp + estimatedCharges.mileageOverage;

      const returnRequest: CustomerReturnRequest = {
        id: `RET-${Date.now()}`,
        agreementId,
        customerId,
        proposedReturnDate: formData.proposedReturnDate,
        proposedReturnTime: formData.proposedReturnTime,
        returnLocation: formData.returnLocation,
        estimatedOdometer: formData.estimatedOdometer,
        estimatedFuelLevel: formData.estimatedFuelLevel,
        reportedDamages: formData.reportedDamages,
        returnPhotos: formData.returnPhotos,
        returnVideos: formData.returnVideos,
        customerNotes: formData.customerNotes,
        termsAccepted: formData.termsAccepted,
        declarationSigned: formData.declarationSigned,
        status: 'SUBMITTED',
        submittedAt: new Date().toISOString(),
        estimatedCharges
      };

      // TODO: Submit to API
      // await api.submitReturnRequest(token, returnRequest);

      onComplete(returnRequest);
    } catch (error: any) {
      setErrors([error.message || 'Failed to submit return request']);
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0: // Return Details
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                When and where would you like to return the vehicle?
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                label="Return Date"
                type="date"
                value={formData.proposedReturnDate}
                onChange={(e) => updateFormData({ proposedReturnDate: e.target.value })}
                fullWidth
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                label="Return Time"
                type="time"
                value={formData.proposedReturnTime}
                onChange={(e) => updateFormData({ proposedReturnTime: e.target.value })}
                fullWidth
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                label="Return Location"
                value={formData.returnLocation}
                onChange={(e) => updateFormData({ returnLocation: e.target.value })}
                fullWidth
                placeholder="e.g., Dubai Airport Terminal 1"
              />
            </Grid>
          </Grid>
        );

      case 1: // Vehicle Condition
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Document the vehicle condition
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Take photos of the vehicle from all angles. Report any damages you notice.
            </Typography>

            {/* Photo Capture */}
            <Box sx={{ mb: 3 }}>
              <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={<PhotoCameraIcon />}
                  onClick={() => photoInputRef.current?.click()}
                >
                  Take Photos
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<VideocamIcon />}
                  onClick={() => videoInputRef.current?.click()}
                >
                  Record Video
                </Button>
              </Stack>

              <input
                ref={photoInputRef}
                type="file"
                accept="image/*"
                multiple
                capture="environment"
                style={{ display: 'none' }}
                onChange={handlePhotoCapture}
              />
              <input
                ref={videoInputRef}
                type="file"
                accept="video/*"
                capture="environment"
                style={{ display: 'none' }}
                onChange={handleVideoCapture}
              />

              {/* Photo Gallery */}
              {formData.returnPhotos.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Photos ({formData.returnPhotos.length})
                  </Typography>
                  <Grid container spacing={1}>
                    {formData.returnPhotos.map((photo, index) => (
                      <Grid item xs={6} sm={4} md={3} key={index}>
                        <Paper sx={{ position: 'relative', p: 1 }}>
                          <img
                            src={photo}
                            alt={`Return photo ${index + 1}`}
                            style={{ width: '100%', height: 100, objectFit: 'cover' }}
                          />
                          <IconButton
                            size="small"
                            onClick={() => removePhoto(index)}
                            sx={{ position: 'absolute', top: 4, right: 4, bgcolor: 'rgba(255,255,255,0.8)' }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}

              {/* Video Gallery */}
              {formData.returnVideos.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Videos ({formData.returnVideos.length})
                  </Typography>
                  <Grid container spacing={1}>
                    {formData.returnVideos.map((video, index) => (
                      <Grid item xs={6} sm={4} md={3} key={index}>
                        <Paper sx={{ position: 'relative', p: 1 }}>
                          <video
                            src={video}
                            style={{ width: '100%', height: 100, objectFit: 'cover' }}
                            controls
                          />
                          <IconButton
                            size="small"
                            onClick={() => removeVideo(index)}
                            sx={{ position: 'absolute', top: 4, right: 4, bgcolor: 'rgba(255,255,255,0.8)' }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}
            </Box>

            {/* Damage Reporting */}
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Report any damages (optional)
              </Typography>
              <TextField
                placeholder="Describe any damages you notice..."
                fullWidth
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    addDamage((e.target as HTMLInputElement).value);
                    (e.target as HTMLInputElement).value = '';
                  }
                }}
                helperText="Press Enter to add damage report"
              />
              
              {formData.reportedDamages.length > 0 && (
                <Box sx={{ mt: 2 }}>
                  <Stack direction="row" spacing={1} flexWrap="wrap">
                    {formData.reportedDamages.map((damage, index) => (
                      <Chip
                        key={index}
                        label={damage}
                        onDelete={() => removeDamage(damage)}
                        color="warning"
                        variant="outlined"
                      />
                    ))}
                  </Stack>
                </Box>
              )}
            </Box>
          </Box>
        );

      case 2: // Odometer & Fuel
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Current vehicle readings
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                label="Current Odometer Reading"
                type="number"
                value={formData.estimatedOdometer}
                onChange={(e) => updateFormData({ estimatedOdometer: Number(e.target.value) })}
                fullWidth
                inputProps={{ min: 0 }}
                helperText="Enter the current odometer reading in kilometers"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                label="Current Fuel Level (%)"
                type="number"
                value={formData.estimatedFuelLevel}
                onChange={(e) => updateFormData({ estimatedFuelLevel: Number(e.target.value) })}
                fullWidth
                inputProps={{ min: 0, max: 100 }}
                helperText="Estimate the current fuel level percentage"
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                label="Additional Notes"
                value={formData.customerNotes}
                onChange={(e) => updateFormData({ customerNotes: e.target.value })}
                fullWidth
                multiline
                rows={3}
                placeholder="Any additional information about the vehicle condition or return..."
              />
            </Grid>
          </Grid>
        );

      case 3: // Review & Submit
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Review your return request
            </Typography>
            
            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Return Date & Time:</Typography>
                <Typography>{formData.proposedReturnDate} at {formData.proposedReturnTime}</Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Return Location:</Typography>
                <Typography>{formData.returnLocation}</Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Odometer Reading:</Typography>
                <Typography>{formData.estimatedOdometer.toLocaleString()} km</Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Fuel Level:</Typography>
                <Typography>{formData.estimatedFuelLevel}%</Typography>
              </Grid>
              
              <Grid item xs={12}>
                <Typography variant="subtitle2">Photos/Videos:</Typography>
                <Typography>{formData.returnPhotos.length} photos, {formData.returnVideos.length} videos</Typography>
              </Grid>
              
              {formData.reportedDamages.length > 0 && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2">Reported Damages:</Typography>
                  <Typography>{formData.reportedDamages.join(', ')}</Typography>
                </Grid>
              )}
            </Grid>

            <Box sx={{ mb: 3 }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.termsAccepted}
                    onChange={(e) => updateFormData({ termsAccepted: e.target.checked })}
                  />
                }
                label="I accept the terms and conditions for vehicle return"
              />
              
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.declarationSigned}
                    onChange={(e) => updateFormData({ declarationSigned: e.target.checked })}
                  />
                }
                label="I declare that the information provided is accurate to the best of my knowledge"
              />
            </Box>

            <Alert severity="info">
              <Typography variant="body2">
                This is a return request. An agent will review your submission and contact you to confirm the return process.
                Estimated processing time: 2-4 hours during business hours.
              </Typography>
            </Alert>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <Card>
        <CardContent>
          <Box sx={{ mb: 4 }}>
            <Typography variant="h5" gutterBottom>
              Vehicle Return Request
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Submit your vehicle return request with photos and condition details
            </Typography>
          </Box>

          {loading && <LinearProgress sx={{ mb: 2 }} />}

          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            {steps.map((step, index) => (
              <Step key={step.label}>
                <StepLabel>
                  <Typography variant="subtitle2">{step.label}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {step.description}
                  </Typography>
                </StepLabel>
              </Step>
            ))}
          </Stepper>

          {errors.length > 0 && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {errors.map((error, index) => (
                <div key={index}>{error}</div>
              ))}
            </Alert>
          )}

          <Box sx={{ mb: 4 }}>
            {renderStepContent(activeStep)}
          </Box>

          <Stack direction="row" spacing={2} justifyContent="space-between">
            <Button
              variant="outlined"
              onClick={() => setShowCancelDialog(true)}
              disabled={loading}
            >
              Cancel
            </Button>

            <Stack direction="row" spacing={2}>
              <Button
                onClick={handleBack}
                disabled={activeStep === 0 || loading}
              >
                Back
              </Button>
              
              {activeStep === steps.length - 1 ? (
                <Button
                  variant="contained"
                  onClick={handleSubmit}
                  disabled={loading}
                  startIcon={<CheckCircleIcon />}
                >
                  Submit Return Request
                </Button>
              ) : (
                <Button
                  variant="contained"
                  onClick={handleNext}
                  disabled={loading}
                >
                  Next
                </Button>
              )}
            </Stack>
          </Stack>
        </CardContent>
      </Card>

      {/* Cancel Confirmation Dialog */}
      <Dialog open={showCancelDialog} onClose={() => setShowCancelDialog(false)}>
        <DialogTitle>Cancel Return Request</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to cancel? All entered data will be lost.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowCancelDialog(false)}>
            Continue
          </Button>
          <Button onClick={onCancel} color="error">
            Cancel Request
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
