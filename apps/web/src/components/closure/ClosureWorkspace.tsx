import {
  <PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ack,
  Alert,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip
} from '@mui/material';
import { useState, useEffect } from 'react';
import { ClosureFormData, STRAgreement, ClosurePermissions } from '../../types/str';
import { getClosurePermissions, validateClosureBusinessRules, calculateSettlement } from '../../utils/strPermissions';

// Step Components (to be created)
// import AgreementLookupStep from './steps/AgreementLookupStep';
// import VehicleCheckStep from './steps/VehicleCheckStep';
// import AdditionalChargesStep from './steps/AdditionalChargesStep';
// import SettlementStep from './steps/SettlementStep';
// import FinalClosureStep from './steps/FinalClosureStep';

interface ClosureWorkspaceProps {
  agreementId?: string;
  userRoles: string[];
  token: string;
  onComplete: (closedAgreement: STRAgreement) => void;
  onCancel: () => void;
}

const steps = [
  {
    label: 'Agreement Lookup',
    description: 'Find and validate agreement'
  },
  {
    label: 'Vehicle Check',
    description: 'Inspect vehicle condition'
  },
  {
    label: 'Additional Charges',
    description: 'Add charges and fees'
  },
  {
    label: 'Settlement',
    description: 'Process payment and refunds'
  },
  {
    label: 'Final Closure',
    description: 'Generate documents and close'
  }
];

export default function ClosureWorkspace({
  agreementId,
  userRoles,
  token,
  onComplete,
  onCancel
}: ClosureWorkspaceProps) {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState<ClosureFormData>({
    agreementId: agreementId || '',
    returnDate: new Date().toISOString().split('T')[0],
    returnTime: new Date().toTimeString().slice(0, 5),
    returnLocation: '',
    odometerReading: 0,
    fuelLevel: 100,
    vehicleCondition: {
      exterior: [],
      interior: [],
      mechanical: [],
      accessories: [],
      overallRating: 'GOOD'
    },
    damageReport: [],
    returnPhotos: [],
    additionalCharges: [],
    salikCharges: [],
    fineCharges: [],
    documentsGenerated: [],
    closureNotes: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [warnings, setWarnings] = useState<string[]>([]);
  const [showCancelDialog, setShowCancelDialog] = useState(false);

  const permissions = getClosurePermissions(userRoles);

  useEffect(() => {
    if (agreementId) {
      loadAgreement(agreementId);
    }
  }, [agreementId]);

  const loadAgreement = async (id: string) => {
    setLoading(true);
    try {
      // TODO: Implement API call to load agreement
      // const agreement = await api.getAgreement(token, id);
      
      // Mock agreement for now
      const mockAgreement: STRAgreement = {
        id: id,
        agreementNumber: `STR-${id}`,
        customerId: 'CUST-001',
        vehicleId: 'VEH-001',
        startDate: '2024-01-15',
        endDate: '2024-01-20',
        duration: 5,
        status: 'ACTIVE',
        workflowStage: 'ACTIVE',
        pricing: {
          baseAmount: 1000,
          additionalCharges: 0,
          discounts: 0,
          taxes: 50,
          totalAmount: 1050,
          currency: 'AED',
          breakdown: []
        },
        handoverDetails: {
          handoverDate: '2024-01-15T10:00:00Z',
          handoverLocation: 'Dubai Airport',
          odometerReading: 50000,
          fuelLevel: 100,
          vehicleCondition: {
            exterior: [],
            interior: [],
            mechanical: [],
            accessories: [],
            overallRating: 'EXCELLENT'
          },
          documentsProvided: [],
          handoverNotes: '',
          handoverBy: 'agent-001'
        },
        documents: [],
        requiredDocuments: [],
        createdBy: 'agent-001',
        createdAt: '2024-01-15T09:00:00Z',
        updatedBy: 'agent-001',
        updatedAt: '2024-01-15T09:00:00Z',
        auditLog: [],
        allowExtension: true,
        allowEarlyReturn: true,
        requiresDeposit: true,
        totalAmount: 1050,
        paidAmount: 1050,
        outstandingAmount: 0,
        depositAmount: 500,
        refundableDeposit: 500
      };

      setFormData(prev => ({
        ...prev,
        agreement: mockAgreement,
        odometerReading: mockAgreement.handoverDetails?.odometerReading || 0
      }));
    } catch (error: any) {
      setErrors([error.message || 'Failed to load agreement']);
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (updates: Partial<ClosureFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
    setErrors([]);
    setWarnings([]);
  };

  const validateStep = (step: number): boolean => {
    const stepErrors: string[] = [];
    const stepWarnings: string[] = [];

    switch (step) {
      case 0: // Agreement Lookup
        if (!formData.agreementId) {
          stepErrors.push('Agreement ID is required');
        }
        if (!formData.agreement) {
          stepErrors.push('Agreement not found or not loaded');
        }
        break;

      case 1: // Vehicle Check
        if (!formData.returnDate) {
          stepErrors.push('Return date is required');
        }
        if (!formData.returnLocation) {
          stepErrors.push('Return location is required');
        }
        if (formData.odometerReading <= 0) {
          stepErrors.push('Valid odometer reading is required');
        }
        if (formData.fuelLevel < 0 || formData.fuelLevel > 100) {
          stepErrors.push('Fuel level must be between 0 and 100');
        }
        break;

      case 2: // Additional Charges
        // Validate charges if any
        const invalidCharges = formData.additionalCharges.filter(c => c.amount <= 0);
        if (invalidCharges.length > 0) {
          stepErrors.push('All charges must have positive amounts');
        }
        break;

      case 3: // Settlement
        if (formData.agreement && formData.settlementSummary) {
          const settlement = formData.settlementSummary;
          if (settlement.netAmount > 0 && !formData.paymentMethod) {
            stepErrors.push('Payment method required for outstanding balance');
          }
          if (settlement.netAmount < 0 && !formData.refundMethod) {
            stepErrors.push('Refund method required for refund amount');
          }
        }
        break;

      case 4: // Final Closure
        if (!formData.closureNotes) {
          stepWarnings.push('Closure notes are recommended');
        }
        break;
    }

    // Business rule validation
    if (formData.agreement) {
      const validation = validateClosureBusinessRules(formData.agreement, formData, userRoles);
      stepErrors.push(...validation.errors);
      stepWarnings.push(...validation.warnings);
    }

    setErrors(stepErrors);
    setWarnings(stepWarnings);
    return stepErrors.length === 0;
  };

  const handleNext = () => {
    if (validateStep(activeStep)) {
      // Calculate settlement when moving to settlement step
      if (activeStep === 2 && formData.agreement) {
        const settlement = calculateSettlement(formData.agreement, formData.additionalCharges);
        updateFormData({ settlementSummary: settlement });
      }
      
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
    setErrors([]);
    setWarnings([]);
  };

  const handleSubmit = async () => {
    if (!validateStep(activeStep) || !formData.agreement) {
      return;
    }

    setLoading(true);
    try {
      // TODO: Implement API call to close agreement
      // const result = await api.closeAgreement(token, formData.agreementId, {
      //   returnDate: `${formData.returnDate}T${formData.returnTime}:00Z`,
      //   returnLocation: formData.returnLocation,
      //   odometerReading: formData.odometerReading,
      //   fuelLevel: formData.fuelLevel,
      //   vehicleCondition: formData.vehicleCondition,
      //   damageReport: formData.damageReport,
      //   additionalCharges: formData.additionalCharges,
      //   settlementSummary: formData.settlementSummary,
      //   closureNotes: formData.closureNotes
      // });

      // Mock successful closure
      const closedAgreement: STRAgreement = {
        ...formData.agreement,
        status: 'CLOSED',
        workflowStage: 'CLOSED',
        actualEndDate: `${formData.returnDate}T${formData.returnTime}:00Z`,
        returnDetails: {
          returnDate: `${formData.returnDate}T${formData.returnTime}:00Z`,
          returnLocation: formData.returnLocation,
          odometerReading: formData.odometerReading,
          fuelLevel: formData.fuelLevel,
          vehicleCondition: formData.vehicleCondition,
          damageReport: formData.damageReport,
          returnNotes: formData.closureNotes,
          returnPhotos: formData.returnPhotos,
          returnBy: 'current-user'
        }
      };

      onComplete(closedAgreement);
    } catch (error: any) {
      setErrors([error.message || 'Failed to close agreement']);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setShowCancelDialog(true);
  };

  const confirmCancel = () => {
    setShowCancelDialog(false);
    onCancel();
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>Agreement Lookup & Validation</Typography>
            <Typography>Step content coming soon...</Typography>
          </Box>
        );
      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>Vehicle Check</Typography>
            <Typography>Step content coming soon...</Typography>
          </Box>
        );
      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>Additional Charges</Typography>
            <Typography>Step content coming soon...</Typography>
          </Box>
        );
      case 3:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>Settlement</Typography>
            <Typography>Step content coming soon...</Typography>
          </Box>
        );
      case 4:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>Final Closure</Typography>
            <Typography>Step content coming soon...</Typography>
          </Box>
        );
      default:
        return null;
    }
  };

  // Check permissions
  if (!permissions.canInitiateClosure) {
    return (
      <Card>
        <CardContent>
          <Alert severity="error">
            You do not have permission to initiate agreement closures.
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardContent>
          <Box sx={{ mb: 4 }}>
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Box>
                <Typography variant="h5" gutterBottom>
                  Agreement Closure Workspace
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Process vehicle return and close agreement
                </Typography>
              </Box>
              
              {formData.agreement && (
                <Stack direction="row" spacing={1}>
                  <Chip 
                    label={formData.agreement.agreementNumber} 
                    color="primary" 
                    variant="outlined" 
                  />
                  <Chip 
                    label={formData.agreement.status} 
                    color="success" 
                    size="small" 
                  />
                </Stack>
              )}
            </Stack>
          </Box>

          {loading && <LinearProgress sx={{ mb: 2 }} />}

          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            {steps.map((step, index) => (
              <Step key={step.label}>
                <StepLabel>
                  <Typography variant="subtitle2">{step.label}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {step.description}
                  </Typography>
                </StepLabel>
              </Step>
            ))}
          </Stepper>

          {errors.length > 0 && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {errors.map((error, index) => (
                <div key={index}>{error}</div>
              ))}
            </Alert>
          )}

          {warnings.length > 0 && (
            <Alert severity="warning" sx={{ mb: 3 }}>
              {warnings.map((warning, index) => (
                <div key={index}>{warning}</div>
              ))}
            </Alert>
          )}

          <Box sx={{ mb: 4 }}>
            {renderStepContent(activeStep)}
          </Box>

          <Stack direction="row" spacing={2} justifyContent="space-between">
            <Button
              variant="outlined"
              onClick={handleCancel}
              disabled={loading}
            >
              Cancel
            </Button>

            <Stack direction="row" spacing={2}>
              <Button
                onClick={handleBack}
                disabled={activeStep === 0 || loading}
              >
                Back
              </Button>
              
              {activeStep === steps.length - 1 ? (
                <Button
                  variant="contained"
                  onClick={handleSubmit}
                  disabled={loading}
                  color="error"
                >
                  Close Agreement
                </Button>
              ) : (
                <Button
                  variant="contained"
                  onClick={handleNext}
                  disabled={loading}
                >
                  Next
                </Button>
              )}
            </Stack>
          </Stack>
        </CardContent>
      </Card>

      {/* Cancel Confirmation Dialog */}
      <Dialog open={showCancelDialog} onClose={() => setShowCancelDialog(false)}>
        <DialogTitle>Cancel Closure Process</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to cancel the closure process? All entered data will be lost.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowCancelDialog(false)}>
            Continue Closure
          </Button>
          <Button onClick={confirmCancel} color="error">
            Cancel Process
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
