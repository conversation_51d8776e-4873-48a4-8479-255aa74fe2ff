import {
  Box,
  Card,
  Card<PERSON>ontent,
  CardHeader,
  Grid,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  IconButton,
  Stack,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  ImageList,
  ImageListItem,
  ImageListItemBar
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  PhotoCamera as PhotoCameraIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { useState, useRef } from 'react';
import { VehicleCondition, DamageItem, ConditionItem } from '../../types/str';

interface DamageChecklistProps {
  vehicleCondition: VehicleCondition;
  damageReport: DamageItem[];
  onUpdate: (condition: VehicleCondition, damages: DamageItem[]) => void;
  editable: boolean;
  showPhotos?: boolean;
}

const vehicleAreas = {
  exterior: [
    'Front Bumper', 'Rear Bumper', 'Left Side', 'Right Side', 
    'Hood', 'Roof', 'Trunk', 'Windshield', 'Rear Window',
    'Left Headlight', 'Right Headlight', 'Left Taillight', 'Right Taillight'
  ],
  interior: [
    'Dashboard', 'Steering Wheel', 'Seats', 'Floor Mats',
    'Door Panels', 'Center Console', 'Glove Box', 'Mirrors'
  ],
  mechanical: [
    'Engine', 'Transmission', 'Brakes', 'Tires',
    'Air Conditioning', 'Electrical System', 'Exhaust'
  ],
  accessories: [
    'Spare Tire', 'Jack', 'Tool Kit', 'First Aid Kit',
    'Fire Extinguisher', 'Warning Triangle', 'Manual'
  ]
};

export default function DamageChecklist({
  vehicleCondition,
  damageReport,
  onUpdate,
  editable,
  showPhotos = true
}: DamageChecklistProps) {
  const [selectedCategory, setSelectedCategory] = useState<keyof typeof vehicleAreas>('exterior');
  const [showAddDamage, setShowAddDamage] = useState(false);
  const [showPhotoDialog, setShowPhotoDialog] = useState(false);
  const [selectedPhotos, setSelectedPhotos] = useState<string[]>([]);
  const [newDamage, setNewDamage] = useState({
    area: '',
    description: '',
    severity: 'MINOR' as 'MINOR' | 'MODERATE' | 'MAJOR',
    estimatedCost: 0,
    photos: [] as string[]
  });

  const photoInputRef = useRef<HTMLInputElement>(null);

  const updateConditionItem = (category: keyof typeof vehicleAreas, area: string, condition: string, notes?: string) => {
    const updatedCondition = { ...vehicleCondition };
    const existingIndex = updatedCondition[category].findIndex(item => item.area === area);
    
    const conditionItem: ConditionItem = {
      area,
      condition: condition as any,
      notes: notes || ''
    };

    if (existingIndex >= 0) {
      updatedCondition[category][existingIndex] = conditionItem;
    } else {
      updatedCondition[category].push(conditionItem);
    }

    onUpdate(updatedCondition, damageReport);
  };

  const getConditionForArea = (category: keyof typeof vehicleAreas, area: string): ConditionItem | undefined => {
    return vehicleCondition[category].find(item => item.area === area);
  };

  const addDamage = () => {
    if (!newDamage.area || !newDamage.description) return;

    const damage: DamageItem = {
      id: `DMG-${Date.now()}`,
      area: newDamage.area,
      description: newDamage.description,
      severity: newDamage.severity,
      estimatedCost: newDamage.estimatedCost,
      photos: newDamage.photos,
      approved: false,
      waived: false
    };

    onUpdate(vehicleCondition, [...damageReport, damage]);
    setNewDamage({
      area: '',
      description: '',
      severity: 'MINOR',
      estimatedCost: 0,
      photos: []
    });
    setShowAddDamage(false);
  };

  const removeDamage = (damageId: string) => {
    const updatedDamages = damageReport.filter(d => d.id !== damageId);
    onUpdate(vehicleCondition, updatedDamages);
  };

  const updateDamage = (damageId: string, updates: Partial<DamageItem>) => {
    const updatedDamages = damageReport.map(d => 
      d.id === damageId ? { ...d, ...updates } : d
    );
    onUpdate(vehicleCondition, updatedDamages);
  };

  const handlePhotoCapture = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      Array.from(files).forEach(file => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const photoUrl = e.target?.result as string;
          setNewDamage(prev => ({
            ...prev,
            photos: [...prev.photos, photoUrl]
          }));
        };
        reader.readAsDataURL(file);
      });
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'MINOR': return 'info';
      case 'MODERATE': return 'warning';
      case 'MAJOR': return 'error';
      default: return 'default';
    }
  };

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case 'EXCELLENT': return 'success';
      case 'GOOD': return 'info';
      case 'FAIR': return 'warning';
      case 'POOR': return 'error';
      case 'DAMAGED': return 'error';
      default: return 'default';
    }
  };

  return (
    <Box>
      <Grid container spacing={3}>
        {/* Vehicle Condition Checklist */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardHeader 
              title="Vehicle Condition Assessment"
              action={
                editable && (
                  <Button
                    variant="outlined"
                    startIcon={<AddIcon />}
                    onClick={() => setShowAddDamage(true)}
                  >
                    Report Damage
                  </Button>
                )
              }
            />
            <CardContent>
              {/* Category Tabs */}
              <Stack direction="row" spacing={1} sx={{ mb: 3 }}>
                {Object.keys(vehicleAreas).map((category) => (
                  <Chip
                    key={category}
                    label={category.charAt(0).toUpperCase() + category.slice(1)}
                    onClick={() => setSelectedCategory(category as keyof typeof vehicleAreas)}
                    color={selectedCategory === category ? 'primary' : 'default'}
                    variant={selectedCategory === category ? 'filled' : 'outlined'}
                  />
                ))}
              </Stack>

              {/* Condition Items */}
              <Grid container spacing={2}>
                {vehicleAreas[selectedCategory].map((area) => {
                  const conditionItem = getConditionForArea(selectedCategory, area);
                  return (
                    <Grid item xs={12} sm={6} md={4} key={area}>
                      <Card variant="outlined" sx={{ p: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          {area}
                        </Typography>
                        
                        {editable ? (
                          <Stack spacing={1}>
                            <FormControl size="small" fullWidth>
                              <Select
                                value={conditionItem?.condition || 'EXCELLENT'}
                                onChange={(e) => updateConditionItem(
                                  selectedCategory, 
                                  area, 
                                  e.target.value,
                                  conditionItem?.notes
                                )}
                              >
                                <MenuItem value="EXCELLENT">Excellent</MenuItem>
                                <MenuItem value="GOOD">Good</MenuItem>
                                <MenuItem value="FAIR">Fair</MenuItem>
                                <MenuItem value="POOR">Poor</MenuItem>
                                <MenuItem value="DAMAGED">Damaged</MenuItem>
                              </Select>
                            </FormControl>
                            
                            <TextField
                              size="small"
                              placeholder="Notes..."
                              value={conditionItem?.notes || ''}
                              onChange={(e) => updateConditionItem(
                                selectedCategory,
                                area,
                                conditionItem?.condition || 'EXCELLENT',
                                e.target.value
                              )}
                              multiline
                              rows={2}
                            />
                          </Stack>
                        ) : (
                          <Box>
                            <Chip
                              label={conditionItem?.condition || 'Not Assessed'}
                              color={getConditionColor(conditionItem?.condition || '') as any}
                              size="small"
                            />
                            {conditionItem?.notes && (
                              <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                                {conditionItem.notes}
                              </Typography>
                            )}
                          </Box>
                        )}
                      </Card>
                    </Grid>
                  );
                })}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Damage Report */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader title="Damage Report" />
            <CardContent>
              {damageReport.length === 0 ? (
                <Typography variant="body2" color="text.secondary">
                  No damages reported
                </Typography>
              ) : (
                <Stack spacing={2}>
                  {damageReport.map((damage) => (
                    <Card key={damage.id} variant="outlined">
                      <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                        <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                          <Box sx={{ flex: 1 }}>
                            <Typography variant="subtitle2" gutterBottom>
                              {damage.area}
                            </Typography>
                            <Typography variant="body2" sx={{ mb: 1 }}>
                              {damage.description}
                            </Typography>
                            <Stack direction="row" spacing={1} alignItems="center">
                              <Chip
                                label={damage.severity}
                                color={getSeverityColor(damage.severity) as any}
                                size="small"
                              />
                              <Typography variant="caption">
                                AED {damage.estimatedCost}
                              </Typography>
                            </Stack>
                            
                            {damage.photos.length > 0 && showPhotos && (
                              <Button
                                size="small"
                                startIcon={<VisibilityIcon />}
                                onClick={() => {
                                  setSelectedPhotos(damage.photos);
                                  setShowPhotoDialog(true);
                                }}
                                sx={{ mt: 1 }}
                              >
                                View Photos ({damage.photos.length})
                              </Button>
                            )}
                          </Box>
                          
                          {editable && (
                            <IconButton
                              size="small"
                              onClick={() => removeDamage(damage.id)}
                              color="error"
                            >
                              <DeleteIcon />
                            </IconButton>
                          )}
                        </Stack>
                      </CardContent>
                    </Card>
                  ))}
                </Stack>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Add Damage Dialog */}
      <Dialog open={showAddDamage} onClose={() => setShowAddDamage(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Report Vehicle Damage</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              label="Area/Location"
              value={newDamage.area}
              onChange={(e) => setNewDamage(prev => ({ ...prev, area: e.target.value }))}
              fullWidth
              placeholder="e.g., Front Bumper, Driver Door"
            />
            
            <TextField
              label="Description"
              value={newDamage.description}
              onChange={(e) => setNewDamage(prev => ({ ...prev, description: e.target.value }))}
              fullWidth
              multiline
              rows={3}
              placeholder="Describe the damage in detail..."
            />
            
            <FormControl fullWidth>
              <InputLabel>Severity</InputLabel>
              <Select
                value={newDamage.severity}
                label="Severity"
                onChange={(e) => setNewDamage(prev => ({ ...prev, severity: e.target.value as any }))}
              >
                <MenuItem value="MINOR">Minor</MenuItem>
                <MenuItem value="MODERATE">Moderate</MenuItem>
                <MenuItem value="MAJOR">Major</MenuItem>
              </Select>
            </FormControl>
            
            <TextField
              label="Estimated Cost (AED)"
              type="number"
              value={newDamage.estimatedCost}
              onChange={(e) => setNewDamage(prev => ({ ...prev, estimatedCost: Number(e.target.value) }))}
              fullWidth
              inputProps={{ min: 0 }}
            />
            
            <Box>
              <Button
                variant="outlined"
                startIcon={<PhotoCameraIcon />}
                onClick={() => photoInputRef.current?.click()}
                sx={{ mb: 2 }}
              >
                Add Photos
              </Button>
              
              <input
                ref={photoInputRef}
                type="file"
                accept="image/*"
                multiple
                capture="environment"
                style={{ display: 'none' }}
                onChange={handlePhotoCapture}
              />
              
              {newDamage.photos.length > 0 && (
                <Typography variant="body2" color="text.secondary">
                  {newDamage.photos.length} photo(s) added
                </Typography>
              )}
            </Box>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAddDamage(false)}>Cancel</Button>
          <Button
            onClick={addDamage}
            variant="contained"
            disabled={!newDamage.area || !newDamage.description}
          >
            Add Damage
          </Button>
        </DialogActions>
      </Dialog>

      {/* Photo Viewer Dialog */}
      <Dialog open={showPhotoDialog} onClose={() => setShowPhotoDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Damage Photos</DialogTitle>
        <DialogContent>
          <ImageList cols={2} gap={8}>
            {selectedPhotos.map((photo, index) => (
              <ImageListItem key={index}>
                <img
                  src={photo}
                  alt={`Damage photo ${index + 1}`}
                  loading="lazy"
                  style={{ height: 200, objectFit: 'cover' }}
                />
                <ImageListItemBar
                  title={`Photo ${index + 1}`}
                />
              </ImageListItem>
            ))}
          </ImageList>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowPhotoDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
