import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  IconButton,
  Stack,
  Chip,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  Checkbox,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  MoneyOff as MoneyOffIcon
} from '@mui/icons-material';
import { useState } from 'react';
import { AdditionalCharge } from '../../types/str';
import { getChargeTypeLabel } from '../../utils/strPermissions';

interface ChargesTableProps {
  charges: AdditionalCharge[];
  onUpdate: (charges: AdditionalCharge[]) => void;
  editable: boolean;
  userRoles: string[];
  showApproval?: boolean;
}

export default function ChargesTable({
  charges,
  onUpdate,
  editable,
  userRoles,
  showApproval = true
}: ChargesTableProps) {
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [showWaiveDialog, setShowWaiveDialog] = useState(false);
  const [selectedChargeId, setSelectedChargeId] = useState<string>('');
  const [waiveReason, setWaiveReason] = useState('');
  
  const [newCharge, setNewCharge] = useState({
    type: 'OTHER' as AdditionalCharge['type'],
    description: '',
    quantity: 1,
    unitPrice: 0,
    taxable: true,
    notes: ''
  });

  const isAdmin = userRoles.includes('admin');
  const canWaive = isAdmin;
  const canApprove = isAdmin || userRoles.includes('agent');

  const addCharge = () => {
    if (!newCharge.description || newCharge.unitPrice <= 0) return;

    const charge: AdditionalCharge = {
      id: `CHG-${Date.now()}`,
      type: newCharge.type,
      description: newCharge.description,
      amount: newCharge.quantity * newCharge.unitPrice,
      quantity: newCharge.quantity,
      unitPrice: newCharge.unitPrice,
      taxable: newCharge.taxable,
      approved: false,
      waived: false,
      notes: newCharge.notes
    };

    onUpdate([...charges, charge]);
    setNewCharge({
      type: 'OTHER',
      description: '',
      quantity: 1,
      unitPrice: 0,
      taxable: true,
      notes: ''
    });
    setShowAddDialog(false);
  };

  const updateCharge = (chargeId: string, updates: Partial<AdditionalCharge>) => {
    const updatedCharges = charges.map(charge => {
      if (charge.id === chargeId) {
        const updated = { ...charge, ...updates };
        // Recalculate amount if quantity or unit price changed
        if (updates.quantity !== undefined || updates.unitPrice !== undefined) {
          updated.amount = updated.quantity * updated.unitPrice;
        }
        return updated;
      }
      return charge;
    });
    onUpdate(updatedCharges);
  };

  const removeCharge = (chargeId: string) => {
    const updatedCharges = charges.filter(c => c.id !== chargeId);
    onUpdate(updatedCharges);
  };

  const approveCharge = (chargeId: string) => {
    updateCharge(chargeId, { 
      approved: true, 
      approvedBy: 'current-user' // TODO: Get from user context
    });
  };

  const waiveCharge = () => {
    if (!selectedChargeId || !waiveReason) return;
    
    updateCharge(selectedChargeId, {
      waived: true,
      waivedBy: 'current-user', // TODO: Get from user context
      waivedReason: waiveReason
    });
    
    setShowWaiveDialog(false);
    setSelectedChargeId('');
    setWaiveReason('');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: 'AED',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const getChargeTypeColor = (type: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      'DAMAGE': 'error',
      'FUEL': 'warning',
      'MILEAGE': 'info',
      'CLEANING': 'secondary',
      'LATE_RETURN': 'warning',
      'SALIK': 'primary',
      'FINE': 'error',
      'OTHER': 'default'
    };
    return colors[type] || 'default';
  };

  const totalCharges = charges
    .filter(c => !c.waived)
    .reduce((sum, c) => sum + c.amount, 0);

  const totalTax = charges
    .filter(c => c.taxable && !c.waived)
    .reduce((sum, c) => sum + (c.amount * 0.05), 0);

  return (
    <Box>
      <Card>
        <CardHeader
          title="Additional Charges"
          subheader={`${charges.length} charges • Total: ${formatCurrency(totalCharges + totalTax)}`}
          action={
            editable && (
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={() => setShowAddDialog(true)}
              >
                Add Charge
              </Button>
            )
          }
        />
        <CardContent>
          {charges.length === 0 ? (
            <Alert severity="info">
              No additional charges added yet.
            </Alert>
          ) : (
            <>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Type</TableCell>
                      <TableCell>Description</TableCell>
                      <TableCell align="center">Qty</TableCell>
                      <TableCell align="right">Unit Price</TableCell>
                      <TableCell align="right">Amount</TableCell>
                      <TableCell align="center">Tax</TableCell>
                      {showApproval && <TableCell align="center">Status</TableCell>}
                      {editable && <TableCell align="center">Actions</TableCell>}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {charges.map((charge) => (
                      <TableRow key={charge.id}>
                        <TableCell>
                          <Chip
                            label={getChargeTypeLabel(charge.type)}
                            color={getChargeTypeColor(charge.type)}
                            size="small"
                          />
                        </TableCell>
                        
                        <TableCell>
                          {editingId === charge.id ? (
                            <TextField
                              size="small"
                              value={charge.description}
                              onChange={(e) => updateCharge(charge.id, { description: e.target.value })}
                              fullWidth
                            />
                          ) : (
                            <Box>
                              <Typography variant="body2">{charge.description}</Typography>
                              {charge.notes && (
                                <Typography variant="caption" color="text.secondary">
                                  {charge.notes}
                                </Typography>
                              )}
                            </Box>
                          )}
                        </TableCell>
                        
                        <TableCell align="center">
                          {editingId === charge.id ? (
                            <TextField
                              size="small"
                              type="number"
                              value={charge.quantity}
                              onChange={(e) => updateCharge(charge.id, { quantity: Number(e.target.value) })}
                              inputProps={{ min: 1 }}
                              sx={{ width: 80 }}
                            />
                          ) : (
                            charge.quantity
                          )}
                        </TableCell>
                        
                        <TableCell align="right">
                          {editingId === charge.id ? (
                            <TextField
                              size="small"
                              type="number"
                              value={charge.unitPrice}
                              onChange={(e) => updateCharge(charge.id, { unitPrice: Number(e.target.value) })}
                              inputProps={{ min: 0, step: 0.01 }}
                              sx={{ width: 100 }}
                            />
                          ) : (
                            formatCurrency(charge.unitPrice)
                          )}
                        </TableCell>
                        
                        <TableCell align="right">
                          <Typography 
                            variant="body2" 
                            fontWeight={charge.waived ? 'normal' : 'bold'}
                            sx={{ 
                              textDecoration: charge.waived ? 'line-through' : 'none',
                              color: charge.waived ? 'text.secondary' : 'inherit'
                            }}
                          >
                            {formatCurrency(charge.amount)}
                          </Typography>
                          {charge.waived && (
                            <Typography variant="caption" color="success.main">
                              Waived
                            </Typography>
                          )}
                        </TableCell>
                        
                        <TableCell align="center">
                          <Chip
                            label={charge.taxable ? 'Yes' : 'No'}
                            color={charge.taxable ? 'primary' : 'default'}
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                        
                        {showApproval && (
                          <TableCell align="center">
                            <Stack spacing={0.5}>
                              {charge.approved ? (
                                <Chip label="Approved" color="success" size="small" />
                              ) : (
                                <Chip label="Pending" color="warning" size="small" />
                              )}
                              
                              {charge.waived && (
                                <Typography variant="caption" color="success.main">
                                  Waived by {charge.waivedBy}
                                </Typography>
                              )}
                            </Stack>
                          </TableCell>
                        )}
                        
                        {editable && (
                          <TableCell align="center">
                            <Stack direction="row" spacing={0.5}>
                              {editingId === charge.id ? (
                                <>
                                  <IconButton
                                    size="small"
                                    onClick={() => setEditingId(null)}
                                    color="primary"
                                  >
                                    <CheckIcon />
                                  </IconButton>
                                  <IconButton
                                    size="small"
                                    onClick={() => setEditingId(null)}
                                  >
                                    <CloseIcon />
                                  </IconButton>
                                </>
                              ) : (
                                <>
                                  <IconButton
                                    size="small"
                                    onClick={() => setEditingId(charge.id)}
                                  >
                                    <EditIcon />
                                  </IconButton>
                                  
                                  {canApprove && !charge.approved && (
                                    <IconButton
                                      size="small"
                                      onClick={() => approveCharge(charge.id)}
                                      color="success"
                                    >
                                      <CheckIcon />
                                    </IconButton>
                                  )}
                                  
                                  {canWaive && !charge.waived && (
                                    <IconButton
                                      size="small"
                                      onClick={() => {
                                        setSelectedChargeId(charge.id);
                                        setShowWaiveDialog(true);
                                      }}
                                      color="warning"
                                    >
                                      <MoneyOffIcon />
                                    </IconButton>
                                  )}
                                  
                                  <IconButton
                                    size="small"
                                    onClick={() => removeCharge(charge.id)}
                                    color="error"
                                  >
                                    <DeleteIcon />
                                  </IconButton>
                                </>
                              )}
                            </Stack>
                          </TableCell>
                        )}
                      </TableRow>
                    ))}
                    
                    {/* Summary Row */}
                    <TableRow>
                      <TableCell colSpan={4} align="right">
                        <Typography variant="subtitle2">Subtotal:</Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="subtitle2" fontWeight="bold">
                          {formatCurrency(totalCharges)}
                        </Typography>
                      </TableCell>
                      <TableCell colSpan={showApproval && editable ? 3 : showApproval || editable ? 2 : 1} />
                    </TableRow>
                    
                    <TableRow>
                      <TableCell colSpan={4} align="right">
                        <Typography variant="subtitle2">Tax (5%):</Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="subtitle2" fontWeight="bold">
                          {formatCurrency(totalTax)}
                        </Typography>
                      </TableCell>
                      <TableCell colSpan={showApproval && editable ? 3 : showApproval || editable ? 2 : 1} />
                    </TableRow>
                    
                    <TableRow>
                      <TableCell colSpan={4} align="right">
                        <Typography variant="h6">Total:</Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="h6" color="primary" fontWeight="bold">
                          {formatCurrency(totalCharges + totalTax)}
                        </Typography>
                      </TableCell>
                      <TableCell colSpan={showApproval && editable ? 3 : showApproval || editable ? 2 : 1} />
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </>
          )}
        </CardContent>
      </Card>

      {/* Add Charge Dialog */}
      <Dialog open={showAddDialog} onClose={() => setShowAddDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Additional Charge</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <FormControl fullWidth>
              <InputLabel>Charge Type</InputLabel>
              <Select
                value={newCharge.type}
                label="Charge Type"
                onChange={(e) => setNewCharge(prev => ({ ...prev, type: e.target.value as any }))}
              >
                <MenuItem value="DAMAGE">Vehicle Damage</MenuItem>
                <MenuItem value="FUEL">Fuel Top-up</MenuItem>
                <MenuItem value="MILEAGE">Excess Mileage</MenuItem>
                <MenuItem value="CLEANING">Cleaning Fee</MenuItem>
                <MenuItem value="LATE_RETURN">Late Return Fee</MenuItem>
                <MenuItem value="SALIK">Salik Charges</MenuItem>
                <MenuItem value="FINE">Traffic Fines</MenuItem>
                <MenuItem value="OTHER">Other</MenuItem>
              </Select>
            </FormControl>
            
            <TextField
              label="Description"
              value={newCharge.description}
              onChange={(e) => setNewCharge(prev => ({ ...prev, description: e.target.value }))}
              fullWidth
              required
            />
            
            <TextField
              label="Quantity"
              type="number"
              value={newCharge.quantity}
              onChange={(e) => setNewCharge(prev => ({ ...prev, quantity: Number(e.target.value) }))}
              fullWidth
              inputProps={{ min: 1 }}
            />
            
            <TextField
              label="Unit Price (AED)"
              type="number"
              value={newCharge.unitPrice}
              onChange={(e) => setNewCharge(prev => ({ ...prev, unitPrice: Number(e.target.value) }))}
              fullWidth
              inputProps={{ min: 0, step: 0.01 }}
              required
            />
            
            <FormControlLabel
              control={
                <Checkbox
                  checked={newCharge.taxable}
                  onChange={(e) => setNewCharge(prev => ({ ...prev, taxable: e.target.checked }))}
                />
              }
              label="Subject to VAT (5%)"
            />
            
            <TextField
              label="Notes"
              value={newCharge.notes}
              onChange={(e) => setNewCharge(prev => ({ ...prev, notes: e.target.value }))}
              fullWidth
              multiline
              rows={2}
            />
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAddDialog(false)}>Cancel</Button>
          <Button
            onClick={addCharge}
            variant="contained"
            disabled={!newCharge.description || newCharge.unitPrice <= 0}
          >
            Add Charge
          </Button>
        </DialogActions>
      </Dialog>

      {/* Waive Charge Dialog */}
      <Dialog open={showWaiveDialog} onClose={() => setShowWaiveDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Waive Charge</DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Please provide a reason for waiving this charge:
          </Typography>
          <TextField
            label="Waive Reason"
            value={waiveReason}
            onChange={(e) => setWaiveReason(e.target.value)}
            fullWidth
            multiline
            rows={3}
            required
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowWaiveDialog(false)}>Cancel</Button>
          <Button
            onClick={waiveCharge}
            variant="contained"
            color="warning"
            disabled={!waiveReason}
          >
            Waive Charge
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
