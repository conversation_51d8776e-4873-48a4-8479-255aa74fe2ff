import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Paper,
  useTheme,
  Chip,
  Stack,
  IconButton,
  Tooltip,
  Badge
} from '@mui/material';
import {
  LocalPolice,
  Dashboard,
  Upload,
  History,
  Assessment,
  Warning,
  Help,
  Settings,
  Speed,
  TrafficOutlined
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import FinesDashboard from './FinesDashboard';
import FinesImport from './FinesImport';
import FinesTransactions from './FinesTransactions';
import FinesHistory from './FinesHistory';

interface FinesWorkspaceProps {
  token: string;
  userRoles: string[];
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`fines-tabpanel-${index}`}
      aria-labelledby={`fines-tab-${index}`}
    >
      {value === index && (
        <AnimatePresence mode="wait">
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Box sx={{ py: 3 }}>
              {children}
            </Box>
          </motion.div>
        </AnimatePresence>
      )}
    </div>
  );
}

const FinesWorkspace: React.FC<FinesWorkspaceProps> = ({ token, userRoles = [] }) => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const isAdmin = userRoles?.includes('admin') || false;
  const isFinance = userRoles?.includes('finance') || isAdmin;
  const isOperations = userRoles?.includes('operations') || isAdmin;
  const isCustomer = userRoles?.includes('customer') || false;

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Mock data for alerts - in real implementation, this would come from API
  const alerts = {
    pendingFines: 23,
    errorFines: 5,
    unmappedFines: 8
  };

  const totalAlerts = alerts.errorFines + alerts.unmappedFines;

  const tabs = [
    {
      label: 'Dashboard',
      icon: <Dashboard />,
      component: <FinesDashboard token={token} userRoles={userRoles} alerts={alerts} />,
      adminOnly: false
    },
    {
      label: 'Import Fines',
      icon: <Upload />,
      component: <FinesImport token={token} userRoles={userRoles} />,
      adminOnly: true
    },
    {
      label: 'Manage Fines',
      icon: <LocalPolice />,
      component: <FinesTransactions token={token} userRoles={userRoles} />,
      adminOnly: true
    },
    {
      label: 'Fines History',
      icon: <History />,
      component: <FinesHistory token={token} userRoles={userRoles} />,
      adminOnly: false
    }
  ];

  const availableTabs = tabs.filter(tab => {
    if (isCustomer) {
      // Customers can only see Dashboard and History
      return tab.label === 'Dashboard' || tab.label === 'Fines History';
    }
    // Admin/Finance/Operations can see all tabs
    return !tab.adminOnly || (isAdmin || isFinance || isOperations);
  });

  // Check if user has access to Fines module
  if (!isAdmin && !isFinance && !isOperations && !isCustomer) {
    return (
      <Container maxWidth="md">
        <Box sx={{ py: 8, textAlign: 'center' }}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Paper sx={{ p: 6 }}>
              <Warning sx={{ fontSize: 64, color: 'warning.main', mb: 2 }} />
              <Typography variant="h4" gutterBottom>
                Access Restricted
              </Typography>
              <Typography variant="body1" color="text.secondary">
                This page is only accessible to authorized users.
              </Typography>
            </Paper>
          </motion.div>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 3 }}>
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Paper
            sx={{
              p: 3,
              mb: 3,
              background: `linear-gradient(135deg, ${theme.palette.error.main}15, ${theme.palette.error.main}05)`,
              border: `1px solid ${theme.palette.error.main}30`
            }}
          >
            <Stack direction="row" alignItems="center" justifyContent="space-between">
              <Box>
                <Typography variant="h4" sx={{ fontWeight: 600, color: 'error.main' }}>
                  Traffic Fines Management
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Import, validate, and manage traffic violations for vehicle agreements
                </Typography>
              </Box>
              <Stack direction="row" spacing={1} alignItems="center">
                <Chip
                  label={
                    isCustomer ? 'Customer View' :
                    isAdmin ? 'Admin Access' : 
                    isFinance ? 'Finance Access' : 'Operations Access'
                  }
                  color={
                    isCustomer ? 'info' :
                    isAdmin ? 'primary' : 
                    isFinance ? 'success' : 'warning'
                  }
                  variant="outlined"
                />
                {totalAlerts > 0 && !isCustomer && (
                  <Badge badgeContent={totalAlerts} color="error">
                    <Tooltip title={`${totalAlerts} fines requiring attention`}>
                      <IconButton size="small" color="error">
                        <Warning />
                      </IconButton>
                    </Tooltip>
                  </Badge>
                )}
                <Tooltip title="Help & Documentation">
                  <IconButton size="small">
                    <Help />
                  </IconButton>
                </Tooltip>
                {(isAdmin || isOperations) && (
                  <Tooltip title="Fines Settings">
                    <IconButton size="small">
                      <Settings />
                    </IconButton>
                  </Tooltip>
                )}
              </Stack>
            </Stack>
          </Paper>
        </motion.div>

        {/* Navigation Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Paper sx={{ mb: 3 }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                borderBottom: 1,
                borderColor: 'divider',
                '& .MuiTab-root': {
                  minHeight: 72,
                  textTransform: 'none',
                  fontSize: '0.95rem',
                  fontWeight: 500
                }
              }}
            >
              {availableTabs.map((tab, index) => (
                <Tab
                  key={index}
                  icon={tab.icon}
                  label={tab.label}
                  iconPosition="start"
                  sx={{
                    '& .MuiSvgIcon-root': {
                      mr: 1
                    }
                  }}
                />
              ))}
            </Tabs>
          </Paper>
        </motion.div>

        {/* Tab Content */}
        {availableTabs.map((tab, index) => (
          <TabPanel key={index} value={activeTab} index={index}>
            {tab.component}
          </TabPanel>
        ))}
      </Box>
    </Container>
  );
};

export default FinesWorkspace;
