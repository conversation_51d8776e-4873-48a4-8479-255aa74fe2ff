import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  Button,
  Stack,
  Paper,
  Alert,
  Snackbar,
  CircularProgress,
  Tabs,
  Tab,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Upload,
  CloudUpload,
  Code,
  CheckCircle,
  Error,
  Warning,
  Refresh,
  Download,
  Help,
  DataObject,
  TableChart,
  LocalPolice,
  Speed,
  TrafficOutlined
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface FinesImportProps {
  token: string;
  userRoles: string[];
}

interface ImportResult {
  total: number;
  imported: number;
  validated: number;
  errors: number;
  duplicates: number;
  details: any[];
}

const FinesImport: React.FC<FinesImportProps> = ({ token, userRoles = [] }) => {
  const [importMethod, setImportMethod] = useState(0); // 0: JSON, 1: CSV, 2: File Upload
  const [jsonData, setJsonData] = useState(`[
  {
    "plateNumber": "P-1001",
    "violationCode": "SPEED",
    "violationType": "Speeding",
    "amount": 250.0,
    "occurredAt": "2025-01-03T18:00:00Z",
    "location": "Sheikh Zayed Road"
  },
  {
    "plateNumber": "P-1002",
    "violationCode": "RED_LIGHT",
    "violationType": "Red Light Violation", 
    "amount": 500.0,
    "occurredAt": "2025-01-03T19:30:00Z",
    "location": "Dubai Mall Junction"
  }
]`);
  const [csvData, setCsvData] = useState(`plateNumber,violationCode,violationType,amount,occurredAt,location
P-1001,SPEED,Speeding,250.0,2025-01-03T18:00:00Z,Sheikh Zayed Road
P-1002,RED_LIGHT,Red Light Violation,500.0,2025-01-03T19:30:00Z,Dubai Mall Junction`);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      // In real implementation, you might want to preview the file content
    }
  };

  const validateJsonData = (data: string) => {
    try {
      const parsed = JSON.parse(data);
      if (!Array.isArray(parsed)) {
        throw new Error('Data must be an array of traffic fines');
      }
      
      const requiredFields = ['plateNumber', 'violationCode', 'amount', 'occurredAt'];
      for (let i = 0; i < parsed.length; i++) {
        const fine = parsed[i];
        for (const field of requiredFields) {
          if (!fine[field]) {
            throw new Error(`Missing required field '${field}' in fine ${i + 1}`);
          }
        }
        
        if (typeof fine.amount !== 'number' || fine.amount <= 0) {
          throw new Error(`Invalid amount in fine ${i + 1}: must be a positive number`);
        }
        
        if (isNaN(Date.parse(fine.occurredAt))) {
          throw new Error(`Invalid date format in fine ${i + 1}: ${fine.occurredAt}`);
        }
      }
      
      return { valid: true, data: parsed };
    } catch (error: any) {
      return { valid: false, error: error.message };
    }
  };

  const handleImport = async () => {
    setLoading(true);
    setImportResult(null);
    
    try {
      let dataToImport: any[] = [];
      
      if (importMethod === 0) {
        // JSON Import
        const validation = validateJsonData(jsonData);
        if (!validation.valid) {
          throw new Error(validation.error);
        }
        dataToImport = validation.data;
      } else if (importMethod === 1) {
        // CSV Import - simplified parsing
        const lines = csvData.trim().split('\n');
        const headers = lines[0].split(',');
        dataToImport = lines.slice(1).map(line => {
          const values = line.split(',');
          const obj: any = {};
          headers.forEach((header, index) => {
            obj[header.trim()] = values[index]?.trim();
          });
          obj.amount = parseFloat(obj.amount);
          return obj;
        });
      } else {
        // File Upload
        if (!selectedFile) {
          throw new Error('Please select a file to upload');
        }
        // In real implementation, you would read and parse the file
        throw new Error('File upload processing not implemented in demo');
      }

      // Simulate API call
      console.log('Importing fines data:', dataToImport);
      
      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock import result
      const mockResult: ImportResult = {
        total: dataToImport.length,
        imported: dataToImport.length - 1,
        validated: dataToImport.length - 2,
        errors: 1,
        duplicates: 0,
        details: [
          { status: 'success', message: 'Fine P-1001/SPEED imported and validated successfully' },
          { status: 'error', message: 'Fine P-1002/RED_LIGHT failed validation: Vehicle P-1002 not found in fleet' }
        ]
      };
      
      setImportResult(mockResult);
      setSnackbar({
        open: true,
        message: `Import completed: ${mockResult.imported}/${mockResult.total} fines processed`,
        severity: mockResult.errors > 0 ? 'warning' : 'success'
      });
      
    } catch (error: any) {
      setSnackbar({
        open: true,
        message: `Import failed: ${error.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const getResultIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle color="success" />;
      case 'error': return <Error color="error" />;
      case 'warning': return <Warning color="warning" />;
      default: return <CheckCircle />;
    }
  };

  const sampleData = {
    json: `[
  {
    "plateNumber": "P-1001",
    "violationCode": "SPEED",
    "violationType": "Speeding",
    "amount": 250.0,
    "occurredAt": "2025-01-03T18:00:00Z",
    "location": "Sheikh Zayed Road"
  }
]`,
    csv: `plateNumber,violationCode,violationType,amount,occurredAt,location
P-1001,SPEED,Speeding,250.0,2025-01-03T18:00:00Z,Sheikh Zayed Road`
  };

  const violationCodes = [
    { code: 'SPEED', type: 'Speeding', icon: <Speed />, color: 'error' },
    { code: 'RED_LIGHT', type: 'Red Light Violation', icon: <TrafficOutlined />, color: 'warning' },
    { code: 'PARKING', type: 'Illegal Parking', icon: <LocalPolice />, color: 'info' },
    { code: 'SIGNAL', type: 'Traffic Signal Violation', icon: <Warning />, color: 'primary' }
  ];

  return (
    <Box>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          Import Traffic Fines
        </Typography>

        {/* Import Method Selection */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Select Import Method
            </Typography>
            <Tabs value={importMethod} onChange={(_, newValue) => setImportMethod(newValue)}>
              <Tab icon={<Code />} label="JSON Data" iconPosition="start" />
              <Tab icon={<TableChart />} label="CSV Data" iconPosition="start" />
              <Tab icon={<CloudUpload />} label="File Upload" iconPosition="start" />
            </Tabs>
          </CardContent>
        </Card>

        <Grid container spacing={3}>
          {/* Import Input */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
                  <Typography variant="h6">
                    {importMethod === 0 ? 'JSON Data' : importMethod === 1 ? 'CSV Data' : 'File Upload'}
                  </Typography>
                  <Tooltip title="View sample format">
                    <IconButton size="small">
                      <Help />
                    </IconButton>
                  </Tooltip>
                </Stack>

                {importMethod === 0 && (
                  <TextField
                    label="Traffic Fines JSON"
                    value={jsonData}
                    onChange={(e) => setJsonData(e.target.value)}
                    multiline
                    rows={12}
                    fullWidth
                    variant="outlined"
                    sx={{ fontFamily: 'monospace' }}
                    helperText="Paste JSON array of traffic fines. Required fields: plateNumber, violationCode, amount, occurredAt"
                  />
                )}

                {importMethod === 1 && (
                  <TextField
                    label="Traffic Fines CSV"
                    value={csvData}
                    onChange={(e) => setCsvData(e.target.value)}
                    multiline
                    rows={12}
                    fullWidth
                    variant="outlined"
                    sx={{ fontFamily: 'monospace' }}
                    helperText="Paste CSV data with headers: plateNumber,violationCode,violationType,amount,occurredAt,location"
                  />
                )}

                {importMethod === 2 && (
                  <Box>
                    <input
                      accept=".json,.csv,.xlsx"
                      style={{ display: 'none' }}
                      id="file-upload"
                      type="file"
                      onChange={handleFileUpload}
                    />
                    <label htmlFor="file-upload">
                      <Paper
                        sx={{
                          p: 4,
                          textAlign: 'center',
                          border: '2px dashed',
                          borderColor: 'grey.300',
                          cursor: 'pointer',
                          '&:hover': {
                            borderColor: 'error.main',
                            bgcolor: 'error.light',
                            opacity: 0.1
                          }
                        }}
                      >
                        <CloudUpload sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
                        <Typography variant="h6" gutterBottom>
                          {selectedFile ? selectedFile.name : 'Click to upload file'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Supports JSON, CSV, and Excel files
                        </Typography>
                      </Paper>
                    </label>
                  </Box>
                )}

                <Stack direction="row" spacing={2} sx={{ mt: 3 }}>
                  <Button
                    variant="contained"
                    startIcon={loading ? <CircularProgress size={16} /> : <Upload />}
                    onClick={handleImport}
                    disabled={loading}
                    size="large"
                    color="error"
                  >
                    {loading ? 'Importing...' : 'Import Fines'}
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<Refresh />}
                    onClick={() => {
                      setJsonData(sampleData.json);
                      setCsvData(sampleData.csv);
                      setSelectedFile(null);
                      setImportResult(null);
                    }}
                  >
                    Reset
                  </Button>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          {/* Import Guidelines */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Import Guidelines
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemIcon>
                      <DataObject color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Required Fields"
                      secondary="plateNumber, violationCode, amount, occurredAt"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircle color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Validation Rules"
                      secondary="Amount > 0, valid date format, existing vehicle plates"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <Warning color="warning" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Duplicate Detection"
                      secondary="Same plate + violation code + timestamp will be rejected"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <Error color="error" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Error Handling"
                      secondary="Failed records can be retried after correction"
                    />
                  </ListItem>
                </List>

                <Divider sx={{ my: 2 }} />

                <Typography variant="subtitle2" gutterBottom>
                  Common Violation Codes:
                </Typography>
                <Stack spacing={1}>
                  {violationCodes.map((violation, index) => (
                    <Chip
                      key={index}
                      icon={violation.icon}
                      label={`${violation.code} - ${violation.type}`}
                      size="small"
                      color={violation.color}
                      variant="outlined"
                    />
                  ))}
                </Stack>

                <Divider sx={{ my: 2 }} />

                <Typography variant="subtitle2" gutterBottom>
                  Sample JSON Format:
                </Typography>
                <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                  <Typography variant="caption" component="pre" sx={{ fontFamily: 'monospace' }}>
                    {sampleData.json}
                  </Typography>
                </Paper>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Import Results */}
        {importResult && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card sx={{ mt: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Import Results
                </Typography>

                <Grid container spacing={2} sx={{ mb: 3 }}>
                  <Grid item xs={6} sm={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="primary.main">
                        {importResult.total}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Records
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="success.main">
                        {importResult.imported}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Imported
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="info.main">
                        {importResult.validated}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Validated
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="error.main">
                        {importResult.errors}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Errors
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>

                <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
                  Processing Details:
                </Typography>
                <List>
                  {importResult.details.map((detail, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        {getResultIcon(detail.status)}
                      </ListItemIcon>
                      <ListItemText
                        primary={detail.message}
                        secondary={
                          <Chip
                            label={detail.status}
                            size="small"
                            color={detail.status === 'success' ? 'success' : 'error'}
                            sx={{ mt: 0.5 }}
                          />
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          <Alert
            onClose={() => setSnackbar({ ...snackbar, open: false })}
            severity={snackbar.severity}
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </motion.div>
    </Box>
  );
};

export default FinesImport;
