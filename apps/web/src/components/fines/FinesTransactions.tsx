import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  Button,
  Stack,
  Chip,
  IconButton,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  Avatar,
  Menu,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Search,
  FilterList,
  CheckCircle,
  Error,
  Schedule,
  MoreVert,
  LocalPolice,
  DirectionsCar,
  Receipt,
  RestartAlt,
  Visibility,
  Edit,
  PostAdd,
  ExpandMore,
  Warning,
  Speed,
  TrafficOutlined,
  StopCircle
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface FinesTransactionsProps {
  token: string;
  userRoles: string[];
}

interface TrafficFine {
  id: string;
  plateNumber: string;
  violationCode: string;
  violationType: string;
  amount: number;
  occurredAt: string;
  location?: string;
  status: 'pending' | 'validated' | 'posted' | 'error' | 'unmapped';
  agreementId?: string;
  vehicleId?: string;
  errorMessage?: string;
  validatedAt?: string;
  postedAt?: string;
  validatedBy?: string;
  postedBy?: string;
  retryCount: number;
  maxRetries: number;
  createdAt: string;
  updatedAt: string;
}

const FinesTransactions: React.FC<FinesTransactionsProps> = ({ token, userRoles = [] }) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('pending');
  const [violationFilter, setViolationFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState({ from: '', to: '' });
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedFine, setSelectedFine] = useState<TrafficFine | null>(null);
  const [errorDialogOpen, setErrorDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });
  const [expandedFine, setExpandedFine] = useState<string | null>(null);

  // Mock data - in real implementation, this would come from API
  const [fines, setFines] = useState<TrafficFine[]>([
    {
      id: 'FINE-2024-001',
      plateNumber: 'P-1001',
      violationCode: 'SPEED',
      violationType: 'Speeding',
      amount: 250.0,
      occurredAt: '2024-01-28T18:00:00Z',
      location: 'Sheikh Zayed Road',
      status: 'pending',
      retryCount: 0,
      maxRetries: 3,
      createdAt: '2024-01-28T20:30:00Z',
      updatedAt: '2024-01-28T20:30:00Z'
    },
    {
      id: 'FINE-2024-002',
      plateNumber: 'P-1002',
      violationCode: 'RED_LIGHT',
      violationType: 'Red Light Violation',
      amount: 500.0,
      occurredAt: '2024-01-28T19:15:00Z',
      location: 'Dubai Mall Junction',
      status: 'validated',
      agreementId: 'AGR-2024-001',
      vehicleId: 'VEH-001',
      validatedAt: '2024-01-28T21:45:00Z',
      validatedBy: 'admin',
      retryCount: 0,
      maxRetries: 3,
      createdAt: '2024-01-28T20:30:00Z',
      updatedAt: '2024-01-28T21:45:00Z'
    },
    {
      id: 'FINE-2024-003',
      plateNumber: 'P-1003',
      violationCode: 'PARKING',
      violationType: 'Illegal Parking',
      amount: 150.0,
      occurredAt: '2024-01-28T17:30:00Z',
      location: 'Business Bay',
      status: 'posted',
      agreementId: 'AGR-2024-002',
      vehicleId: 'VEH-002',
      validatedAt: '2024-01-28T19:00:00Z',
      validatedBy: 'admin',
      postedAt: '2024-01-28T22:00:00Z',
      postedBy: 'finance_user',
      retryCount: 0,
      maxRetries: 3,
      createdAt: '2024-01-28T19:00:00Z',
      updatedAt: '2024-01-28T22:00:00Z'
    },
    {
      id: 'FINE-2024-004',
      plateNumber: 'P-9999',
      violationCode: 'SIGNAL',
      violationType: 'Traffic Signal Violation',
      amount: 300.0,
      occurredAt: '2024-01-28T16:45:00Z',
      location: 'Al Wasl Road',
      status: 'error',
      errorMessage: 'Vehicle plate P-9999 not found in fleet database',
      retryCount: 2,
      maxRetries: 3,
      createdAt: '2024-01-28T18:00:00Z',
      updatedAt: '2024-01-28T18:30:00Z'
    },
    {
      id: 'FINE-2024-005',
      plateNumber: 'P-1004',
      violationCode: 'SPEED',
      violationType: 'Speeding',
      amount: 400.0,
      occurredAt: '2024-01-28T15:20:00Z',
      location: 'Emirates Road',
      status: 'unmapped',
      errorMessage: 'No active agreement found for vehicle P-1004 at violation time',
      retryCount: 1,
      maxRetries: 3,
      createdAt: '2024-01-28T17:00:00Z',
      updatedAt: '2024-01-28T17:15:00Z'
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'posted': return 'success';
      case 'validated': return 'info';
      case 'error': case 'unmapped': return 'error';
      case 'pending': return 'warning';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'posted': return <CheckCircle />;
      case 'validated': return <Schedule />;
      case 'error': case 'unmapped': return <Error />;
      case 'pending': return <Schedule />;
      default: return <Schedule />;
    }
  };

  const getViolationIcon = (violationCode: string) => {
    switch (violationCode) {
      case 'SPEED': return <Speed />;
      case 'RED_LIGHT': case 'SIGNAL': return <TrafficOutlined />;
      case 'PARKING': return <LocalPolice />;
      default: return <Warning />;
    }
  };

  const getViolationColor = (violationCode: string) => {
    switch (violationCode) {
      case 'SPEED': return 'error';
      case 'RED_LIGHT': return 'warning';
      case 'PARKING': return 'info';
      case 'SIGNAL': return 'primary';
      default: return 'default';
    }
  };

  const formatDateTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('en-AE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: 'AED'
    }).format(amount);
  };

  const filteredFines = fines.filter(fine => {
    const matchesSearch = 
      fine.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fine.plateNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fine.violationCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fine.violationType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fine.agreementId?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fine.location?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || fine.status === statusFilter;
    const matchesViolation = violationFilter === 'all' || fine.violationCode === violationFilter;
    
    const matchesDateRange = 
      (!dateFilter.from || fine.occurredAt >= dateFilter.from) &&
      (!dateFilter.to || fine.occurredAt <= dateFilter.to);

    return matchesSearch && matchesStatus && matchesViolation && matchesDateRange;
  });

  const paginatedFines = filteredFines.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, fine: TrafficFine) => {
    setAnchorEl(event.currentTarget);
    setSelectedFine(fine);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedFine(null);
  };

  const handleValidateFines = async () => {
    setLoading(true);
    try {
      // API call to validate pending fines
      console.log('Validating pending fines...');
      
      // Simulate API response
      setTimeout(() => {
        setSnackbar({
          open: true,
          message: 'Validation process started for pending fines',
          severity: 'success'
        });
        setLoading(false);
        // Refresh data
        refreshData();
      }, 2000);
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to start validation process',
        severity: 'error'
      });
      setLoading(false);
    }
  };

  const handlePostFines = async () => {
    setLoading(true);
    try {
      // API call to post validated fines to billing
      console.log('Posting validated fines to billing...');
      
      // Simulate API response
      setTimeout(() => {
        setSnackbar({
          open: true,
          message: 'Posting process started for validated fines',
          severity: 'success'
        });
        setLoading(false);
        // Refresh data
        refreshData();
      }, 2000);
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to start posting process',
        severity: 'error'
      });
      setLoading(false);
    }
  };

  const handleRetryFine = async (fineId: string) => {
    try {
      // API call to retry specific fine
      console.log('Retrying fine:', fineId);
      
      setSnackbar({
        open: true,
        message: 'Fine retry initiated',
        severity: 'success'
      });
      handleMenuClose();
      refreshData();
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to retry fine',
        severity: 'error'
      });
    }
  };

  const handleViewError = () => {
    setErrorDialogOpen(true);
    handleMenuClose();
  };

  const refreshData = () => {
    // In real implementation, this would fetch fresh data from API
    console.log('Refreshing fines data...');
  };

  const pendingCount = fines.filter(f => f.status === 'pending').length;
  const validatedCount = fines.filter(f => f.status === 'validated').length;
  const errorCount = fines.filter(f => f.status === 'error' || f.status === 'unmapped').length;

  return (
    <Box>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
          <Typography variant="h5" sx={{ fontWeight: 600 }}>
            Manage Traffic Fines
          </Typography>
          <Stack direction="row" spacing={2}>
            <Button
              variant="outlined"
              startIcon={<CheckCircle />}
              onClick={handleValidateFines}
              disabled={loading || pendingCount === 0}
            >
              Validate ({pendingCount})
            </Button>
            <Button
              variant="contained"
              startIcon={loading ? <CircularProgress size={16} /> : <PostAdd />}
              onClick={handlePostFines}
              disabled={loading || validatedCount === 0}
              color="error"
            >
              {loading ? 'Posting...' : `Post to Billing (${validatedCount})`}
            </Button>
          </Stack>
        </Stack>

        {/* Quick Stats */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main" sx={{ fontWeight: 600 }}>
                {pendingCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Pending Validation
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="info.main" sx={{ fontWeight: 600 }}>
                {validatedCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Ready to Post
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="error.main" sx={{ fontWeight: 600 }}>
                {errorCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Errors/Unmapped
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="success.main" sx={{ fontWeight: 600 }}>
                {fines.filter(f => f.status === 'posted').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Posted to Billing
              </Typography>
            </Paper>
          </Grid>
        </Grid>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Search & Filters
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  placeholder="Search by ID, plate, violation, agreement, or location..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    label="Status"
                  >
                    <MenuItem value="all">All Statuses</MenuItem>
                    <MenuItem value="pending">Pending</MenuItem>
                    <MenuItem value="validated">Validated</MenuItem>
                    <MenuItem value="posted">Posted</MenuItem>
                    <MenuItem value="error">Error</MenuItem>
                    <MenuItem value="unmapped">Unmapped</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Violation</InputLabel>
                  <Select
                    value={violationFilter}
                    onChange={(e) => setViolationFilter(e.target.value)}
                    label="Violation"
                  >
                    <MenuItem value="all">All Types</MenuItem>
                    <MenuItem value="SPEED">Speeding</MenuItem>
                    <MenuItem value="RED_LIGHT">Red Light</MenuItem>
                    <MenuItem value="PARKING">Parking</MenuItem>
                    <MenuItem value="SIGNAL">Traffic Signal</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  fullWidth
                  label="From Date"
                  type="date"
                  value={dateFilter.from}
                  onChange={(e) => setDateFilter({ ...dateFilter, from: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  fullWidth
                  label="To Date"
                  type="date"
                  value={dateFilter.to}
                  onChange={(e) => setDateFilter({ ...dateFilter, to: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={1}>
                <Button
                  variant="outlined"
                  startIcon={<FilterList />}
                  fullWidth
                  sx={{ height: '100%' }}
                  onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('pending');
                    setViolationFilter('all');
                    setDateFilter({ from: '', to: '' });
                  }}
                >
                  Clear
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Results Summary */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="body1">
            Showing {paginatedFines.length} of {filteredFines.length} traffic fines
          </Typography>
        </Paper>

        {/* Fines Table */}
        <Card>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Fine ID</TableCell>
                  <TableCell>Plate Number</TableCell>
                  <TableCell>Violation</TableCell>
                  <TableCell align="right">Amount</TableCell>
                  <TableCell>Occurred At</TableCell>
                  <TableCell>Location</TableCell>
                  <TableCell align="center">Status</TableCell>
                  <TableCell>Agreement</TableCell>
                  <TableCell align="center">Retries</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedFines.map((fine) => (
                  <React.Fragment key={fine.id}>
                    <TableRow hover>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {fine.id}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <DirectionsCar fontSize="small" color="action" />
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {fine.plateNumber}
                          </Typography>
                        </Stack>
                      </TableCell>
                      <TableCell>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Avatar sx={{
                            bgcolor: `${getViolationColor(fine.violationCode)}.main`,
                            width: 32,
                            height: 32
                          }}>
                            {getViolationIcon(fine.violationCode)}
                          </Avatar>
                          <Stack>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {fine.violationType}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {fine.violationCode}
                            </Typography>
                          </Stack>
                        </Stack>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {formatCurrency(fine.amount)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDateTime(fine.occurredAt)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {fine.location || 'Not specified'}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          label={fine.status.replace('_', ' ')}
                          size="small"
                          color={getStatusColor(fine.status)}
                          icon={getStatusIcon(fine.status)}
                          sx={{ textTransform: 'capitalize' }}
                        />
                      </TableCell>
                      <TableCell>
                        {fine.agreementId ? (
                          <Stack>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {fine.agreementId}
                            </Typography>
                            {fine.vehicleId && (
                              <Typography variant="caption" color="text.secondary">
                                Vehicle: {fine.vehicleId}
                              </Typography>
                            )}
                          </Stack>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            Not mapped
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell align="center">
                        <Stack alignItems="center">
                          <Typography variant="body2">
                            {fine.retryCount}/{fine.maxRetries}
                          </Typography>
                          {fine.retryCount > 0 && (
                            <Typography variant="caption" color="warning.main">
                              Retried
                            </Typography>
                          )}
                        </Stack>
                      </TableCell>
                      <TableCell align="center">
                        <Stack direction="row" spacing={1}>
                          {fine.errorMessage && (
                            <Tooltip title="View error details">
                              <IconButton
                                size="small"
                                onClick={() => setExpandedFine(
                                  expandedFine === fine.id ? null : fine.id
                                )}
                              >
                                <ExpandMore
                                  sx={{
                                    transform: expandedFine === fine.id ? 'rotate(180deg)' : 'rotate(0deg)',
                                    transition: 'transform 0.3s'
                                  }}
                                />
                              </IconButton>
                            </Tooltip>
                          )}
                          <Tooltip title="More actions">
                            <IconButton
                              size="small"
                              onClick={(e) => handleMenuOpen(e, fine)}
                            >
                              <MoreVert />
                            </IconButton>
                          </Tooltip>
                        </Stack>
                      </TableCell>
                    </TableRow>

                    {/* Expandable Error Details Row */}
                    {expandedFine === fine.id && fine.errorMessage && (
                      <TableRow>
                        <TableCell colSpan={10} sx={{ py: 0 }}>
                          <Accordion expanded={true} sx={{ boxShadow: 'none' }}>
                            <AccordionDetails sx={{ pt: 2 }}>
                              <Alert severity="error" sx={{ mb: 2 }}>
                                <Typography variant="body2">
                                  <strong>Error Details:</strong> {fine.errorMessage}
                                </Typography>
                              </Alert>
                              <Grid container spacing={2}>
                                <Grid item xs={12} md={6}>
                                  <Typography variant="subtitle2" gutterBottom>
                                    Fine Details
                                  </Typography>
                                  <Stack spacing={1}>
                                    <Typography variant="body2">
                                      <strong>Created:</strong> {formatDateTime(fine.createdAt)}
                                    </Typography>
                                    <Typography variant="body2">
                                      <strong>Last Updated:</strong> {formatDateTime(fine.updatedAt)}
                                    </Typography>
                                    <Typography variant="body2">
                                      <strong>Retry Count:</strong> {fine.retryCount}/{fine.maxRetries}
                                    </Typography>
                                  </Stack>
                                </Grid>
                                <Grid item xs={12} md={6}>
                                  <Typography variant="subtitle2" gutterBottom>
                                    Possible Solutions
                                  </Typography>
                                  <Stack spacing={1}>
                                    {fine.status === 'unmapped' && (
                                      <Typography variant="body2" color="text.secondary">
                                        • Check if vehicle has an active agreement at violation time
                                      </Typography>
                                    )}
                                    {fine.status === 'error' && (
                                      <Typography variant="body2" color="text.secondary">
                                        • Verify vehicle plate exists in fleet database
                                      </Typography>
                                    )}
                                    <Typography variant="body2" color="text.secondary">
                                      • Contact support if issue persists
                                    </Typography>
                                  </Stack>
                                </Grid>
                              </Grid>
                            </AccordionDetails>
                          </Accordion>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50]}
            component="div"
            count={filteredFines.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            onRowsPerPageChange={(e) => {
              setRowsPerPage(parseInt(e.target.value, 10));
              setPage(0);
            }}
          />
        </Card>

        {/* Actions Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          {selectedFine?.status === 'validated' && (
            <MenuItem onClick={() => console.log('Post to billing')}>
              <ListItemIcon>
                <PostAdd fontSize="small" />
              </ListItemIcon>
              <ListItemText>Post to Billing</ListItemText>
            </MenuItem>
          )}
          {(selectedFine?.status === 'error' || selectedFine?.status === 'unmapped') &&
           selectedFine.retryCount < selectedFine.maxRetries && (
            <MenuItem onClick={() => selectedFine && handleRetryFine(selectedFine.id)}>
              <ListItemIcon>
                <RestartAlt fontSize="small" />
              </ListItemIcon>
              <ListItemText>Retry Fine</ListItemText>
            </MenuItem>
          )}
          {selectedFine?.errorMessage && (
            <MenuItem onClick={handleViewError}>
              <ListItemIcon>
                <Visibility fontSize="small" />
              </ListItemIcon>
              <ListItemText>View Error Details</ListItemText>
            </MenuItem>
          )}
          <MenuItem onClick={() => console.log('Edit fine')}>
            <ListItemIcon>
              <Edit fontSize="small" />
            </ListItemIcon>
            <ListItemText>Edit Fine</ListItemText>
          </MenuItem>
        </Menu>

        {/* Error Dialog */}
        <Dialog open={errorDialogOpen} onClose={() => setErrorDialogOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>Fine Error Details</DialogTitle>
          <DialogContent>
            {selectedFine && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Fine: {selectedFine.id}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Plate: {selectedFine.plateNumber} | Violation: {selectedFine.violationType} | Amount: {formatCurrency(selectedFine.amount)}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Occurred: {formatDateTime(selectedFine.occurredAt)} | Location: {selectedFine.location}
                </Typography>

                <Alert severity="error" sx={{ mt: 2 }}>
                  <Typography variant="body2">
                    {selectedFine.errorMessage}
                  </Typography>
                </Alert>

                <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                  Retry attempts: {selectedFine.retryCount}/{selectedFine.maxRetries}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Last updated: {formatDateTime(selectedFine.updatedAt)}
                </Typography>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setErrorDialogOpen(false)}>Close</Button>
            {selectedFine?.retryCount < selectedFine?.maxRetries && (
              <Button
                variant="contained"
                color="error"
                onClick={() => {
                  if (selectedFine) {
                    handleRetryFine(selectedFine.id);
                    setErrorDialogOpen(false);
                  }
                }}
              >
                Retry Fine
              </Button>
            )}
          </DialogActions>
        </Dialog>

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          <Alert
            onClose={() => setSnackbar({ ...snackbar, open: false })}
            severity={snackbar.severity}
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </motion.div>
    </Box>
  );
};

export default FinesTransactions;
