import React from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ack,
  Avatar,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
  useTheme,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Button,
  Paper
} from '@mui/material';
import {
  LocalPolice,
  CheckCircle,
  Error,
  Schedule,
  Refresh,
  TrendingUp,
  TrendingDown,
  Upload,
  Download,
  Warning,
  Assessment,
  DirectionsCar,
  Receipt,
  AccountBalance,
  Speed,
  TrafficOutlined,
  StopCircle
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface FinesDashboardProps {
  token: string;
  userRoles: string[];
  alerts: {
    pendingFines: number;
    errorFines: number;
    unmappedFines: number;
  };
}

const FinesDashboard: React.FC<FinesDashboardProps> = ({ token, userRoles = [], alerts }) => {
  const theme = useTheme();
  const isAdmin = userRoles?.includes('admin') || false;
  const isCustomer = userRoles?.includes('customer') || false;

  // Mock data - in real implementation, this would come from API
  const dashboardMetrics = {
    totalFines: 1847,
    validatedFines: 1654,
    postedFines: 1598,
    errorFines: 56,
    totalAmount: 125678.50,
    validationRate: 89.5,
    avgProcessingTime: 2.3,
    lastImportTime: '2024-01-28T16:30:00Z',
    nextScheduledImport: '2024-01-29T08:00:00Z'
  };

  const violationTypes = [
    { type: 'Speeding', count: 856, amount: 68945.50, percentage: 46.4, icon: <Speed /> },
    { type: 'Red Light', count: 342, amount: 34256.75, percentage: 18.5, icon: <StopCircle /> },
    { type: 'Parking', count: 289, amount: 14476.25, percentage: 15.7, icon: <LocalPolice /> },
    { type: 'Traffic Signal', count: 234, amount: 11700.00, percentage: 12.7, icon: <TrafficOutlined /> },
    { type: 'Other', count: 126, amount: 6300.00, percentage: 6.8, icon: <Warning /> }
  ];

  const recentActivity = [
    { type: 'import', action: 'Bulk import completed', count: 89, status: 'success', time: '1 hour ago', user: 'admin' },
    { type: 'validation', action: 'Validation failed', count: 5, status: 'error', time: '2 hours ago', user: 'system', error: 'Unmapped vehicle plates' },
    { type: 'posting', action: 'Posted to billing', count: 67, status: 'success', time: '3 hours ago', user: 'finance_user' },
    { type: 'retry', action: 'Retry processing', count: 8, status: 'warning', time: '4 hours ago', user: 'operations' }
  ];

  const systemStatus = [
    { system: 'Traffic Police API', status: 'connected', lastSync: '2024-01-28T16:30:00Z' },
    { system: 'Vehicle Mapping', status: 'connected', lastSync: '2024-01-28T16:29:00Z' },
    { system: 'Billing Integration', status: 'connected', lastSync: '2024-01-28T16:25:00Z' },
    { system: 'Audit Logger', status: 'connected', lastSync: '2024-01-28T16:30:00Z' }
  ];

  const formatDateTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('en-AE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: 'AED'
    }).format(amount);
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'import': return <Upload />;
      case 'validation': return <CheckCircle />;
      case 'posting': return <Receipt />;
      case 'retry': return <Refresh />;
      default: return <LocalPolice />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': case 'connected': return 'success';
      case 'error': case 'failed': return 'error';
      case 'warning': case 'pending': return 'warning';
      case 'processing': return 'info';
      default: return 'default';
    }
  };

  const StatCard = ({ title, value, subtitle, icon, color, trend }: any) => (
    <Card>
      <CardContent>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Avatar sx={{ bgcolor: `${color}.main`, width: 56, height: 56 }}>
            {icon}
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h4" sx={{ fontWeight: 600 }}>
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="caption" color="text.secondary">
                {subtitle}
              </Typography>
            )}
            {trend && (
              <Stack direction="row" alignItems="center" spacing={1} sx={{ mt: 1 }}>
                {trend > 0 ? <TrendingUp color="success" fontSize="small" /> : <TrendingDown color="error" fontSize="small" />}
                <Typography 
                  variant="caption" 
                  color={trend > 0 ? 'success.main' : 'error.main'}
                  sx={{ fontWeight: 500 }}
                >
                  {Math.abs(trend)}% from last week
                </Typography>
              </Stack>
            )}
          </Box>
        </Stack>
      </CardContent>
    </Card>
  );

  return (
    <Box>
      {/* Alerts Section */}
      {(alerts.errorFines > 0 || alerts.unmappedFines > 0) && !isCustomer && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Alert 
            severity="warning" 
            icon={<Warning />}
            sx={{ mb: 3 }}
            action={
              <Button size="small" color="inherit" startIcon={<Assessment />}>
                Review Fines
              </Button>
            }
          >
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              Traffic Fines Alerts: {alerts.errorFines} failed validations, {alerts.unmappedFines} unmapped vehicles, 
              {alerts.pendingFines} pending processing
            </Typography>
          </Alert>
        </motion.div>
      )}

      {/* Key Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          {isCustomer ? 'Your Traffic Fines Overview' : 'Traffic Fines Performance'}
        </Typography>
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title={isCustomer ? "Your Total Fines" : "Total Fines"}
              value={isCustomer ? formatCurrency(dashboardMetrics.totalAmount) : dashboardMetrics.totalFines}
              subtitle={isCustomer ? "All time" : "All time"}
              icon={<LocalPolice />}
              color="error"
              trend={-8.5}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title={isCustomer ? "This Month" : "Validation Rate"}
              value={isCustomer ? formatCurrency(8678.25) : `${dashboardMetrics.validationRate}%`}
              subtitle={isCustomer ? "Current month fines" : "Last 30 days"}
              icon={<CheckCircle />}
              color="success"
              trend={3.2}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title={isCustomer ? "Pending Fines" : "Error Fines"}
              value={isCustomer ? formatCurrency(450.00) : dashboardMetrics.errorFines}
              subtitle={isCustomer ? "Awaiting processing" : "Requiring attention"}
              icon={<Error />}
              color="warning"
              trend={-12.3}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title={isCustomer ? "Avg per Fine" : "Avg Processing"}
              value={isCustomer ? formatCurrency(68.50) : `${dashboardMetrics.avgProcessingTime}min`}
              subtitle={isCustomer ? "Per violation" : "Per fine batch"}
              icon={<Schedule />}
              color="info"
              trend={-5.7}
            />
          </Grid>
        </Grid>
      </motion.div>

      <Grid container spacing={3}>
        {/* Violation Types Breakdown */}
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {isCustomer ? 'Your Fines by Type' : 'Violation Types Distribution'}
                  </Typography>
                  <Tooltip title="Refresh data">
                    <IconButton size="small">
                      <Refresh />
                    </IconButton>
                  </Tooltip>
                </Stack>
                <Stack spacing={3}>
                  {violationTypes.map((type, index) => (
                    <Box key={index}>
                      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Avatar sx={{ 
                            bgcolor: index === 0 ? 'error.main' : 
                                   index === 1 ? 'warning.main' : 
                                   index === 2 ? 'info.main' : 
                                   index === 3 ? 'primary.main' : 'grey.500',
                            width: 32,
                            height: 32
                          }}>
                            {type.icon}
                          </Avatar>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {type.type}
                          </Typography>
                          <Chip 
                            label={`${type.count} fines`} 
                            size="small" 
                            variant="outlined"
                          />
                        </Stack>
                        <Typography variant="body2" color="text.secondary">
                          {formatCurrency(type.amount)}
                        </Typography>
                      </Stack>
                      <LinearProgress 
                        variant="determinate" 
                        value={type.percentage} 
                        sx={{ 
                          height: 8, 
                          borderRadius: 4,
                          bgcolor: 'grey.200',
                          '& .MuiLinearProgress-bar': {
                            borderRadius: 4,
                            bgcolor: index === 0 ? 'error.main' : 
                                   index === 1 ? 'warning.main' : 
                                   index === 2 ? 'info.main' : 
                                   index === 3 ? 'primary.main' : 'grey.500'
                          }
                        }}
                      />
                      <Stack direction="row" justifyContent="space-between" sx={{ mt: 0.5 }}>
                        <Typography variant="caption" color="text.secondary">
                          {type.percentage}% of total
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {type.count} violations
                        </Typography>
                      </Stack>
                    </Box>
                  ))}
                </Stack>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                  {isCustomer ? 'Recent Fine Activity' : 'Recent Processing Activity'}
                </Typography>
                <List dense>
                  {recentActivity.map((activity, index) => (
                    <React.Fragment key={index}>
                      <ListItem>
                        <ListItemIcon>
                          <Avatar sx={{ 
                            bgcolor: `${getStatusColor(activity.status)}.main`,
                            width: 32,
                            height: 32
                          }}>
                            {getActivityIcon(activity.type)}
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {activity.action} ({activity.count} records)
                            </Typography>
                          }
                          secondary={
                            <Stack spacing={0.5}>
                              <Typography variant="caption" color="text.secondary">
                                {activity.time} • by {activity.user}
                              </Typography>
                              {activity.error && (
                                <Typography variant="caption" color="error.main">
                                  Error: {activity.error}
                                </Typography>
                              )}
                            </Stack>
                          }
                        />
                        <Chip 
                          label={activity.status} 
                          size="small" 
                          color={getStatusColor(activity.status)}
                        />
                      </ListItem>
                      {index < recentActivity.length - 1 && <Divider variant="inset" component="li" />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* System Status - Admin only */}
        {!isCustomer && (
          <Grid item xs={12}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <Card>
                <CardContent>
                  <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      System Status & Connectivity
                    </Typography>
                    <Stack direction="row" spacing={1}>
                      <Typography variant="body2" color="text.secondary">
                        Last import: {formatDateTime(dashboardMetrics.lastImportTime)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Next scheduled: {formatDateTime(dashboardMetrics.nextScheduledImport)}
                      </Typography>
                    </Stack>
                  </Stack>
                  <Grid container spacing={2}>
                    {systemStatus.map((system, index) => (
                      <Grid item xs={12} sm={6} md={3} key={index}>
                        <Box sx={{ 
                          p: 2, 
                          border: 1, 
                          borderColor: `${getStatusColor(system.status)}.main`,
                          borderRadius: 1,
                          bgcolor: `${getStatusColor(system.status)}.light`,
                          opacity: 0.1
                        }}>
                          <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1 }}>
                            <CheckCircle 
                              color={getStatusColor(system.status)} 
                              fontSize="small" 
                            />
                            <Typography variant="body2" sx={{ fontWeight: 600 }}>
                              {system.system}
                            </Typography>
                          </Stack>
                          <Typography variant="caption" color="text.secondary">
                            Last sync: {formatDateTime(system.lastSync)}
                          </Typography>
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default FinesDashboard;
