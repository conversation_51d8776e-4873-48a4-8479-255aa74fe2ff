import { Navigate, useLocation } from 'react-router-dom';
import { ReactNode } from 'react';

interface ProtectedRouteProps {
  children: ReactNode;
  isAuthenticated: boolean;
  requiredRoles?: string[];
  userRoles?: string[];
}

export default function ProtectedRoute({ 
  children, 
  isAuthenticated, 
  requiredRoles = [], 
  userRoles = [] 
}: ProtectedRouteProps) {
  const location = useLocation();

  // Check if user is authenticated
  if (!isAuthenticated) {
    // Redirect to login page with return url
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check if user has required roles
  if (requiredRoles.length > 0) {
    const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));
    if (!hasRequiredRole) {
      // Redirect to unauthorized page or home
      return <Navigate to="/" replace />;
    }
  }

  return <>{children}</>;
}
