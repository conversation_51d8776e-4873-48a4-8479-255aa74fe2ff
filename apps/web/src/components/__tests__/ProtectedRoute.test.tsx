import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import ProtectedRoute from '../ProtectedRoute';

// Mock react-router-dom Navigate component
const mockNavigate = jest.fn();

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  Navigate: ({ to, state, replace }: any) => {
    mockNavigate(to, { state, replace });
    return <div data-testid="navigate">Redirecting to {to}</div>;
  },
  useLocation: () => ({ pathname: '/protected-page' }),
}));

const TestComponent = () => <div data-testid="protected-content">Protected Content</div>;

const renderProtectedRoute = (props: any) => {
  return render(
    <BrowserRouter>
      <ProtectedRoute {...props}>
        <TestComponent />
      </ProtectedRoute>
    </BrowserRouter>
  );
};

describe('ProtectedRoute Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Authentication Check', () => {
    it('renders children when user is authenticated', () => {
      renderProtectedRoute({
        isAuthenticated: true,
        userRoles: ['user'],
      });

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
      expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
    });

    it('redirects to login when user is not authenticated', () => {
      renderProtectedRoute({
        isAuthenticated: false,
        userRoles: [],
      });

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
      expect(screen.getByTestId('navigate')).toBeInTheDocument();
      expect(mockNavigate).toHaveBeenCalledWith('/login', {
        state: { from: { pathname: '/protected-page' } },
        replace: true,
      });
    });
  });

  describe('Role-based Access Control', () => {
    it('renders children when user has required role', () => {
      renderProtectedRoute({
        isAuthenticated: true,
        requiredRoles: ['admin'],
        userRoles: ['admin', 'user'],
      });

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
      expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
    });

    it('renders children when user has one of multiple required roles', () => {
      renderProtectedRoute({
        isAuthenticated: true,
        requiredRoles: ['admin', 'moderator'],
        userRoles: ['moderator'],
      });

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
      expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
    });

    it('redirects to home when user lacks required role', () => {
      renderProtectedRoute({
        isAuthenticated: true,
        requiredRoles: ['admin'],
        userRoles: ['user'],
      });

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
      expect(screen.getByTestId('navigate')).toBeInTheDocument();
      expect(mockNavigate).toHaveBeenCalledWith('/', {
        state: undefined,
        replace: true,
      });
    });

    it('renders children when no roles are required', () => {
      renderProtectedRoute({
        isAuthenticated: true,
        requiredRoles: [],
        userRoles: ['user'],
      });

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
      expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
    });

    it('renders children when requiredRoles is undefined', () => {
      renderProtectedRoute({
        isAuthenticated: true,
        userRoles: ['user'],
      });

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
      expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles empty userRoles array', () => {
      renderProtectedRoute({
        isAuthenticated: true,
        requiredRoles: ['admin'],
        userRoles: [],
      });

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
      expect(screen.getByTestId('navigate')).toBeInTheDocument();
      expect(mockNavigate).toHaveBeenCalledWith('/', {
        state: undefined,
        replace: true,
      });
    });

    it('handles undefined userRoles', () => {
      renderProtectedRoute({
        isAuthenticated: true,
        requiredRoles: ['admin'],
      });

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
      expect(screen.getByTestId('navigate')).toBeInTheDocument();
      expect(mockNavigate).toHaveBeenCalledWith('/', {
        state: undefined,
        replace: true,
      });
    });

    it('prioritizes authentication check over role check', () => {
      renderProtectedRoute({
        isAuthenticated: false,
        requiredRoles: ['admin'],
        userRoles: ['admin'],
      });

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
      expect(screen.getByTestId('navigate')).toBeInTheDocument();
      expect(mockNavigate).toHaveBeenCalledWith('/login', {
        state: { from: { pathname: '/protected-page' } },
        replace: true,
      });
    });
  });

  describe('Complex Role Scenarios', () => {
    it('handles case-sensitive role matching', () => {
      renderProtectedRoute({
        isAuthenticated: true,
        requiredRoles: ['Admin'],
        userRoles: ['admin'],
      });

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
      expect(screen.getByTestId('navigate')).toBeInTheDocument();
    });

    it('handles multiple required roles with partial match', () => {
      renderProtectedRoute({
        isAuthenticated: true,
        requiredRoles: ['admin', 'superuser'],
        userRoles: ['admin', 'user'],
      });

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
      expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
    });

    it('handles duplicate roles in userRoles', () => {
      renderProtectedRoute({
        isAuthenticated: true,
        requiredRoles: ['admin'],
        userRoles: ['admin', 'admin', 'user'],
      });

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
      expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
    });
  });
});
