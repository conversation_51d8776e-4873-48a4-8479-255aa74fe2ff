import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import UserDashboard from '../UserDashboard';

const theme = createTheme();

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        {component}
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('UserDashboard', () => {
  const mockProps = {
    token: 'mock-user-token',
    roles: ['user']
  };

  it('renders user dashboard header', () => {
    renderWithProviders(<UserDashboard {...mockProps} />);
    
    expect(screen.getByText('User Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Welcome back! Manage your agreements and track your progress.')).toBeInTheDocument();
  });

  it('displays user role chip', () => {
    renderWithProviders(<UserDashboard {...mockProps} />);
    
    expect(screen.getByText('user')).toBeInTheDocument();
  });

  it('shows user performance stats', () => {
    renderWithProviders(<UserDashboard {...mockProps} />);
    
    expect(screen.getByText('Your Performance')).toBeInTheDocument();
    expect(screen.getByText('Active Agreements')).toBeInTheDocument();
    expect(screen.getByText('Completed This Month')).toBeInTheDocument();
    expect(screen.getByText('Success Rate')).toBeInTheDocument();
    expect(screen.getByText('Performance Score')).toBeInTheDocument();
  });

  it('displays user quick actions', () => {
    renderWithProviders(<UserDashboard {...mockProps} />);
    
    expect(screen.getByText('Quick Actions')).toBeInTheDocument();
    expect(screen.getByText('Create STR Agreement')).toBeInTheDocument();
    expect(screen.getByText('Process Closure')).toBeInTheDocument();
    expect(screen.getByText('View History')).toBeInTheDocument();
    expect(screen.getByText('Help & Support')).toBeInTheDocument();
  });

  it('shows recent activity section', () => {
    renderWithProviders(<UserDashboard {...mockProps} />);
    
    expect(screen.getByText('Recent Activity')).toBeInTheDocument();
    expect(screen.getByText('STR Agreement Created')).toBeInTheDocument();
    expect(screen.getByText('Closure Processed')).toBeInTheDocument();
    expect(screen.getByText('Payment Received')).toBeInTheDocument();
    expect(screen.getByText('Agreement Pending')).toBeInTheDocument();
  });

  it('highlights featured actions', () => {
    renderWithProviders(<UserDashboard {...mockProps} />);
    
    const featuredChips = screen.getAllByText('Featured');
    expect(featuredChips.length).toBe(2); // STR Agreement and Process Closure
  });

  it('provides navigation links for all actions', () => {
    renderWithProviders(<UserDashboard {...mockProps} />);
    
    const openButtons = screen.getAllByText('Open');
    expect(openButtons.length).toBe(4); // Should have 4 user actions
  });

  it('shows activity status indicators', () => {
    renderWithProviders(<UserDashboard {...mockProps} />);
    
    // Check for time indicators
    expect(screen.getByText('2 hours ago')).toBeInTheDocument();
    expect(screen.getByText('1 day ago')).toBeInTheDocument();
    expect(screen.getByText('2 days ago')).toBeInTheDocument();
    expect(screen.getByText('3 days ago')).toBeInTheDocument();
  });

  it('works with multiple roles', () => {
    const multiRoleProps = {
      token: 'mock-user-token',
      roles: ['user', 'guest']
    };
    
    renderWithProviders(<UserDashboard {...multiRoleProps} />);
    
    expect(screen.getByText('user')).toBeInTheDocument();
    expect(screen.getByText('guest')).toBeInTheDocument();
  });
});
