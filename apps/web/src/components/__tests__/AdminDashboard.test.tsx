import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import AdminDashboard from '../AdminDashboard';

const theme = createTheme();

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        {component}
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('AdminDashboard', () => {
  const mockProps = {
    token: 'mock-admin-token',
    roles: ['admin', 'user']
  };

  it('renders admin dashboard header', () => {
    renderWithProviders(<AdminDashboard {...mockProps} />);
    
    expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Welcome back, Administrator! You have full system access.')).toBeInTheDocument();
  });

  it('displays admin role chips', () => {
    renderWithProviders(<AdminDashboard {...mockProps} />);
    
    expect(screen.getByText('admin')).toBeInTheDocument();
    expect(screen.getByText('user')).toBeInTheDocument();
  });

  it('shows system overview stats', () => {
    renderWithProviders(<AdminDashboard {...mockProps} />);
    
    expect(screen.getByText('System Overview')).toBeInTheDocument();
    expect(screen.getByText('Total Users')).toBeInTheDocument();
    expect(screen.getByText('Active Vehicles')).toBeInTheDocument();
    expect(screen.getByText('Monthly Revenue')).toBeInTheDocument();
    expect(screen.getByText('System Health')).toBeInTheDocument();
  });

  it('displays administrative actions', () => {
    renderWithProviders(<AdminDashboard {...mockProps} />);
    
    expect(screen.getByText('Administrative Actions')).toBeInTheDocument();
    expect(screen.getByText('Fleet Management')).toBeInTheDocument();
    expect(screen.getByText('Billing & Payments')).toBeInTheDocument();
    expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument();
    expect(screen.getByText('User Management')).toBeInTheDocument();
    expect(screen.getByText('System Settings')).toBeInTheDocument();
    expect(screen.getByText('Integrations')).toBeInTheDocument();
  });

  it('highlights priority actions', () => {
    renderWithProviders(<AdminDashboard {...mockProps} />);
    
    const priorityChips = screen.getAllByText('Priority');
    expect(priorityChips.length).toBeGreaterThan(0);
  });

  it('provides navigation links for all actions', () => {
    renderWithProviders(<AdminDashboard {...mockProps} />);
    
    const openButtons = screen.getAllByText('Open');
    expect(openButtons.length).toBe(6); // Should have 6 admin actions
  });

  it('works with admin-only roles', () => {
    const adminOnlyProps = {
      token: 'mock-admin-token',
      roles: ['admin']
    };
    
    renderWithProviders(<AdminDashboard {...adminOnlyProps} />);
    
    expect(screen.getByText('admin')).toBeInTheDocument();
    expect(screen.queryByText('user')).not.toBeInTheDocument();
  });
});
