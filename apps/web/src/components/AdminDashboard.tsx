import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Avatar,
  Stack,
  Chip,
  Paper,
  useTheme
} from '@mui/material';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  Dashboard,
  People,
  Analytics,
  AccountBalance,
  Settings,
  Security,
  CloudSync,
  Assessment,
  DirectionsCar,
  Assignment
} from '@mui/icons-material';

interface AdminDashboardProps {
  token: string;
  roles: string[];
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({ token, roles }) => {
  const theme = useTheme();

  const adminActions = [
    {
      title: 'Fleet Management',
      description: 'Manage vehicles, maintenance, and availability',
      link: '/admin/lease',
      icon: <DirectionsCar />,
      color: 'primary',
      priority: 'high'
    },
    {
      title: 'Billing & Payments',
      description: 'Process payments and manage invoices',
      link: '/billing',
      icon: <AccountBalance />,
      color: 'success',
      priority: 'high'
    },
    {
      title: 'Analytics Dashboard',
      description: 'View comprehensive business analytics',
      link: '/admin/analytics',
      icon: <Analytics />,
      color: 'info',
      priority: 'medium'
    },
    {
      title: 'User Management',
      description: 'Manage user accounts and permissions',
      link: '/admin/users',
      icon: <People />,
      color: 'secondary',
      priority: 'medium'
    },
    {
      title: 'System Settings',
      description: 'Configure system-wide settings',
      link: '/admin/settings',
      icon: <Settings />,
      color: 'warning',
      priority: 'low'
    },
    {
      title: 'Integrations',
      description: 'Manage third-party integrations',
      link: '/admin/integrations',
      icon: <CloudSync />,
      color: 'info',
      priority: 'low'
    }
  ];

  const adminStats = [
    { label: 'Total Users', value: '1,234', change: '+12%', icon: <People /> },
    { label: 'Active Vehicles', value: '567', change: '+5%', icon: <DirectionsCar /> },
    { label: 'Monthly Revenue', value: '$89.2K', change: '+18%', icon: <AccountBalance /> },
    { label: 'System Health', value: '99.9%', change: '+0.1%', icon: <Security /> }
  ];

  return (
    <Box>
      {/* Admin Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Paper
          sx={{
            p: 3,
            mb: 4,
            background: `linear-gradient(135deg, ${theme.palette.primary.main}15, ${theme.palette.primary.main}05)`,
            border: `1px solid ${theme.palette.primary.main}30`
          }}
        >
          <Stack direction="row" alignItems="center" spacing={2}>
            <Avatar sx={{ bgcolor: 'primary.main', width: 56, height: 56 }}>
              <Dashboard />
            </Avatar>
            <Box>
              <Typography variant="h4" color="primary.main" sx={{ fontWeight: 600 }}>
                Admin Dashboard
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Welcome back, Administrator! You have full system access.
              </Typography>
            </Box>
            <Box sx={{ ml: 'auto' }}>
              <Stack direction="row" spacing={1}>
                {roles.map((role) => (
                  <Chip
                    key={role}
                    label={role}
                    color={role === 'admin' ? 'primary' : 'default'}
                    variant="outlined"
                  />
                ))}
              </Stack>
            </Box>
          </Stack>
        </Paper>
      </motion.div>

      {/* Admin Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          System Overview
        </Typography>
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {adminStats.map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card>
                <CardContent>
                  <Stack direction="row" alignItems="center" spacing={2}>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      {stat.icon}
                    </Avatar>
                    <Box>
                      <Typography variant="h6">{stat.value}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {stat.label}
                      </Typography>
                      <Typography variant="caption" color="success.main">
                        {stat.change}
                      </Typography>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </motion.div>

      {/* Admin Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          Administrative Actions
        </Typography>
        <Grid container spacing={3}>
          {adminActions.map((action, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Card
                component={motion.div}
                whileHover={{ y: -4, transition: { duration: 0.2 } }}
                sx={{
                  height: '100%',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  border: action.priority === 'high' ? `2px solid ${theme.palette.primary.main}30` : undefined
                }}
              >
                <CardContent sx={{ pb: 1 }}>
                  <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 2 }}>
                    <Avatar
                      sx={{
                        bgcolor: `${action.color}.main`,
                        width: 48,
                        height: 48
                      }}
                    >
                      {action.icon}
                    </Avatar>
                    {action.priority === 'high' && (
                      <Chip label="Priority" color="primary" size="small" />
                    )}
                  </Stack>
                  <Typography variant="h6" gutterBottom>
                    {action.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {action.description}
                  </Typography>
                </CardContent>
                <CardActions>
                  <Button
                    component={Link}
                    to={action.link}
                    color={action.color as any}
                    size="small"
                    variant={action.priority === 'high' ? 'contained' : 'outlined'}
                  >
                    Open
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      </motion.div>
    </Box>
  );
};

export default AdminDashboard;
