import React from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Avatar,
  Chip
} from '@mui/material';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

interface QuickActionCardProps {
  title: string;
  description: string;
  link: string;
  icon: React.ReactNode;
  color: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  available: boolean;
  delay?: number;
}

const QuickActionCard: React.FC<QuickActionCardProps> = ({
  title,
  description,
  link,
  icon,
  color,
  available,
  delay = 0
}) => {
  return (
    <Card
      component={motion.div}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      whileHover={available ? { 
        y: -4, 
        transition: { duration: 0.2 } 
      } : {}}
      sx={{
        height: '100%',
        opacity: available ? 1 : 0.6,
        cursor: available ? 'pointer' : 'not-allowed',
        transition: 'all 0.3s ease',
        position: 'relative',
        '&:hover': available ? {
          boxShadow: (theme) => `0 8px 32px ${theme.palette[color].main}20`
        } : {}
      }}
    >
      {!available && (
        <Chip
          label="Admin Only"
          size="small"
          color="warning"
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            zIndex: 1
          }}
        />
      )}
      <CardContent sx={{ pb: 1 }}>
        <Avatar
          sx={{
            bgcolor: `${color}.main`,
            mb: 2,
            width: 56,
            height: 56
          }}
        >
          {icon}
        </Avatar>
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {description}
        </Typography>
      </CardContent>
      <CardActions>
        {available ? (
          <Button
            component={Link}
            to={link}
            color={color}
            size="small"
            variant="outlined"
          >
            Open
          </Button>
        ) : (
          <Button disabled size="small">
            Restricted
          </Button>
        )}
      </CardActions>
    </Card>
  );
};

export default QuickActionCard;
