import React from 'react';
import {
  <PERSON>,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Avatar,
  Stack,
  Chip,
  Paper,
  useTheme,
  LinearProgress
} from '@mui/material';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  Person,
  DirectionsCar,
  Assignment,
  History,
  Notifications,
  Help,
  Star,
  TrendingUp
} from '@mui/icons-material';

interface UserDashboardProps {
  token: string;
  roles: string[];
}

const UserDashboard: React.FC<UserDashboardProps> = ({ token, roles }) => {
  const theme = useTheme();

  const userActions = [
    {
      title: 'Create STR Agreement',
      description: 'Start a new short-term rental agreement',
      link: '/str-new',
      icon: <DirectionsCar />,
      color: 'primary',
      featured: true
    },
    {
      title: 'Process Closure',
      description: 'Close existing rental agreements',
      link: '/closure-new',
      icon: <Assignment />,
      color: 'secondary',
      featured: true
    },
    {
      title: 'View History',
      description: 'Check your agreement history',
      link: '/history',
      icon: <History />,
      color: 'info',
      featured: false
    },
    {
      title: 'Help & Support',
      description: 'Get help with the platform',
      link: '/support',
      icon: <Help />,
      color: 'warning',
      featured: false
    }
  ];

  const userStats = [
    { label: 'Active Agreements', value: '3', icon: <DirectionsCar /> },
    { label: 'Completed This Month', value: '12', icon: <Assignment /> },
    { label: 'Success Rate', value: '98%', icon: <Star /> },
    { label: 'Performance Score', value: '4.8/5', icon: <TrendingUp /> }
  ];

  const recentActivity = [
    { action: 'STR Agreement Created', time: '2 hours ago', status: 'completed' },
    { action: 'Closure Processed', time: '1 day ago', status: 'completed' },
    { action: 'Payment Received', time: '2 days ago', status: 'completed' },
    { action: 'Agreement Pending', time: '3 days ago', status: 'pending' }
  ];

  return (
    <Box>
      {/* User Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Paper
          sx={{
            p: 3,
            mb: 4,
            background: `linear-gradient(135deg, ${theme.palette.secondary.main}15, ${theme.palette.secondary.main}05)`,
            border: `1px solid ${theme.palette.secondary.main}30`
          }}
        >
          <Stack direction="row" alignItems="center" spacing={2}>
            <Avatar sx={{ bgcolor: 'secondary.main', width: 56, height: 56 }}>
              <Person />
            </Avatar>
            <Box>
              <Typography variant="h4" color="secondary.main" sx={{ fontWeight: 600 }}>
                User Dashboard
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Welcome back! Manage your agreements and track your progress.
              </Typography>
            </Box>
            <Box sx={{ ml: 'auto' }}>
              <Stack direction="row" spacing={1}>
                {roles.map((role) => (
                  <Chip
                    key={role}
                    label={role}
                    color="secondary"
                    variant="outlined"
                  />
                ))}
              </Stack>
            </Box>
          </Stack>
        </Paper>
      </motion.div>

      {/* User Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          Your Performance
        </Typography>
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {userStats.map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card>
                <CardContent>
                  <Stack direction="row" alignItems="center" spacing={2}>
                    <Avatar sx={{ bgcolor: 'secondary.main' }}>
                      {stat.icon}
                    </Avatar>
                    <Box>
                      <Typography variant="h6">{stat.value}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {stat.label}
                      </Typography>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </motion.div>

      <Grid container spacing={3}>
        {/* Quick Actions */}
        <Grid item xs={12} md={8}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
              Quick Actions
            </Typography>
            <Grid container spacing={3}>
              {userActions.map((action, index) => (
                <Grid item xs={12} sm={6} key={index}>
                  <Card
                    component={motion.div}
                    whileHover={{ y: -4, transition: { duration: 0.2 } }}
                    sx={{
                      height: '100%',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      border: action.featured ? `2px solid ${theme.palette.secondary.main}30` : undefined
                    }}
                  >
                    <CardContent sx={{ pb: 1 }}>
                      <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 2 }}>
                        <Avatar
                          sx={{
                            bgcolor: `${action.color}.main`,
                            width: 48,
                            height: 48
                          }}
                        >
                          {action.icon}
                        </Avatar>
                        {action.featured && (
                          <Chip label="Featured" color="secondary" size="small" />
                        )}
                      </Stack>
                      <Typography variant="h6" gutterBottom>
                        {action.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {action.description}
                      </Typography>
                    </CardContent>
                    <CardActions>
                      <Button
                        component={Link}
                        to={action.link}
                        color={action.color as any}
                        size="small"
                        variant={action.featured ? 'contained' : 'outlined'}
                      >
                        Open
                      </Button>
                    </CardActions>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={4}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
              Recent Activity
            </Typography>
            <Card>
              <CardContent>
                <Stack spacing={2}>
                  {recentActivity.map((activity, index) => (
                    <Box key={index}>
                      <Stack direction="row" alignItems="center" spacing={2}>
                        <Box
                          sx={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            bgcolor: activity.status === 'completed' ? 'success.main' : 'warning.main'
                          }}
                        />
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {activity.action}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {activity.time}
                          </Typography>
                        </Box>
                      </Stack>
                      {index < recentActivity.length - 1 && (
                        <Box sx={{ ml: 2, mt: 1 }}>
                          <LinearProgress
                            variant="determinate"
                            value={activity.status === 'completed' ? 100 : 60}
                            sx={{ height: 2 }}
                          />
                        </Box>
                      )}
                    </Box>
                  ))}
                </Stack>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>
    </Box>
  );
};

export default UserDashboard;
