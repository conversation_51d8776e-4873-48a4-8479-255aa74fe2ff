import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Paper,
  useTheme,
  Chip,
  Stack,
  IconButton,
  Tooltip,
  Badge
} from '@mui/material';
import {
  CloudSync,
  Dashboard,
  PlayArrow,
  History,
  Settings,
  Warning,
  Help,
  Assessment
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import IntegrationsDashboard from './IntegrationsDashboard';
import ExportsManager from './ExportsManager';
import ExportsHistory from './ExportsHistory';
import IntegrationsSettings from './IntegrationsSettings';

interface IntegrationsWorkspaceProps {
  token: string;
  userRoles: string[];
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`integrations-tabpanel-${index}`}
      aria-labelledby={`integrations-tab-${index}`}
    >
      {value === index && (
        <AnimatePresence mode="wait">
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Box sx={{ py: 3 }}>
              {children}
            </Box>
          </motion.div>
        </AnimatePresence>
      )}
    </div>
  );
}

const IntegrationsWorkspace: React.FC<IntegrationsWorkspaceProps> = ({ token, userRoles = [] }) => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const isAdmin = userRoles?.includes('admin') || false;
  const isFinance = userRoles?.includes('finance') || isAdmin;

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Mock data for alerts - in real implementation, this would come from API
  const alerts = {
    failedExports: 3,
    pendingExports: 12,
    retryingExports: 2
  };

  const totalAlerts = alerts.failedExports + alerts.retryingExports;

  const tabs = [
    {
      label: 'Dashboard',
      icon: <Dashboard />,
      component: <IntegrationsDashboard token={token} userRoles={userRoles} alerts={alerts} />,
      adminOnly: false
    },
    {
      label: 'Export Manager',
      icon: <PlayArrow />,
      component: <ExportsManager token={token} userRoles={userRoles} />,
      adminOnly: false
    },
    {
      label: 'Export History',
      icon: <History />,
      component: <ExportsHistory token={token} userRoles={userRoles} />,
      adminOnly: false
    },
    {
      label: 'Settings',
      icon: <Settings />,
      component: <IntegrationsSettings token={token} userRoles={userRoles} />,
      adminOnly: true
    }
  ];

  const availableTabs = tabs.filter(tab => !tab.adminOnly || isAdmin);

  // Check if user has access to integrations
  if (!isFinance) {
    return (
      <Container maxWidth="md">
        <Box sx={{ py: 8, textAlign: 'center' }}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Paper sx={{ p: 6 }}>
              <Warning sx={{ fontSize: 64, color: 'warning.main', mb: 2 }} />
              <Typography variant="h4" gutterBottom>
                Access Restricted
              </Typography>
              <Typography variant="body1" color="text.secondary">
                This page is only accessible to Finance and Admin users.
              </Typography>
            </Paper>
          </motion.div>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 3 }}>
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Paper
            sx={{
              p: 3,
              mb: 3,
              background: `linear-gradient(135deg, ${theme.palette.info.main}15, ${theme.palette.info.main}05)`,
              border: `1px solid ${theme.palette.info.main}30`
            }}
          >
            <Stack direction="row" alignItems="center" justifyContent="space-between">
              <Box>
                <Typography variant="h4" sx={{ fontWeight: 600, color: 'info.main' }}>
                  System Integrations
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Enterprise data export and ERP integration management
                </Typography>
              </Box>
              <Stack direction="row" spacing={1} alignItems="center">
                <Chip
                  label={isAdmin ? 'Admin Access' : 'Finance Access'}
                  color={isAdmin ? 'primary' : 'info'}
                  variant="outlined"
                />
                {totalAlerts > 0 && (
                  <Badge badgeContent={totalAlerts} color="error">
                    <Tooltip title={`${totalAlerts} exports requiring attention`}>
                      <IconButton size="small" color="warning">
                        <Warning />
                      </IconButton>
                    </Tooltip>
                  </Badge>
                )}
                <Tooltip title="Help & Documentation">
                  <IconButton size="small">
                    <Help />
                  </IconButton>
                </Tooltip>
                {isAdmin && (
                  <Tooltip title="Integration Settings">
                    <IconButton size="small">
                      <Settings />
                    </IconButton>
                  </Tooltip>
                )}
              </Stack>
            </Stack>
          </Paper>
        </motion.div>

        {/* Navigation Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Paper sx={{ mb: 3 }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                borderBottom: 1,
                borderColor: 'divider',
                '& .MuiTab-root': {
                  minHeight: 72,
                  textTransform: 'none',
                  fontSize: '0.95rem',
                  fontWeight: 500
                }
              }}
            >
              {availableTabs.map((tab, index) => (
                <Tab
                  key={index}
                  icon={tab.icon}
                  label={tab.label}
                  iconPosition="start"
                  sx={{
                    '& .MuiSvgIcon-root': {
                      mr: 1
                    }
                  }}
                />
              ))}
            </Tabs>
          </Paper>
        </motion.div>

        {/* Tab Content */}
        {availableTabs.map((tab, index) => (
          <TabPanel key={index} value={activeTab} index={index}>
            {tab.component}
          </TabPanel>
        ))}
      </Box>
    </Container>
  );
};

export default IntegrationsWorkspace;
