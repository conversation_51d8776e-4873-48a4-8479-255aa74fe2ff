import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  Button,
  Stack,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  Avatar,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Search,
  FilterList,
  Download,
  ExpandMore,
  CloudSync,
  Receipt,
  AccountBalance,
  CheckCircle,
  Error,
  Schedule,
  Person,
  CalendarToday,
  Refresh
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface ExportsHistoryProps {
  token: string;
  userRoles: string[];
}

interface HistoryRecord {
  id: string;
  exportType: 'invoice' | 'receipt' | 'gl_entry';
  sourceType: string;
  sourceId: string;
  status: 'completed' | 'failed';
  attempts: number;
  filePath?: string;
  fileName?: string;
  fileSize?: number;
  errorMessage?: string;
  createdAt: string;
  completedAt: string;
  createdBy: string;
  processingTime: number;
  recordsProcessed: number;
  recordsSuccessful: number;
  recordsFailed: number;
}

const ExportsHistory: React.FC<ExportsHistoryProps> = ({ token, userRoles = [] }) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [dateRange, setDateRange] = useState({ from: '', to: '' });
  const [expandedRecord, setExpandedRecord] = useState<string | null>(null);

  // Mock data - in real implementation, this would come from API
  const historyRecords: HistoryRecord[] = [
    {
      id: 'EXP-2024-001',
      exportType: 'invoice',
      sourceType: 'agreement',
      sourceId: 'AGR-2024-001',
      status: 'completed',
      attempts: 1,
      filePath: '/exports/invoices/2024/01/invoice_batch_20240128.csv',
      fileName: 'invoice_batch_20240128.csv',
      fileSize: 25600,
      createdAt: '2024-01-28T10:30:00Z',
      completedAt: '2024-01-28T10:33:00Z',
      createdBy: 'admin',
      processingTime: 180,
      recordsProcessed: 45,
      recordsSuccessful: 45,
      recordsFailed: 0
    },
    {
      id: 'EXP-2024-002',
      exportType: 'receipt',
      sourceType: 'payment',
      sourceId: 'PAY-2024-045',
      status: 'completed',
      attempts: 1,
      filePath: '/exports/receipts/2024/01/receipt_batch_20240128.csv',
      fileName: 'receipt_batch_20240128.csv',
      fileSize: 15420,
      createdAt: '2024-01-28T09:15:00Z',
      completedAt: '2024-01-28T09:18:00Z',
      createdBy: 'finance_user',
      processingTime: 180,
      recordsProcessed: 23,
      recordsSuccessful: 23,
      recordsFailed: 0
    },
    {
      id: 'EXP-2024-003',
      exportType: 'gl_entry',
      sourceType: 'invoice',
      sourceId: 'INV-2024-002',
      status: 'failed',
      attempts: 3,
      errorMessage: 'Invalid GL account code: 4001-RENT. Please verify chart of accounts configuration.',
      createdAt: '2024-01-28T08:45:00Z',
      completedAt: '2024-01-28T08:52:00Z',
      createdBy: 'admin',
      processingTime: 420,
      recordsProcessed: 12,
      recordsSuccessful: 0,
      recordsFailed: 12
    },
    {
      id: 'EXP-2024-004',
      exportType: 'invoice',
      sourceType: 'agreement',
      sourceId: 'AGR-2024-003',
      status: 'completed',
      attempts: 2,
      filePath: '/exports/invoices/2024/01/invoice_batch_20240127.csv',
      fileName: 'invoice_batch_20240127.csv',
      fileSize: 18900,
      createdAt: '2024-01-27T16:00:00Z',
      completedAt: '2024-01-27T16:05:00Z',
      createdBy: 'system',
      processingTime: 300,
      recordsProcessed: 34,
      recordsSuccessful: 34,
      recordsFailed: 0
    },
    {
      id: 'EXP-2024-005',
      exportType: 'receipt',
      sourceType: 'payment',
      sourceId: 'PAY-2024-046',
      status: 'failed',
      attempts: 3,
      errorMessage: 'Connection timeout to ERP system. Network connectivity issues detected.',
      createdAt: '2024-01-27T14:30:00Z',
      completedAt: '2024-01-27T14:45:00Z',
      createdBy: 'admin',
      processingTime: 900,
      recordsProcessed: 18,
      recordsSuccessful: 0,
      recordsFailed: 18
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'invoice': return <Receipt />;
      case 'receipt': return <CheckCircle />;
      case 'gl_entry': return <AccountBalance />;
      default: return <CloudSync />;
    }
  };

  const formatDateTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('en-AE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return minutes > 0 ? `${minutes}m ${remainingSeconds}s` : `${remainingSeconds}s`;
  };

  const filteredRecords = historyRecords.filter(record => {
    const matchesSearch = 
      record.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.sourceId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.createdBy.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || record.status === statusFilter;
    const matchesType = typeFilter === 'all' || record.exportType === typeFilter;
    
    const matchesDateRange = 
      (!dateRange.from || record.createdAt >= dateRange.from) &&
      (!dateRange.to || record.createdAt <= dateRange.to);

    return matchesSearch && matchesStatus && matchesType && matchesDateRange;
  });

  const paginatedRecords = filteredRecords.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  const handleDownloadFile = (record: HistoryRecord) => {
    if (record.filePath && record.fileName) {
      // In real implementation, this would download the file
      console.log('Downloading file:', record.fileName);
    }
  };

  const handleExportHistory = () => {
    console.log('Exporting history data...', filteredRecords);
  };

  const successfulExports = filteredRecords.filter(r => r.status === 'completed').length;
  const failedExports = filteredRecords.filter(r => r.status === 'failed').length;
  const totalRecordsProcessed = filteredRecords.reduce((sum, r) => sum + r.recordsProcessed, 0);

  return (
    <Box>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          Export History & Audit Trail
        </Typography>

        {/* Summary Stats */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="success.main" sx={{ fontWeight: 600 }}>
                {successfulExports}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Successful Exports
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="error.main" sx={{ fontWeight: 600 }}>
                {failedExports}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Failed Exports
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="primary.main" sx={{ fontWeight: 600 }}>
                {totalRecordsProcessed}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Records Processed
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="info.main" sx={{ fontWeight: 600 }}>
                {filteredRecords.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Exports
              </Typography>
            </Paper>
          </Grid>
        </Grid>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Search & Filters
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder="Search by ID, source, or user..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    label="Status"
                  >
                    <MenuItem value="all">All Statuses</MenuItem>
                    <MenuItem value="completed">Completed</MenuItem>
                    <MenuItem value="failed">Failed</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Type</InputLabel>
                  <Select
                    value={typeFilter}
                    onChange={(e) => setTypeFilter(e.target.value)}
                    label="Type"
                  >
                    <MenuItem value="all">All Types</MenuItem>
                    <MenuItem value="invoice">Invoices</MenuItem>
                    <MenuItem value="receipt">Receipts</MenuItem>
                    <MenuItem value="gl_entry">GL Entries</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  fullWidth
                  label="From Date"
                  type="date"
                  value={dateRange.from}
                  onChange={(e) => setDateRange({ ...dateRange, from: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  fullWidth
                  label="To Date"
                  type="date"
                  value={dateRange.to}
                  onChange={(e) => setDateRange({ ...dateRange, to: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
            </Grid>
            <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
              <Button
                variant="outlined"
                startIcon={<FilterList />}
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                  setTypeFilter('all');
                  setDateRange({ from: '', to: '' });
                }}
              >
                Clear Filters
              </Button>
              <Button
                variant="outlined"
                startIcon={<Download />}
                onClick={handleExportHistory}
              >
                Export History
              </Button>
            </Stack>
          </CardContent>
        </Card>

        {/* Results Summary */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="body1">
            Showing {paginatedRecords.length} of {filteredRecords.length} export records
          </Typography>
        </Paper>

        {/* History Table */}
        <Card>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Export ID</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Source</TableCell>
                  <TableCell align="center">Status</TableCell>
                  <TableCell align="center">Records</TableCell>
                  <TableCell>File</TableCell>
                  <TableCell>Duration</TableCell>
                  <TableCell>Completed</TableCell>
                  <TableCell>Created By</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedRecords.map((record) => (
                  <React.Fragment key={record.id}>
                    <TableRow hover>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {record.id}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Avatar sx={{
                            bgcolor: record.exportType === 'invoice' ? 'primary.main' :
                                     record.exportType === 'receipt' ? 'success.main' : 'info.main',
                            width: 32,
                            height: 32
                          }}>
                            {getTypeIcon(record.exportType)}
                          </Avatar>
                          <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                            {record.exportType.replace('_', ' ')}
                          </Typography>
                        </Stack>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {record.sourceId}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {record.sourceType}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          label={record.status}
                          size="small"
                          color={getStatusColor(record.status)}
                          icon={record.status === 'completed' ? <CheckCircle /> : <Error />}
                        />
                        {record.attempts > 1 && (
                          <Typography variant="caption" display="block" color="text.secondary">
                            {record.attempts} attempts
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell align="center">
                        <Stack alignItems="center">
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {record.recordsProcessed}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {record.recordsSuccessful}✓ {record.recordsFailed}✗
                          </Typography>
                        </Stack>
                      </TableCell>
                      <TableCell>
                        {record.fileName ? (
                          <Stack>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {record.fileName}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {record.fileSize ? formatFileSize(record.fileSize) : 'Unknown size'}
                            </Typography>
                          </Stack>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            No file generated
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDuration(record.processingTime)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <CalendarToday fontSize="small" color="action" />
                          <Typography variant="body2">
                            {formatDateTime(record.completedAt)}
                          </Typography>
                        </Stack>
                      </TableCell>
                      <TableCell>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Person fontSize="small" color="action" />
                          <Typography variant="body2">
                            {record.createdBy}
                          </Typography>
                        </Stack>
                      </TableCell>
                      <TableCell align="center">
                        <Stack direction="row" spacing={1}>
                          {record.fileName && (
                            <Tooltip title="Download file">
                              <IconButton
                                size="small"
                                onClick={() => handleDownloadFile(record)}
                              >
                                <Download />
                              </IconButton>
                            </Tooltip>
                          )}
                          {(record.errorMessage || record.recordsFailed > 0) && (
                            <Tooltip title="View details">
                              <IconButton
                                size="small"
                                onClick={() => setExpandedRecord(
                                  expandedRecord === record.id ? null : record.id
                                )}
                              >
                                <ExpandMore
                                  sx={{
                                    transform: expandedRecord === record.id ? 'rotate(180deg)' : 'rotate(0deg)',
                                    transition: 'transform 0.3s'
                                  }}
                                />
                              </IconButton>
                            </Tooltip>
                          )}
                        </Stack>
                      </TableCell>
                    </TableRow>

                    {/* Expandable Details Row */}
                    {expandedRecord === record.id && (
                      <TableRow>
                        <TableCell colSpan={10} sx={{ py: 0 }}>
                          <Accordion expanded={true} sx={{ boxShadow: 'none' }}>
                            <AccordionDetails sx={{ pt: 2 }}>
                              <Grid container spacing={3}>
                                <Grid item xs={12} md={6}>
                                  <Typography variant="subtitle2" gutterBottom>
                                    Export Details
                                  </Typography>
                                  <Stack spacing={1}>
                                    <Typography variant="body2">
                                      <strong>Created:</strong> {formatDateTime(record.createdAt)}
                                    </Typography>
                                    <Typography variant="body2">
                                      <strong>Completed:</strong> {formatDateTime(record.completedAt)}
                                    </Typography>
                                    <Typography variant="body2">
                                      <strong>Processing Time:</strong> {formatDuration(record.processingTime)}
                                    </Typography>
                                    <Typography variant="body2">
                                      <strong>Attempts:</strong> {record.attempts}
                                    </Typography>
                                  </Stack>
                                </Grid>
                                <Grid item xs={12} md={6}>
                                  <Typography variant="subtitle2" gutterBottom>
                                    Processing Summary
                                  </Typography>
                                  <Stack spacing={1}>
                                    <Typography variant="body2">
                                      <strong>Total Records:</strong> {record.recordsProcessed}
                                    </Typography>
                                    <Typography variant="body2" color="success.main">
                                      <strong>Successful:</strong> {record.recordsSuccessful}
                                    </Typography>
                                    <Typography variant="body2" color="error.main">
                                      <strong>Failed:</strong> {record.recordsFailed}
                                    </Typography>
                                    {record.filePath && (
                                      <Typography variant="body2">
                                        <strong>File Path:</strong> {record.filePath}
                                      </Typography>
                                    )}
                                  </Stack>
                                </Grid>
                                {record.errorMessage && (
                                  <Grid item xs={12}>
                                    <Typography variant="subtitle2" gutterBottom>
                                      Error Details
                                    </Typography>
                                    <Paper sx={{ p: 2, bgcolor: 'error.light', color: 'error.contrastText' }}>
                                      <Typography variant="body2">
                                        {record.errorMessage}
                                      </Typography>
                                    </Paper>
                                  </Grid>
                                )}
                              </Grid>
                            </AccordionDetails>
                          </Accordion>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50]}
            component="div"
            count={filteredRecords.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            onRowsPerPageChange={(e) => {
              setRowsPerPage(parseInt(e.target.value, 10));
              setPage(0);
            }}
          />
        </Card>
      </motion.div>
    </Box>
  );
};

export default ExportsHistory;
