import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  Button,
  Stack,
  Chip,
  IconButton,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  Avatar,
  Menu,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar,
  CircularProgress
} from '@mui/material';
import {
  Search,
  FilterList,
  PlayArrow,
  Refresh,
  Download,
  RestartAlt,
  Visibility,
  MoreVert,
  CloudSync,
  Receipt,
  AccountBalance,
  Error,
  CheckCircle,
  Schedule,
  Warning
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface ExportsManagerProps {
  token: string;
  userRoles: string[];
}

interface ExportJob {
  id: string;
  exportType: 'invoice' | 'receipt' | 'gl_entry';
  sourceType: string;
  sourceId: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'retrying';
  attempts: number;
  maxAttempts: number;
  filePath?: string;
  fileName?: string;
  fileSize?: number;
  lastError?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  processingTime?: number;
}

const ExportsManager: React.FC<ExportsManagerProps> = ({ token, userRoles = [] }) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('pending');
  const [typeFilter, setTypeFilter] = useState('all');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedJob, setSelectedJob] = useState<ExportJob | null>(null);
  const [errorDialogOpen, setErrorDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // Mock data - in real implementation, this would come from API
  const [exportJobs, setExportJobs] = useState<ExportJob[]>([
    {
      id: 'EXP-2024-001',
      exportType: 'invoice',
      sourceType: 'agreement',
      sourceId: 'AGR-2024-001',
      status: 'pending',
      attempts: 0,
      maxAttempts: 3,
      createdAt: '2024-01-28T10:30:00Z',
      updatedAt: '2024-01-28T10:30:00Z',
      createdBy: 'admin'
    },
    {
      id: 'EXP-2024-002',
      exportType: 'receipt',
      sourceType: 'payment',
      sourceId: 'PAY-2024-045',
      status: 'completed',
      attempts: 1,
      maxAttempts: 3,
      filePath: '/exports/receipts/2024/01/receipt_batch_20240128.csv',
      fileName: 'receipt_batch_20240128.csv',
      fileSize: 15420,
      createdAt: '2024-01-28T09:15:00Z',
      updatedAt: '2024-01-28T09:18:00Z',
      createdBy: 'finance_user',
      processingTime: 180
    },
    {
      id: 'EXP-2024-003',
      exportType: 'gl_entry',
      sourceType: 'invoice',
      sourceId: 'INV-2024-002',
      status: 'failed',
      attempts: 3,
      maxAttempts: 3,
      lastError: 'Invalid GL account code: 4001-RENT',
      createdAt: '2024-01-28T08:45:00Z',
      updatedAt: '2024-01-28T08:52:00Z',
      createdBy: 'admin'
    },
    {
      id: 'EXP-2024-004',
      exportType: 'invoice',
      sourceType: 'agreement',
      sourceId: 'AGR-2024-003',
      status: 'retrying',
      attempts: 2,
      maxAttempts: 3,
      lastError: 'Connection timeout to ERP system',
      createdAt: '2024-01-28T08:00:00Z',
      updatedAt: '2024-01-28T08:15:00Z',
      createdBy: 'system'
    },
    {
      id: 'EXP-2024-005',
      exportType: 'receipt',
      sourceType: 'payment',
      sourceId: 'PAY-2024-046',
      status: 'in_progress',
      attempts: 1,
      maxAttempts: 3,
      createdAt: '2024-01-28T07:30:00Z',
      updatedAt: '2024-01-28T07:32:00Z',
      createdBy: 'admin'
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'failed': return 'error';
      case 'in_progress': case 'retrying': return 'warning';
      case 'pending': return 'info';
      default: return 'default';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'invoice': return <Receipt />;
      case 'receipt': return <CheckCircle />;
      case 'gl_entry': return <AccountBalance />;
      default: return <CloudSync />;
    }
  };

  const formatDateTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('en-AE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const filteredJobs = exportJobs.filter(job => {
    const matchesSearch = 
      job.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.sourceId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.createdBy.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || job.status === statusFilter;
    const matchesType = typeFilter === 'all' || job.exportType === typeFilter;

    return matchesSearch && matchesStatus && matchesType;
  });

  const paginatedJobs = filteredJobs.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, job: ExportJob) => {
    setAnchorEl(event.currentTarget);
    setSelectedJob(job);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedJob(null);
  };

  const handleRunExports = async () => {
    setLoading(true);
    try {
      // API call to run pending exports
      console.log('Running pending exports...');
      // await api.runExports(token);
      
      // Simulate API response
      setTimeout(() => {
        setSnackbar({
          open: true,
          message: 'Export jobs started successfully',
          severity: 'success'
        });
        setLoading(false);
        // Refresh data
        refreshData();
      }, 2000);
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to start export jobs',
        severity: 'error'
      });
      setLoading(false);
    }
  };

  const handleRetryExport = async (jobId: string) => {
    try {
      // API call to retry specific export
      console.log('Retrying export:', jobId);
      // await api.retryExport(token, jobId);
      
      setSnackbar({
        open: true,
        message: 'Export retry initiated',
        severity: 'success'
      });
      handleMenuClose();
      refreshData();
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to retry export',
        severity: 'error'
      });
    }
  };

  const handleDownloadFile = (job: ExportJob) => {
    if (job.filePath && job.fileName) {
      // In real implementation, this would download the file
      console.log('Downloading file:', job.fileName);
      setSnackbar({
        open: true,
        message: `Downloading ${job.fileName}`,
        severity: 'success'
      });
    }
    handleMenuClose();
  };

  const handleViewError = () => {
    setErrorDialogOpen(true);
    handleMenuClose();
  };

  const refreshData = () => {
    // In real implementation, this would fetch fresh data from API
    console.log('Refreshing export data...');
  };

  const pendingJobsCount = exportJobs.filter(job => job.status === 'pending').length;
  const failedJobsCount = exportJobs.filter(job => job.status === 'failed').length;

  return (
    <Box>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
          <Typography variant="h5" sx={{ fontWeight: 600 }}>
            Export Manager
          </Typography>
          <Stack direction="row" spacing={2}>
            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={refreshData}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={loading ? <CircularProgress size={16} /> : <PlayArrow />}
              onClick={handleRunExports}
              disabled={loading || pendingJobsCount === 0}
            >
              {loading ? 'Running...' : `Run Exports (${pendingJobsCount})`}
            </Button>
          </Stack>
        </Stack>

        {/* Quick Stats */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="info.main" sx={{ fontWeight: 600 }}>
                {pendingJobsCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Pending Exports
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="error.main" sx={{ fontWeight: 600 }}>
                {failedJobsCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Failed Exports
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main" sx={{ fontWeight: 600 }}>
                {exportJobs.filter(job => job.status === 'retrying').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Retrying
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="success.main" sx={{ fontWeight: 600 }}>
                {exportJobs.filter(job => job.status === 'completed').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Completed
              </Typography>
            </Paper>
          </Grid>
        </Grid>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Search & Filters
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder="Search by ID, source, or user..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    label="Status"
                  >
                    <MenuItem value="all">All Statuses</MenuItem>
                    <MenuItem value="pending">Pending</MenuItem>
                    <MenuItem value="in_progress">In Progress</MenuItem>
                    <MenuItem value="completed">Completed</MenuItem>
                    <MenuItem value="failed">Failed</MenuItem>
                    <MenuItem value="retrying">Retrying</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Type</InputLabel>
                  <Select
                    value={typeFilter}
                    onChange={(e) => setTypeFilter(e.target.value)}
                    label="Type"
                  >
                    <MenuItem value="all">All Types</MenuItem>
                    <MenuItem value="invoice">Invoices</MenuItem>
                    <MenuItem value="receipt">Receipts</MenuItem>
                    <MenuItem value="gl_entry">GL Entries</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <Button
                  variant="outlined"
                  startIcon={<FilterList />}
                  fullWidth
                  sx={{ height: '100%' }}
                  onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('pending');
                    setTypeFilter('all');
                  }}
                >
                  Clear Filters
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Results Summary */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="body1">
            Showing {paginatedJobs.length} of {filteredJobs.length} export jobs
          </Typography>
        </Paper>

        {/* Exports Table */}
        <Card>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Export ID</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Source</TableCell>
                  <TableCell align="center">Status</TableCell>
                  <TableCell align="center">Attempts</TableCell>
                  <TableCell>File</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell>Created By</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedJobs.map((job) => (
                  <TableRow key={job.id} hover>
                    <TableCell>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {job.id}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <Avatar sx={{
                          bgcolor: job.exportType === 'invoice' ? 'primary.main' :
                                   job.exportType === 'receipt' ? 'success.main' : 'info.main',
                          width: 32,
                          height: 32
                        }}>
                          {getTypeIcon(job.exportType)}
                        </Avatar>
                        <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                          {job.exportType.replace('_', ' ')}
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {job.sourceId}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {job.sourceType}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="center">
                      <Chip
                        label={job.status.replace('_', ' ')}
                        size="small"
                        color={getStatusColor(job.status)}
                        sx={{ textTransform: 'capitalize' }}
                        icon={
                          job.status === 'completed' ? <CheckCircle /> :
                          job.status === 'failed' ? <Error /> :
                          job.status === 'in_progress' ? <Schedule /> :
                          job.status === 'retrying' ? <Refresh /> : undefined
                        }
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Stack alignItems="center">
                        <Typography variant="body2">
                          {job.attempts}/{job.maxAttempts}
                        </Typography>
                        {job.attempts > 0 && (
                          <Typography variant="caption" color="text.secondary">
                            {job.processingTime ? `${job.processingTime}s` : 'Processing...'}
                          </Typography>
                        )}
                      </Stack>
                    </TableCell>
                    <TableCell>
                      {job.fileName ? (
                        <Stack>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {job.fileName}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {job.fileSize ? formatFileSize(job.fileSize) : 'Unknown size'}
                          </Typography>
                        </Stack>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          No file generated
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatDateTime(job.createdAt)}
                      </Typography>
                      {job.updatedAt !== job.createdAt && (
                        <Typography variant="caption" color="text.secondary" display="block">
                          Updated: {formatDateTime(job.updatedAt)}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {job.createdBy}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Tooltip title="More actions">
                        <IconButton
                          size="small"
                          onClick={(e) => handleMenuOpen(e, job)}
                        >
                          <MoreVert />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50]}
            component="div"
            count={filteredJobs.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            onRowsPerPageChange={(e) => {
              setRowsPerPage(parseInt(e.target.value, 10));
              setPage(0);
            }}
          />
        </Card>

        {/* Actions Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          {selectedJob?.status === 'completed' && selectedJob.fileName && (
            <MenuItem onClick={() => selectedJob && handleDownloadFile(selectedJob)}>
              <ListItemIcon>
                <Download fontSize="small" />
              </ListItemIcon>
              <ListItemText>Download File</ListItemText>
            </MenuItem>
          )}
          {selectedJob?.status === 'failed' && selectedJob.attempts < selectedJob.maxAttempts && (
            <MenuItem onClick={() => selectedJob && handleRetryExport(selectedJob.id)}>
              <ListItemIcon>
                <RestartAlt fontSize="small" />
              </ListItemIcon>
              <ListItemText>Retry Export</ListItemText>
            </MenuItem>
          )}
          {selectedJob?.lastError && (
            <MenuItem onClick={handleViewError}>
              <ListItemIcon>
                <Visibility fontSize="small" />
              </ListItemIcon>
              <ListItemText>View Error</ListItemText>
            </MenuItem>
          )}
        </Menu>

        {/* Error Dialog */}
        <Dialog open={errorDialogOpen} onClose={() => setErrorDialogOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>Export Error Details</DialogTitle>
          <DialogContent>
            {selectedJob && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Export: {selectedJob.id}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Type: {selectedJob.exportType} | Source: {selectedJob.sourceId}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Attempts: {selectedJob.attempts}/{selectedJob.maxAttempts}
                </Typography>

                <Alert severity="error" sx={{ mt: 2 }}>
                  <Typography variant="body2">
                    {selectedJob.lastError}
                  </Typography>
                </Alert>

                <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                  Last attempt: {formatDateTime(selectedJob.updatedAt)}
                </Typography>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setErrorDialogOpen(false)}>Close</Button>
            {selectedJob?.status === 'failed' && selectedJob.attempts < selectedJob.maxAttempts && (
              <Button
                variant="contained"
                onClick={() => {
                  if (selectedJob) {
                    handleRetryExport(selectedJob.id);
                    setErrorDialogOpen(false);
                  }
                }}
              >
                Retry Export
              </Button>
            )}
          </DialogActions>
        </Dialog>

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          <Alert
            onClose={() => setSnackbar({ ...snackbar, open: false })}
            severity={snackbar.severity}
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </motion.div>
    </Box>
  );
};

export default ExportsManager;
