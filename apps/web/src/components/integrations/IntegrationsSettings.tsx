import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  Button,
  Stack,
  Switch,
  FormControlLabel,
  Divider,
  Alert,
  Chip,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Save,
  Science,
  CloudSync,
  Security,
  Schedule,
  Notifications,
  Storage,
  Settings,
  CheckCircle,
  Error,
  Warning,
  Edit,
  Delete
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface IntegrationsSettingsProps {
  token: string;
  userRoles: string[];
}

const IntegrationsSettings: React.FC<IntegrationsSettingsProps> = ({ token, userRoles = [] }) => {
  const [settings, setSettings] = useState({
    // ERP Connection Settings
    erpEndpoint: 'https://erp.company.com/api/v1',
    erpApiKey: '••••••••••••••••',
    erpTimeout: 30,
    erpRetryAttempts: 3,
    
    // Export Settings
    exportPath: '/var/exports/a-realm',
    maxFileSize: 50,
    compressionEnabled: true,
    encryptionEnabled: true,
    
    // Scheduling Settings
    autoExportEnabled: true,
    exportSchedule: '0 */6 * * *', // Every 6 hours
    batchSize: 100,
    
    // Notification Settings
    emailNotifications: true,
    notificationEmail: '<EMAIL>',
    alertOnFailure: true,
    alertOnSuccess: false,
    
    // Audit Settings
    auditLogRetention: 90,
    detailedLogging: true
  });

  const [testResults, setTestResults] = useState<Record<string, 'success' | 'error' | 'testing' | null>>({
    erpConnection: null,
    fileSystem: null,
    emailService: null
  });

  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editField, setEditField] = useState<string>('');
  const [editValue, setEditValue] = useState<string>('');

  const handleSettingChange = (field: string, value: any) => {
    setSettings(prev => ({ ...prev, [field]: value }));
  };

  const handleTestConnection = async (service: string) => {
    setTestResults(prev => ({ ...prev, [service]: 'testing' }));
    
    // Simulate API test
    setTimeout(() => {
      const success = Math.random() > 0.3; // 70% success rate for demo
      setTestResults(prev => ({ 
        ...prev, 
        [service]: success ? 'success' : 'error' 
      }));
    }, 2000);
  };

  const handleSaveSettings = async () => {
    try {
      // API call to save settings
      console.log('Saving settings...', settings);
      // await api.saveIntegrationSettings(token, settings);
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  };

  const handleEditField = (field: string, currentValue: string) => {
    setEditField(field);
    setEditValue(currentValue);
    setEditDialogOpen(true);
  };

  const handleSaveEdit = () => {
    handleSettingChange(editField, editValue);
    setEditDialogOpen(false);
    setEditField('');
    setEditValue('');
  };

  const getTestIcon = (status: 'success' | 'error' | 'testing' | null) => {
    switch (status) {
      case 'success': return <CheckCircle color="success" />;
      case 'error': return <Error color="error" />;
      case 'testing': return <CloudSync className="rotating" />;
      default: return <Science />;
    }
  };

  const connectionTests = [
    {
      id: 'erpConnection',
      name: 'ERP System Connection',
      description: 'Test connection to external ERP system',
      endpoint: settings.erpEndpoint
    },
    {
      id: 'fileSystem',
      name: 'File System Access',
      description: 'Test write access to export directory',
      endpoint: settings.exportPath
    },
    {
      id: 'emailService',
      name: 'Email Notifications',
      description: 'Test email notification service',
      endpoint: settings.notificationEmail
    }
  ];

  return (
    <Box>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          Integration Settings
        </Typography>

        <Grid container spacing={3}>
          {/* ERP Connection Settings */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 3 }}>
                  <CloudSync color="primary" />
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    ERP Connection Settings
                  </Typography>
                </Stack>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="ERP Endpoint URL"
                      value={settings.erpEndpoint}
                      onChange={(e) => handleSettingChange('erpEndpoint', e.target.value)}
                      fullWidth
                      helperText="Base URL for ERP API integration"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Stack direction="row" spacing={1} alignItems="center">
                      <TextField
                        label="API Key"
                        value={settings.erpApiKey}
                        fullWidth
                        type="password"
                        helperText="API key for ERP authentication"
                        disabled
                      />
                      <IconButton onClick={() => handleEditField('erpApiKey', settings.erpApiKey)}>
                        <Edit />
                      </IconButton>
                    </Stack>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="Connection Timeout (seconds)"
                      type="number"
                      value={settings.erpTimeout}
                      onChange={(e) => handleSettingChange('erpTimeout', parseInt(e.target.value))}
                      fullWidth
                      inputProps={{ min: 5, max: 300 }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="Retry Attempts"
                      type="number"
                      value={settings.erpRetryAttempts}
                      onChange={(e) => handleSettingChange('erpRetryAttempts', parseInt(e.target.value))}
                      fullWidth
                      inputProps={{ min: 1, max: 10 }}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Export Settings */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 3 }}>
                  <Storage color="primary" />
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Export Settings
                  </Typography>
                </Stack>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="Export Directory Path"
                      value={settings.exportPath}
                      onChange={(e) => handleSettingChange('exportPath', e.target.value)}
                      fullWidth
                      helperText="Server directory for export files"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="Max File Size (MB)"
                      type="number"
                      value={settings.maxFileSize}
                      onChange={(e) => handleSettingChange('maxFileSize', parseInt(e.target.value))}
                      fullWidth
                      inputProps={{ min: 1, max: 1000 }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.compressionEnabled}
                          onChange={(e) => handleSettingChange('compressionEnabled', e.target.checked)}
                        />
                      }
                      label="Enable File Compression"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.encryptionEnabled}
                          onChange={(e) => handleSettingChange('encryptionEnabled', e.target.checked)}
                        />
                      }
                      label="Enable File Encryption"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Scheduling Settings */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 3 }}>
                  <Schedule color="primary" />
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Scheduling Settings
                  </Typography>
                </Stack>
                
                <Stack spacing={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.autoExportEnabled}
                        onChange={(e) => handleSettingChange('autoExportEnabled', e.target.checked)}
                      />
                    }
                    label="Enable Automatic Exports"
                  />
                  <TextField
                    label="Export Schedule (Cron)"
                    value={settings.exportSchedule}
                    onChange={(e) => handleSettingChange('exportSchedule', e.target.value)}
                    fullWidth
                    helperText="Cron expression for scheduled exports"
                    disabled={!settings.autoExportEnabled}
                  />
                  <TextField
                    label="Batch Size"
                    type="number"
                    value={settings.batchSize}
                    onChange={(e) => handleSettingChange('batchSize', parseInt(e.target.value))}
                    fullWidth
                    inputProps={{ min: 10, max: 1000 }}
                    helperText="Number of records per export batch"
                  />
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          {/* Notification Settings */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 3 }}>
                  <Notifications color="primary" />
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Notification Settings
                  </Typography>
                </Stack>
                
                <Stack spacing={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.emailNotifications}
                        onChange={(e) => handleSettingChange('emailNotifications', e.target.checked)}
                      />
                    }
                    label="Enable Email Notifications"
                  />
                  <TextField
                    label="Notification Email"
                    value={settings.notificationEmail}
                    onChange={(e) => handleSettingChange('notificationEmail', e.target.value)}
                    fullWidth
                    type="email"
                    disabled={!settings.emailNotifications}
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.alertOnFailure}
                        onChange={(e) => handleSettingChange('alertOnFailure', e.target.checked)}
                      />
                    }
                    label="Alert on Export Failure"
                    disabled={!settings.emailNotifications}
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.alertOnSuccess}
                        onChange={(e) => handleSettingChange('alertOnSuccess', e.target.checked)}
                      />
                    }
                    label="Alert on Export Success"
                    disabled={!settings.emailNotifications}
                  />
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          {/* Connection Tests */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                  Connection Tests
                </Typography>
                <List>
                  {connectionTests.map((test) => (
                    <ListItem key={test.id}>
                      <ListItemIcon>
                        {getTestIcon(testResults[test.id])}
                      </ListItemIcon>
                      <ListItemText
                        primary={test.name}
                        secondary={
                          <Stack spacing={0.5}>
                            <Typography variant="body2" color="text.secondary">
                              {test.description}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {test.endpoint}
                            </Typography>
                          </Stack>
                        }
                      />
                      <ListItemSecondaryAction>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => handleTestConnection(test.id)}
                          disabled={testResults[test.id] === 'testing'}
                        >
                          {testResults[test.id] === 'testing' ? 'Testing...' : 'Test'}
                        </Button>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Save Actions */}
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Alert severity="info" sx={{ flex: 1, mr: 2 }}>
                  Changes will take effect after saving and may require system restart for some settings.
                </Alert>
                <Button
                  variant="contained"
                  startIcon={<Save />}
                  onClick={handleSaveSettings}
                  size="large"
                >
                  Save Settings
                </Button>
              </Stack>
            </Paper>
          </Grid>
        </Grid>

        {/* Edit Dialog */}
        <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Edit {editField}</DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              label="Value"
              type={editField.includes('Key') ? 'password' : 'text'}
              fullWidth
              variant="outlined"
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSaveEdit} variant="contained">Save</Button>
          </DialogActions>
        </Dialog>
      </motion.div>
    </Box>
  );
};

export default IntegrationsSettings;
