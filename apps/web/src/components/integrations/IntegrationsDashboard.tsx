import React from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ack,
  Avatar,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
  useTheme,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Button
} from '@mui/material';
import {
  CloudSync,
  CheckCircle,
  Error,
  Schedule,
  Refresh,
  TrendingUp,
  TrendingDown,
  PlayArrow,
  Download,
  Warning,
  Assessment,
  Receipt,
  AccountBalance
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface IntegrationsDashboardProps {
  token: string;
  userRoles: string[];
  alerts: {
    failedExports: number;
    pendingExports: number;
    retryingExports: number;
  };
}

const IntegrationsDashboard: React.FC<IntegrationsDashboardProps> = ({ token, userRoles = [], alerts }) => {
  const theme = useTheme();
  const isAdmin = userRoles?.includes('admin') || false;

  // Mock data - in real implementation, this would come from API
  const dashboardMetrics = {
    totalExports: 1247,
    successfulExports: 1198,
    failedExports: 37,
    pendingExports: 12,
    successRate: 96.1,
    avgProcessingTime: 2.3,
    lastRunTime: '2024-01-28T14:30:00Z',
    nextScheduledRun: '2024-01-28T18:00:00Z'
  };

  const exportTypes = [
    { type: 'Invoices', count: 856, success: 842, failed: 14, percentage: 68.7 },
    { type: 'Receipts', count: 312, success: 298, failed: 14, percentage: 25.0 },
    { type: 'GL Entries', count: 79, success: 70, failed: 9, percentage: 6.3 }
  ];

  const recentActivity = [
    { type: 'invoice', action: 'Export completed', count: 45, status: 'success', time: '2 hours ago', user: 'system' },
    { type: 'receipt', action: 'Export failed', count: 3, status: 'error', time: '3 hours ago', user: 'admin', error: 'Invalid GL code' },
    { type: 'gl_entry', action: 'Export retrying', count: 12, status: 'warning', time: '4 hours ago', user: 'system' },
    { type: 'invoice', action: 'Manual export triggered', count: 23, status: 'success', time: '6 hours ago', user: 'finance_user' }
  ];

  const systemStatus = [
    { system: 'ERP Connection', status: 'connected', lastSync: '2024-01-28T14:30:00Z' },
    { system: 'File Server', status: 'connected', lastSync: '2024-01-28T14:29:00Z' },
    { system: 'Backup System', status: 'connected', lastSync: '2024-01-28T14:25:00Z' },
    { system: 'Audit Logger', status: 'connected', lastSync: '2024-01-28T14:30:00Z' }
  ];

  const formatDateTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('en-AE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'invoice': return <Receipt />;
      case 'receipt': return <CheckCircle />;
      case 'gl_entry': return <AccountBalance />;
      default: return <CloudSync />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': case 'connected': return 'success';
      case 'error': case 'failed': return 'error';
      case 'warning': case 'retrying': return 'warning';
      case 'pending': return 'info';
      default: return 'default';
    }
  };

  const StatCard = ({ title, value, subtitle, icon, color, trend }: any) => (
    <Card>
      <CardContent>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Avatar sx={{ bgcolor: `${color}.main`, width: 56, height: 56 }}>
            {icon}
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h4" sx={{ fontWeight: 600 }}>
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="caption" color="text.secondary">
                {subtitle}
              </Typography>
            )}
            {trend && (
              <Stack direction="row" alignItems="center" spacing={1} sx={{ mt: 1 }}>
                {trend > 0 ? <TrendingUp color="success" fontSize="small" /> : <TrendingDown color="error" fontSize="small" />}
                <Typography 
                  variant="caption" 
                  color={trend > 0 ? 'success.main' : 'error.main'}
                  sx={{ fontWeight: 500 }}
                >
                  {Math.abs(trend)}% from last week
                </Typography>
              </Stack>
            )}
          </Box>
        </Stack>
      </CardContent>
    </Card>
  );

  return (
    <Box>
      {/* Alerts Section */}
      {(alerts.failedExports > 0 || alerts.retryingExports > 0) && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Alert 
            severity="warning" 
            icon={<Warning />}
            sx={{ mb: 3 }}
            action={
              <Button size="small" color="inherit" startIcon={<PlayArrow />}>
                Run Exports
              </Button>
            }
          >
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              Integration Alerts: {alerts.failedExports} failed exports, {alerts.retryingExports} retrying, 
              {alerts.pendingExports} pending processing
            </Typography>
          </Alert>
        </motion.div>
      )}

      {/* Key Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          Export Performance Overview
        </Typography>
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Exports"
              value={dashboardMetrics.totalExports}
              subtitle="All time"
              icon={<CloudSync />}
              color="primary"
              trend={12.5}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Success Rate"
              value={`${dashboardMetrics.successRate}%`}
              subtitle="Last 30 days"
              icon={<CheckCircle />}
              color="success"
              trend={2.1}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Failed Exports"
              value={dashboardMetrics.failedExports}
              subtitle="Requiring attention"
              icon={<Error />}
              color="error"
              trend={-15.3}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Avg Processing"
              value={`${dashboardMetrics.avgProcessingTime}min`}
              subtitle="Per export batch"
              icon={<Schedule />}
              color="info"
              trend={-8.7}
            />
          </Grid>
        </Grid>
      </motion.div>

      <Grid container spacing={3}>
        {/* Export Types Breakdown */}
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Export Types Distribution
                  </Typography>
                  <Tooltip title="Refresh data">
                    <IconButton size="small">
                      <Refresh />
                    </IconButton>
                  </Tooltip>
                </Stack>
                <Stack spacing={3}>
                  {exportTypes.map((type, index) => (
                    <Box key={index}>
                      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {type.type}
                          </Typography>
                          <Chip 
                            label={`${type.count} total`} 
                            size="small" 
                            variant="outlined"
                          />
                        </Stack>
                        <Typography variant="body2" color="text.secondary">
                          {type.percentage}%
                        </Typography>
                      </Stack>
                      <LinearProgress 
                        variant="determinate" 
                        value={type.percentage} 
                        sx={{ 
                          height: 8, 
                          borderRadius: 4,
                          bgcolor: 'grey.200',
                          '& .MuiLinearProgress-bar': {
                            borderRadius: 4,
                            bgcolor: index === 0 ? 'primary.main' : 
                                   index === 1 ? 'success.main' : 'info.main'
                          }
                        }}
                      />
                      <Stack direction="row" justifyContent="space-between" sx={{ mt: 0.5 }}>
                        <Typography variant="caption" color="success.main">
                          ✓ {type.success} successful
                        </Typography>
                        <Typography variant="caption" color="error.main">
                          ✗ {type.failed} failed
                        </Typography>
                      </Stack>
                    </Box>
                  ))}
                </Stack>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                  Recent Export Activity
                </Typography>
                <List dense>
                  {recentActivity.map((activity, index) => (
                    <React.Fragment key={index}>
                      <ListItem>
                        <ListItemIcon>
                          <Avatar sx={{ 
                            bgcolor: `${getStatusColor(activity.status)}.main`,
                            width: 32,
                            height: 32
                          }}>
                            {getActivityIcon(activity.type)}
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {activity.action} ({activity.count} records)
                            </Typography>
                          }
                          secondary={
                            <Stack spacing={0.5}>
                              <Typography variant="caption" color="text.secondary">
                                {activity.time} • by {activity.user}
                              </Typography>
                              {activity.error && (
                                <Typography variant="caption" color="error.main">
                                  Error: {activity.error}
                                </Typography>
                              )}
                            </Stack>
                          }
                        />
                        <Chip 
                          label={activity.status} 
                          size="small" 
                          color={getStatusColor(activity.status)}
                        />
                      </ListItem>
                      {index < recentActivity.length - 1 && <Divider variant="inset" component="li" />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* System Status */}
        <Grid item xs={12}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    System Status & Connectivity
                  </Typography>
                  <Stack direction="row" spacing={1}>
                    <Typography variant="body2" color="text.secondary">
                      Last run: {formatDateTime(dashboardMetrics.lastRunTime)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Next scheduled: {formatDateTime(dashboardMetrics.nextScheduledRun)}
                    </Typography>
                  </Stack>
                </Stack>
                <Grid container spacing={2}>
                  {systemStatus.map((system, index) => (
                    <Grid item xs={12} sm={6} md={3} key={index}>
                      <Box sx={{ 
                        p: 2, 
                        border: 1, 
                        borderColor: `${getStatusColor(system.status)}.main`,
                        borderRadius: 1,
                        bgcolor: `${getStatusColor(system.status)}.light`,
                        opacity: 0.1
                      }}>
                        <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1 }}>
                          <CheckCircle 
                            color={getStatusColor(system.status)} 
                            fontSize="small" 
                          />
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {system.system}
                          </Typography>
                        </Stack>
                        <Typography variant="caption" color="text.secondary">
                          Last sync: {formatDateTime(system.lastSync)}
                        </Typography>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>
    </Box>
  );
};

export default IntegrationsDashboard;
