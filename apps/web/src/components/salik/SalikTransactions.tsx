import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  Button,
  Stack,
  Chip,
  IconButton,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  Avatar,
  Menu,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Search,
  FilterList,
  CheckCircle,
  Error,
  Schedule,
  MoreVert,
  Toll,
  DirectionsCar,
  Receipt,
  RestartAlt,
  Visibility,
  Edit,
  PostAdd,
  ExpandMore,
  Warning
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface SalikTransactionsProps {
  token: string;
  userRoles: string[];
}

interface SalikTransaction {
  id: string;
  tag: string;
  plateNumber: string;
  amount: number;
  incurredAt: string;
  status: 'pending' | 'validated' | 'posted' | 'error' | 'unmapped';
  agreementId?: string;
  vehicleId?: string;
  errorMessage?: string;
  validatedAt?: string;
  postedAt?: string;
  validatedBy?: string;
  postedBy?: string;
  retryCount: number;
  maxRetries: number;
  createdAt: string;
  updatedAt: string;
}

const SalikTransactions: React.FC<SalikTransactionsProps> = ({ token, userRoles = [] }) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('pending');
  const [dateFilter, setDateFilter] = useState({ from: '', to: '' });
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTransaction, setSelectedTransaction] = useState<SalikTransaction | null>(null);
  const [errorDialogOpen, setErrorDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });
  const [expandedTransaction, setExpandedTransaction] = useState<string | null>(null);

  // Mock data - in real implementation, this would come from API
  const [transactions, setTransactions] = useState<SalikTransaction[]>([
    {
      id: 'SAL-2024-001',
      tag: 'T12345',
      plateNumber: 'P-1001',
      amount: 12.5,
      incurredAt: '2024-01-28T08:00:00Z',
      status: 'pending',
      retryCount: 0,
      maxRetries: 3,
      createdAt: '2024-01-28T10:30:00Z',
      updatedAt: '2024-01-28T10:30:00Z'
    },
    {
      id: 'SAL-2024-002',
      tag: 'T12346',
      plateNumber: 'P-1002',
      amount: 8.0,
      incurredAt: '2024-01-28T09:15:00Z',
      status: 'validated',
      agreementId: 'AGR-2024-001',
      vehicleId: 'VEH-001',
      validatedAt: '2024-01-28T10:45:00Z',
      validatedBy: 'admin',
      retryCount: 0,
      maxRetries: 3,
      createdAt: '2024-01-28T10:30:00Z',
      updatedAt: '2024-01-28T10:45:00Z'
    },
    {
      id: 'SAL-2024-003',
      tag: 'T12347',
      plateNumber: 'P-1003',
      amount: 15.0,
      incurredAt: '2024-01-28T07:30:00Z',
      status: 'posted',
      agreementId: 'AGR-2024-002',
      vehicleId: 'VEH-002',
      validatedAt: '2024-01-28T09:00:00Z',
      validatedBy: 'admin',
      postedAt: '2024-01-28T11:00:00Z',
      postedBy: 'finance_user',
      retryCount: 0,
      maxRetries: 3,
      createdAt: '2024-01-28T09:00:00Z',
      updatedAt: '2024-01-28T11:00:00Z'
    },
    {
      id: 'SAL-2024-004',
      tag: 'T12348',
      plateNumber: 'P-9999',
      amount: 10.0,
      incurredAt: '2024-01-28T06:45:00Z',
      status: 'error',
      errorMessage: 'Vehicle plate P-9999 not found in fleet database',
      retryCount: 2,
      maxRetries: 3,
      createdAt: '2024-01-28T08:00:00Z',
      updatedAt: '2024-01-28T08:30:00Z'
    },
    {
      id: 'SAL-2024-005',
      tag: 'T12349',
      plateNumber: 'P-1004',
      amount: 6.5,
      incurredAt: '2024-01-28T05:20:00Z',
      status: 'unmapped',
      errorMessage: 'No active agreement found for vehicle P-1004 at incident time',
      retryCount: 1,
      maxRetries: 3,
      createdAt: '2024-01-28T07:00:00Z',
      updatedAt: '2024-01-28T07:15:00Z'
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'posted': return 'success';
      case 'validated': return 'info';
      case 'error': case 'unmapped': return 'error';
      case 'pending': return 'warning';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'posted': return <CheckCircle />;
      case 'validated': return <Schedule />;
      case 'error': case 'unmapped': return <Error />;
      case 'pending': return <Schedule />;
      default: return <Schedule />;
    }
  };

  const formatDateTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('en-AE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: 'AED'
    }).format(amount);
  };

  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = 
      transaction.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.tag.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.plateNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.agreementId?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || transaction.status === statusFilter;
    
    const matchesDateRange = 
      (!dateFilter.from || transaction.incurredAt >= dateFilter.from) &&
      (!dateFilter.to || transaction.incurredAt <= dateFilter.to);

    return matchesSearch && matchesStatus && matchesDateRange;
  });

  const paginatedTransactions = filteredTransactions.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, transaction: SalikTransaction) => {
    setAnchorEl(event.currentTarget);
    setSelectedTransaction(transaction);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedTransaction(null);
  };

  const handleValidateTransactions = async () => {
    setLoading(true);
    try {
      // API call to validate pending transactions
      console.log('Validating pending transactions...');
      
      // Simulate API response
      setTimeout(() => {
        setSnackbar({
          open: true,
          message: 'Validation process started for pending transactions',
          severity: 'success'
        });
        setLoading(false);
        // Refresh data
        refreshData();
      }, 2000);
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to start validation process',
        severity: 'error'
      });
      setLoading(false);
    }
  };

  const handlePostTransactions = async () => {
    setLoading(true);
    try {
      // API call to post validated transactions to billing
      console.log('Posting validated transactions to billing...');
      
      // Simulate API response
      setTimeout(() => {
        setSnackbar({
          open: true,
          message: 'Posting process started for validated transactions',
          severity: 'success'
        });
        setLoading(false);
        // Refresh data
        refreshData();
      }, 2000);
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to start posting process',
        severity: 'error'
      });
      setLoading(false);
    }
  };

  const handleRetryTransaction = async (transactionId: string) => {
    try {
      // API call to retry specific transaction
      console.log('Retrying transaction:', transactionId);
      
      setSnackbar({
        open: true,
        message: 'Transaction retry initiated',
        severity: 'success'
      });
      handleMenuClose();
      refreshData();
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to retry transaction',
        severity: 'error'
      });
    }
  };

  const handleViewError = () => {
    setErrorDialogOpen(true);
    handleMenuClose();
  };

  const refreshData = () => {
    // In real implementation, this would fetch fresh data from API
    console.log('Refreshing transaction data...');
  };

  const pendingCount = transactions.filter(t => t.status === 'pending').length;
  const validatedCount = transactions.filter(t => t.status === 'validated').length;
  const errorCount = transactions.filter(t => t.status === 'error' || t.status === 'unmapped').length;

  return (
    <Box>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
          <Typography variant="h5" sx={{ fontWeight: 600 }}>
            Manage Salik Transactions
          </Typography>
          <Stack direction="row" spacing={2}>
            <Button
              variant="outlined"
              startIcon={<CheckCircle />}
              onClick={handleValidateTransactions}
              disabled={loading || pendingCount === 0}
            >
              Validate ({pendingCount})
            </Button>
            <Button
              variant="contained"
              startIcon={loading ? <CircularProgress size={16} /> : <PostAdd />}
              onClick={handlePostTransactions}
              disabled={loading || validatedCount === 0}
            >
              {loading ? 'Posting...' : `Post to Billing (${validatedCount})`}
            </Button>
          </Stack>
        </Stack>

        {/* Quick Stats */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main" sx={{ fontWeight: 600 }}>
                {pendingCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Pending Validation
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="info.main" sx={{ fontWeight: 600 }}>
                {validatedCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Ready to Post
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="error.main" sx={{ fontWeight: 600 }}>
                {errorCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Errors/Unmapped
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="success.main" sx={{ fontWeight: 600 }}>
                {transactions.filter(t => t.status === 'posted').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Posted to Billing
              </Typography>
            </Paper>
          </Grid>
        </Grid>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Search & Filters
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder="Search by ID, tag, plate, or agreement..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    label="Status"
                  >
                    <MenuItem value="all">All Statuses</MenuItem>
                    <MenuItem value="pending">Pending</MenuItem>
                    <MenuItem value="validated">Validated</MenuItem>
                    <MenuItem value="posted">Posted</MenuItem>
                    <MenuItem value="error">Error</MenuItem>
                    <MenuItem value="unmapped">Unmapped</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  fullWidth
                  label="From Date"
                  type="date"
                  value={dateFilter.from}
                  onChange={(e) => setDateFilter({ ...dateFilter, from: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  fullWidth
                  label="To Date"
                  type="date"
                  value={dateFilter.to}
                  onChange={(e) => setDateFilter({ ...dateFilter, to: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <Button
                  variant="outlined"
                  startIcon={<FilterList />}
                  fullWidth
                  sx={{ height: '100%' }}
                  onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('pending');
                    setDateFilter({ from: '', to: '' });
                  }}
                >
                  Clear Filters
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Results Summary */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="body1">
            Showing {paginatedTransactions.length} of {filteredTransactions.length} transactions
          </Typography>
        </Paper>

        {/* Transactions Table */}
        <Card>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Transaction ID</TableCell>
                  <TableCell>Tag ID</TableCell>
                  <TableCell>Plate Number</TableCell>
                  <TableCell align="right">Amount</TableCell>
                  <TableCell>Incurred At</TableCell>
                  <TableCell align="center">Status</TableCell>
                  <TableCell>Agreement</TableCell>
                  <TableCell align="center">Retries</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedTransactions.map((transaction) => (
                  <React.Fragment key={transaction.id}>
                    <TableRow hover>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {transaction.id}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Avatar sx={{
                            bgcolor: 'warning.main',
                            width: 32,
                            height: 32
                          }}>
                            <Toll />
                          </Avatar>
                          <Typography variant="body2">
                            {transaction.tag}
                          </Typography>
                        </Stack>
                      </TableCell>
                      <TableCell>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <DirectionsCar fontSize="small" color="action" />
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {transaction.plateNumber}
                          </Typography>
                        </Stack>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {formatCurrency(transaction.amount)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDateTime(transaction.incurredAt)}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          label={transaction.status.replace('_', ' ')}
                          size="small"
                          color={getStatusColor(transaction.status)}
                          icon={getStatusIcon(transaction.status)}
                          sx={{ textTransform: 'capitalize' }}
                        />
                      </TableCell>
                      <TableCell>
                        {transaction.agreementId ? (
                          <Stack>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {transaction.agreementId}
                            </Typography>
                            {transaction.vehicleId && (
                              <Typography variant="caption" color="text.secondary">
                                Vehicle: {transaction.vehicleId}
                              </Typography>
                            )}
                          </Stack>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            Not mapped
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell align="center">
                        <Stack alignItems="center">
                          <Typography variant="body2">
                            {transaction.retryCount}/{transaction.maxRetries}
                          </Typography>
                          {transaction.retryCount > 0 && (
                            <Typography variant="caption" color="warning.main">
                              Retried
                            </Typography>
                          )}
                        </Stack>
                      </TableCell>
                      <TableCell align="center">
                        <Stack direction="row" spacing={1}>
                          {transaction.errorMessage && (
                            <Tooltip title="View error details">
                              <IconButton
                                size="small"
                                onClick={() => setExpandedTransaction(
                                  expandedTransaction === transaction.id ? null : transaction.id
                                )}
                              >
                                <ExpandMore
                                  sx={{
                                    transform: expandedTransaction === transaction.id ? 'rotate(180deg)' : 'rotate(0deg)',
                                    transition: 'transform 0.3s'
                                  }}
                                />
                              </IconButton>
                            </Tooltip>
                          )}
                          <Tooltip title="More actions">
                            <IconButton
                              size="small"
                              onClick={(e) => handleMenuOpen(e, transaction)}
                            >
                              <MoreVert />
                            </IconButton>
                          </Tooltip>
                        </Stack>
                      </TableCell>
                    </TableRow>

                    {/* Expandable Error Details Row */}
                    {expandedTransaction === transaction.id && transaction.errorMessage && (
                      <TableRow>
                        <TableCell colSpan={9} sx={{ py: 0 }}>
                          <Accordion expanded={true} sx={{ boxShadow: 'none' }}>
                            <AccordionDetails sx={{ pt: 2 }}>
                              <Alert severity="error" sx={{ mb: 2 }}>
                                <Typography variant="body2">
                                  <strong>Error Details:</strong> {transaction.errorMessage}
                                </Typography>
                              </Alert>
                              <Grid container spacing={2}>
                                <Grid item xs={12} md={6}>
                                  <Typography variant="subtitle2" gutterBottom>
                                    Transaction Details
                                  </Typography>
                                  <Stack spacing={1}>
                                    <Typography variant="body2">
                                      <strong>Created:</strong> {formatDateTime(transaction.createdAt)}
                                    </Typography>
                                    <Typography variant="body2">
                                      <strong>Last Updated:</strong> {formatDateTime(transaction.updatedAt)}
                                    </Typography>
                                    <Typography variant="body2">
                                      <strong>Retry Count:</strong> {transaction.retryCount}/{transaction.maxRetries}
                                    </Typography>
                                  </Stack>
                                </Grid>
                                <Grid item xs={12} md={6}>
                                  <Typography variant="subtitle2" gutterBottom>
                                    Possible Solutions
                                  </Typography>
                                  <Stack spacing={1}>
                                    {transaction.status === 'unmapped' && (
                                      <Typography variant="body2" color="text.secondary">
                                        • Check if vehicle has an active agreement at incident time
                                      </Typography>
                                    )}
                                    {transaction.status === 'error' && (
                                      <Typography variant="body2" color="text.secondary">
                                        • Verify vehicle plate exists in fleet database
                                      </Typography>
                                    )}
                                    <Typography variant="body2" color="text.secondary">
                                      • Contact support if issue persists
                                    </Typography>
                                  </Stack>
                                </Grid>
                              </Grid>
                            </AccordionDetails>
                          </Accordion>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50]}
            component="div"
            count={filteredTransactions.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            onRowsPerPageChange={(e) => {
              setRowsPerPage(parseInt(e.target.value, 10));
              setPage(0);
            }}
          />
        </Card>

        {/* Actions Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          {selectedTransaction?.status === 'validated' && (
            <MenuItem onClick={() => console.log('Post to billing')}>
              <ListItemIcon>
                <PostAdd fontSize="small" />
              </ListItemIcon>
              <ListItemText>Post to Billing</ListItemText>
            </MenuItem>
          )}
          {(selectedTransaction?.status === 'error' || selectedTransaction?.status === 'unmapped') &&
           selectedTransaction.retryCount < selectedTransaction.maxRetries && (
            <MenuItem onClick={() => selectedTransaction && handleRetryTransaction(selectedTransaction.id)}>
              <ListItemIcon>
                <RestartAlt fontSize="small" />
              </ListItemIcon>
              <ListItemText>Retry Transaction</ListItemText>
            </MenuItem>
          )}
          {selectedTransaction?.errorMessage && (
            <MenuItem onClick={handleViewError}>
              <ListItemIcon>
                <Visibility fontSize="small" />
              </ListItemIcon>
              <ListItemText>View Error Details</ListItemText>
            </MenuItem>
          )}
          <MenuItem onClick={() => console.log('Edit transaction')}>
            <ListItemIcon>
              <Edit fontSize="small" />
            </ListItemIcon>
            <ListItemText>Edit Transaction</ListItemText>
          </MenuItem>
        </Menu>

        {/* Error Dialog */}
        <Dialog open={errorDialogOpen} onClose={() => setErrorDialogOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>Transaction Error Details</DialogTitle>
          <DialogContent>
            {selectedTransaction && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Transaction: {selectedTransaction.id}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Tag: {selectedTransaction.tag} | Plate: {selectedTransaction.plateNumber} | Amount: {formatCurrency(selectedTransaction.amount)}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Incurred: {formatDateTime(selectedTransaction.incurredAt)}
                </Typography>

                <Alert severity="error" sx={{ mt: 2 }}>
                  <Typography variant="body2">
                    {selectedTransaction.errorMessage}
                  </Typography>
                </Alert>

                <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                  Retry attempts: {selectedTransaction.retryCount}/{selectedTransaction.maxRetries}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Last updated: {formatDateTime(selectedTransaction.updatedAt)}
                </Typography>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setErrorDialogOpen(false)}>Close</Button>
            {selectedTransaction?.retryCount < selectedTransaction?.maxRetries && (
              <Button
                variant="contained"
                onClick={() => {
                  if (selectedTransaction) {
                    handleRetryTransaction(selectedTransaction.id);
                    setErrorDialogOpen(false);
                  }
                }}
              >
                Retry Transaction
              </Button>
            )}
          </DialogActions>
        </Dialog>

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          <Alert
            onClose={() => setSnackbar({ ...snackbar, open: false })}
            severity={snackbar.severity}
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </motion.div>
    </Box>
  );
};

export default SalikTransactions;
