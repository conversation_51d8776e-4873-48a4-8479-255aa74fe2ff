import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  Button,
  Stack,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  Avatar,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Search,
  FilterList,
  Download,
  Toll,
  DirectionsCar,
  CheckCircle,
  Error,
  Schedule,
  Person,
  CalendarToday,
  Receipt
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface SalikHistoryProps {
  token: string;
  userRoles: string[];
}

interface SalikHistoryRecord {
  id: string;
  tag: string;
  plateNumber: string;
  amount: number;
  incurredAt: string;
  status: 'posted' | 'failed';
  agreementId: string;
  vehicleId: string;
  customerId?: string;
  customerName?: string;
  postedAt: string;
  postedBy: string;
  invoiceId?: string;
  receiptId?: string;
  errorMessage?: string;
  retryCount: number;
  processingTime: number;
  createdAt: string;
  completedAt: string;
}

const SalikHistory: React.FC<SalikHistoryProps> = ({ token, userRoles = [] }) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateRange, setDateRange] = useState({ from: '', to: '' });
  const isCustomer = userRoles?.includes('customer') || false;

  // Mock data - in real implementation, this would come from API
  const historyRecords: SalikHistoryRecord[] = [
    {
      id: 'SAL-2024-001',
      tag: 'T12345',
      plateNumber: 'P-1001',
      amount: 12.5,
      incurredAt: '2024-01-28T08:00:00Z',
      status: 'posted',
      agreementId: 'AGR-2024-001',
      vehicleId: 'VEH-001',
      customerId: 'CUST-001',
      customerName: 'Ahmed Al Mansouri',
      postedAt: '2024-01-28T11:00:00Z',
      postedBy: 'finance_user',
      invoiceId: 'INV-2024-001',
      retryCount: 0,
      processingTime: 180,
      createdAt: '2024-01-28T10:30:00Z',
      completedAt: '2024-01-28T11:00:00Z'
    },
    {
      id: 'SAL-2024-002',
      tag: 'T12346',
      plateNumber: 'P-1002',
      amount: 8.0,
      incurredAt: '2024-01-28T09:15:00Z',
      status: 'posted',
      agreementId: 'AGR-2024-002',
      vehicleId: 'VEH-002',
      customerId: 'CUST-002',
      customerName: 'Fatima Al Zahra',
      postedAt: '2024-01-28T12:30:00Z',
      postedBy: 'admin',
      receiptId: 'REC-2024-001',
      retryCount: 0,
      processingTime: 120,
      createdAt: '2024-01-28T10:45:00Z',
      completedAt: '2024-01-28T12:30:00Z'
    },
    {
      id: 'SAL-2024-003',
      tag: 'T12347',
      plateNumber: 'P-1003',
      amount: 15.0,
      incurredAt: '2024-01-27T16:30:00Z',
      status: 'posted',
      agreementId: 'AGR-2024-003',
      vehicleId: 'VEH-003',
      customerId: 'CUST-003',
      customerName: 'Mohammed Al Rashid',
      postedAt: '2024-01-28T09:00:00Z',
      postedBy: 'finance_user',
      invoiceId: 'INV-2024-002',
      retryCount: 1,
      processingTime: 300,
      createdAt: '2024-01-27T18:00:00Z',
      completedAt: '2024-01-28T09:00:00Z'
    },
    {
      id: 'SAL-2024-004',
      tag: 'T12348',
      plateNumber: 'P-1004',
      amount: 10.0,
      incurredAt: '2024-01-27T14:20:00Z',
      status: 'failed',
      agreementId: 'AGR-2024-004',
      vehicleId: 'VEH-004',
      customerId: 'CUST-004',
      customerName: 'Sara Al Maktoum',
      postedAt: '2024-01-27T16:45:00Z',
      postedBy: 'system',
      errorMessage: 'Billing system unavailable during posting',
      retryCount: 3,
      processingTime: 900,
      createdAt: '2024-01-27T15:00:00Z',
      completedAt: '2024-01-27T16:45:00Z'
    },
    {
      id: 'SAL-2024-005',
      tag: 'T12349',
      plateNumber: 'P-1005',
      amount: 6.5,
      incurredAt: '2024-01-27T11:10:00Z',
      status: 'posted',
      agreementId: 'AGR-2024-005',
      vehicleId: 'VEH-005',
      customerId: 'CUST-005',
      customerName: 'Omar Al Nahyan',
      postedAt: '2024-01-27T14:20:00Z',
      postedBy: 'admin',
      invoiceId: 'INV-2024-003',
      retryCount: 0,
      processingTime: 90,
      createdAt: '2024-01-27T12:00:00Z',
      completedAt: '2024-01-27T14:20:00Z'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'posted': return 'success';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const formatDateTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('en-AE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: 'AED'
    }).format(amount);
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return minutes > 0 ? `${minutes}m ${remainingSeconds}s` : `${remainingSeconds}s`;
  };

  const filteredRecords = historyRecords.filter(record => {
    // For customers, only show their own records
    if (isCustomer) {
      // In real implementation, you would filter by the current customer's ID
      // For demo, we'll show all records
    }

    const matchesSearch = 
      record.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.tag.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.plateNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.agreementId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.customerName?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || record.status === statusFilter;
    
    const matchesDateRange = 
      (!dateRange.from || record.incurredAt >= dateRange.from) &&
      (!dateRange.to || record.incurredAt <= dateRange.to);

    return matchesSearch && matchesStatus && matchesDateRange;
  });

  const paginatedRecords = filteredRecords.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  const handleExportHistory = () => {
    console.log('Exporting history data...', filteredRecords);
  };

  const successfulTransactions = filteredRecords.filter(r => r.status === 'posted').length;
  const failedTransactions = filteredRecords.filter(r => r.status === 'failed').length;
  const totalAmount = filteredRecords.reduce((sum, r) => r.status === 'posted' ? sum + r.amount : sum, 0);

  return (
    <Box>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          {isCustomer ? 'Your Salik Transaction History' : 'Salik Transaction History & Audit Trail'}
        </Typography>

        {/* Summary Stats */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="success.main" sx={{ fontWeight: 600 }}>
                {successfulTransactions}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Posted Transactions
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="error.main" sx={{ fontWeight: 600 }}>
                {failedTransactions}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Failed Transactions
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="primary.main" sx={{ fontWeight: 600 }}>
                {formatCurrency(totalAmount)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Amount Posted
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="info.main" sx={{ fontWeight: 600 }}>
                {filteredRecords.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Records
              </Typography>
            </Paper>
          </Grid>
        </Grid>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Search & Filters
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder={isCustomer ? "Search your transactions..." : "Search by ID, tag, plate, agreement, or customer..."}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    label="Status"
                  >
                    <MenuItem value="all">All Statuses</MenuItem>
                    <MenuItem value="posted">Posted</MenuItem>
                    <MenuItem value="failed">Failed</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  fullWidth
                  label="From Date"
                  type="date"
                  value={dateRange.from}
                  onChange={(e) => setDateRange({ ...dateRange, from: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  fullWidth
                  label="To Date"
                  type="date"
                  value={dateRange.to}
                  onChange={(e) => setDateRange({ ...dateRange, to: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <Stack direction="row" spacing={1} sx={{ height: '100%' }}>
                  <Button
                    variant="outlined"
                    startIcon={<FilterList />}
                    onClick={() => {
                      setSearchTerm('');
                      setStatusFilter('all');
                      setDateRange({ from: '', to: '' });
                    }}
                    sx={{ flex: 1 }}
                  >
                    Clear
                  </Button>
                  {!isCustomer && (
                    <Button
                      variant="outlined"
                      startIcon={<Download />}
                      onClick={handleExportHistory}
                      sx={{ flex: 1 }}
                    >
                      Export
                    </Button>
                  )}
                </Stack>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Results Summary */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="body1">
            Showing {paginatedRecords.length} of {filteredRecords.length} transaction records
          </Typography>
        </Paper>

        {/* History Table */}
        <Card>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Transaction ID</TableCell>
                  <TableCell>Tag & Plate</TableCell>
                  <TableCell align="right">Amount</TableCell>
                  <TableCell>Incurred At</TableCell>
                  <TableCell align="center">Status</TableCell>
                  <TableCell>Agreement</TableCell>
                  {!isCustomer && <TableCell>Customer</TableCell>}
                  <TableCell>Posted</TableCell>
                  <TableCell>Document</TableCell>
                  <TableCell align="center">Processing</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedRecords.map((record) => (
                  <TableRow key={record.id} hover>
                    <TableCell>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {record.id}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Stack spacing={1}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Avatar sx={{
                            bgcolor: 'warning.main',
                            width: 24,
                            height: 24
                          }}>
                            <Toll fontSize="small" />
                          </Avatar>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {record.tag}
                          </Typography>
                        </Stack>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <DirectionsCar fontSize="small" color="action" />
                          <Typography variant="body2">
                            {record.plateNumber}
                          </Typography>
                        </Stack>
                      </Stack>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {formatCurrency(record.amount)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <CalendarToday fontSize="small" color="action" />
                        <Typography variant="body2">
                          {formatDateTime(record.incurredAt)}
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell align="center">
                      <Chip
                        label={record.status}
                        size="small"
                        color={getStatusColor(record.status)}
                        icon={record.status === 'posted' ? <CheckCircle /> : <Error />}
                      />
                      {record.retryCount > 0 && (
                        <Typography variant="caption" display="block" color="text.secondary">
                          {record.retryCount} retries
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Stack>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {record.agreementId}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Vehicle: {record.vehicleId}
                        </Typography>
                      </Stack>
                    </TableCell>
                    {!isCustomer && (
                      <TableCell>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Person fontSize="small" color="action" />
                          <Stack>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {record.customerName}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {record.customerId}
                            </Typography>
                          </Stack>
                        </Stack>
                      </TableCell>
                    )}
                    <TableCell>
                      <Stack>
                        <Typography variant="body2">
                          {formatDateTime(record.postedAt)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          by {record.postedBy}
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      {record.invoiceId || record.receiptId ? (
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Receipt fontSize="small" color="primary" />
                          <Stack>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {record.invoiceId || record.receiptId}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {record.invoiceId ? 'Invoice' : 'Receipt'}
                            </Typography>
                          </Stack>
                        </Stack>
                      ) : record.errorMessage ? (
                        <Stack>
                          <Typography variant="body2" color="error.main">
                            Failed
                          </Typography>
                          <Tooltip title={record.errorMessage}>
                            <Typography variant="caption" color="error.main" sx={{ cursor: 'help' }}>
                              View error
                            </Typography>
                          </Tooltip>
                        </Stack>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          No document
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell align="center">
                      <Stack alignItems="center">
                        <Typography variant="body2">
                          {formatDuration(record.processingTime)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          processing time
                        </Typography>
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50]}
            component="div"
            count={filteredRecords.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            onRowsPerPageChange={(e) => {
              setRowsPerPage(parseInt(e.target.value, 10));
              setPage(0);
            }}
          />
        </Card>
      </motion.div>
    </Box>
  );
};

export default SalikHistory;
