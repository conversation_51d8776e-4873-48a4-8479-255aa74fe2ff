import React from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ack,
  Avatar,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
  useTheme,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Button,
  Paper
} from '@mui/material';
import {
  Toll,
  CheckCircle,
  Error,
  Schedule,
  Refresh,
  TrendingUp,
  TrendingDown,
  Upload,
  Download,
  Warning,
  Assessment,
  DirectionsCar,
  Receipt,
  AccountBalance
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface SalikDashboardProps {
  token: string;
  userRoles: string[];
  alerts: {
    pendingTransactions: number;
    errorTransactions: number;
    unmappedTransactions: number;
  };
}

const SalikDashboard: React.FC<SalikDashboardProps> = ({ token, userRoles = [], alerts }) => {
  const theme = useTheme();
  const isAdmin = userRoles?.includes('admin') || false;
  const isCustomer = userRoles?.includes('customer') || false;

  // Mock data - in real implementation, this would come from API
  const dashboardMetrics = {
    totalTransactions: 2847,
    validatedTransactions: 2654,
    postedTransactions: 2598,
    errorTransactions: 56,
    totalAmount: 45678.50,
    validationRate: 93.2,
    avgProcessingTime: 1.8,
    lastImportTime: '2024-01-28T14:30:00Z',
    nextScheduledImport: '2024-01-29T06:00:00Z'
  };

  const transactionTypes = [
    { type: 'Highway Tolls', count: 1856, amount: 28945.50, percentage: 65.2 },
    { type: 'Bridge Tolls', count: 742, amount: 12456.75, percentage: 26.1 },
    { type: 'Tunnel Tolls', count: 249, amount: 4276.25, percentage: 8.7 }
  ];

  const recentActivity = [
    { type: 'import', action: 'Bulk import completed', count: 156, status: 'success', time: '2 hours ago', user: 'admin' },
    { type: 'validation', action: 'Validation failed', count: 8, status: 'error', time: '3 hours ago', user: 'system', error: 'Unmapped vehicle plates' },
    { type: 'posting', action: 'Posted to billing', count: 89, status: 'success', time: '4 hours ago', user: 'finance_user' },
    { type: 'retry', action: 'Retry processing', count: 12, status: 'warning', time: '6 hours ago', user: 'operations' }
  ];

  const systemStatus = [
    { system: 'Salik API', status: 'connected', lastSync: '2024-01-28T14:30:00Z' },
    { system: 'Vehicle Mapping', status: 'connected', lastSync: '2024-01-28T14:29:00Z' },
    { system: 'Billing Integration', status: 'connected', lastSync: '2024-01-28T14:25:00Z' },
    { system: 'Audit Logger', status: 'connected', lastSync: '2024-01-28T14:30:00Z' }
  ];

  const formatDateTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('en-AE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: 'AED'
    }).format(amount);
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'import': return <Upload />;
      case 'validation': return <CheckCircle />;
      case 'posting': return <Receipt />;
      case 'retry': return <Refresh />;
      default: return <Toll />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': case 'connected': return 'success';
      case 'error': case 'failed': return 'error';
      case 'warning': case 'pending': return 'warning';
      case 'processing': return 'info';
      default: return 'default';
    }
  };

  const StatCard = ({ title, value, subtitle, icon, color, trend }: any) => (
    <Card>
      <CardContent>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Avatar sx={{ bgcolor: `${color}.main`, width: 56, height: 56 }}>
            {icon}
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h4" sx={{ fontWeight: 600 }}>
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="caption" color="text.secondary">
                {subtitle}
              </Typography>
            )}
            {trend && (
              <Stack direction="row" alignItems="center" spacing={1} sx={{ mt: 1 }}>
                {trend > 0 ? <TrendingUp color="success" fontSize="small" /> : <TrendingDown color="error" fontSize="small" />}
                <Typography 
                  variant="caption" 
                  color={trend > 0 ? 'success.main' : 'error.main'}
                  sx={{ fontWeight: 500 }}
                >
                  {Math.abs(trend)}% from last week
                </Typography>
              </Stack>
            )}
          </Box>
        </Stack>
      </CardContent>
    </Card>
  );

  return (
    <Box>
      {/* Alerts Section */}
      {(alerts.errorTransactions > 0 || alerts.unmappedTransactions > 0) && !isCustomer && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Alert 
            severity="warning" 
            icon={<Warning />}
            sx={{ mb: 3 }}
            action={
              <Button size="small" color="inherit" startIcon={<Assessment />}>
                Review Transactions
              </Button>
            }
          >
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              Salik Alerts: {alerts.errorTransactions} failed validations, {alerts.unmappedTransactions} unmapped vehicles, 
              {alerts.pendingTransactions} pending processing
            </Typography>
          </Alert>
        </motion.div>
      )}

      {/* Key Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          {isCustomer ? 'Your Salik Charges Overview' : 'Salik Transaction Performance'}
        </Typography>
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title={isCustomer ? "Your Total Charges" : "Total Transactions"}
              value={isCustomer ? formatCurrency(dashboardMetrics.totalAmount) : dashboardMetrics.totalTransactions}
              subtitle={isCustomer ? "All time" : "All time"}
              icon={<Toll />}
              color="primary"
              trend={12.5}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title={isCustomer ? "This Month" : "Validation Rate"}
              value={isCustomer ? formatCurrency(5678.25) : `${dashboardMetrics.validationRate}%`}
              subtitle={isCustomer ? "Current month charges" : "Last 30 days"}
              icon={<CheckCircle />}
              color="success"
              trend={2.1}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title={isCustomer ? "Pending Charges" : "Error Transactions"}
              value={isCustomer ? formatCurrency(234.50) : dashboardMetrics.errorTransactions}
              subtitle={isCustomer ? "Awaiting processing" : "Requiring attention"}
              icon={<Error />}
              color="error"
              trend={-15.3}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title={isCustomer ? "Avg per Trip" : "Avg Processing"}
              value={isCustomer ? formatCurrency(12.50) : `${dashboardMetrics.avgProcessingTime}min`}
              subtitle={isCustomer ? "Per toll transaction" : "Per transaction batch"}
              icon={<Schedule />}
              color="info"
              trend={-8.7}
            />
          </Grid>
        </Grid>
      </motion.div>

      <Grid container spacing={3}>
        {/* Transaction Types Breakdown */}
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {isCustomer ? 'Your Charges by Type' : 'Transaction Types Distribution'}
                  </Typography>
                  <Tooltip title="Refresh data">
                    <IconButton size="small">
                      <Refresh />
                    </IconButton>
                  </Tooltip>
                </Stack>
                <Stack spacing={3}>
                  {transactionTypes.map((type, index) => (
                    <Box key={index}>
                      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {type.type}
                          </Typography>
                          <Chip 
                            label={`${type.count} transactions`} 
                            size="small" 
                            variant="outlined"
                          />
                        </Stack>
                        <Typography variant="body2" color="text.secondary">
                          {formatCurrency(type.amount)}
                        </Typography>
                      </Stack>
                      <LinearProgress 
                        variant="determinate" 
                        value={type.percentage} 
                        sx={{ 
                          height: 8, 
                          borderRadius: 4,
                          bgcolor: 'grey.200',
                          '& .MuiLinearProgress-bar': {
                            borderRadius: 4,
                            bgcolor: index === 0 ? 'primary.main' : 
                                   index === 1 ? 'success.main' : 'warning.main'
                          }
                        }}
                      />
                      <Stack direction="row" justifyContent="space-between" sx={{ mt: 0.5 }}>
                        <Typography variant="caption" color="text.secondary">
                          {type.percentage}% of total
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {type.count} transactions
                        </Typography>
                      </Stack>
                    </Box>
                  ))}
                </Stack>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                  {isCustomer ? 'Recent Charge Activity' : 'Recent Processing Activity'}
                </Typography>
                <List dense>
                  {recentActivity.map((activity, index) => (
                    <React.Fragment key={index}>
                      <ListItem>
                        <ListItemIcon>
                          <Avatar sx={{ 
                            bgcolor: `${getStatusColor(activity.status)}.main`,
                            width: 32,
                            height: 32
                          }}>
                            {getActivityIcon(activity.type)}
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {activity.action} ({activity.count} records)
                            </Typography>
                          }
                          secondary={
                            <Stack spacing={0.5}>
                              <Typography variant="caption" color="text.secondary">
                                {activity.time} • by {activity.user}
                              </Typography>
                              {activity.error && (
                                <Typography variant="caption" color="error.main">
                                  Error: {activity.error}
                                </Typography>
                              )}
                            </Stack>
                          }
                        />
                        <Chip 
                          label={activity.status} 
                          size="small" 
                          color={getStatusColor(activity.status)}
                        />
                      </ListItem>
                      {index < recentActivity.length - 1 && <Divider variant="inset" component="li" />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* System Status - Admin only */}
        {!isCustomer && (
          <Grid item xs={12}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <Card>
                <CardContent>
                  <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      System Status & Connectivity
                    </Typography>
                    <Stack direction="row" spacing={1}>
                      <Typography variant="body2" color="text.secondary">
                        Last import: {formatDateTime(dashboardMetrics.lastImportTime)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Next scheduled: {formatDateTime(dashboardMetrics.nextScheduledImport)}
                      </Typography>
                    </Stack>
                  </Stack>
                  <Grid container spacing={2}>
                    {systemStatus.map((system, index) => (
                      <Grid item xs={12} sm={6} md={3} key={index}>
                        <Box sx={{ 
                          p: 2, 
                          border: 1, 
                          borderColor: `${getStatusColor(system.status)}.main`,
                          borderRadius: 1,
                          bgcolor: `${getStatusColor(system.status)}.light`,
                          opacity: 0.1
                        }}>
                          <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1 }}>
                            <CheckCircle 
                              color={getStatusColor(system.status)} 
                              fontSize="small" 
                            />
                            <Typography variant="body2" sx={{ fontWeight: 600 }}>
                              {system.system}
                            </Typography>
                          </Stack>
                          <Typography variant="caption" color="text.secondary">
                            Last sync: {formatDateTime(system.lastSync)}
                          </Typography>
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default SalikDashboard;
