import React from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typo<PERSON>,
  Stack,
  Avatar,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
  useTheme
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Receipt,
  Payment,
  AccountBalance,
  Warning,
  CheckCircle,
  Schedule,
  Refresh,
  Download
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface BillingDashboardProps {
  token: string;
  userRoles: string[];
}

const BillingDashboard: React.FC<BillingDashboardProps> = ({ token, userRoles }) => {
  const theme = useTheme();
  const isAdmin = userRoles.includes('admin');

  // Mock data - in real implementation, this would come from API
  const dashboardData = {
    totalRevenue: { value: 125420.50, change: 12.5, period: 'This Month' },
    outstandingAmount: { value: 23150.75, change: -8.2, period: 'Outstanding' },
    invoicesIssued: { value: 156, change: 15.3, period: 'This Month' },
    paymentsReceived: { value: 142, change: 18.7, period: 'This Month' },
    
    recentActivity: [
      { type: 'invoice', id: 'INV-2024-001', customer: 'ABC Corp', amount: 2500.00, status: 'paid', time: '2 hours ago' },
      { type: 'payment', id: 'RCP-2024-045', customer: 'XYZ Ltd', amount: 1800.00, status: 'applied', time: '4 hours ago' },
      { type: 'invoice', id: 'INV-2024-002', customer: 'Tech Solutions', amount: 3200.00, status: 'pending', time: '6 hours ago' },
      { type: 'payment', id: 'RCP-2024-046', customer: 'Global Inc', amount: 950.00, status: 'unapplied', time: '1 day ago' }
    ],

    agingAnalysis: [
      { period: 'Current (0-30 days)', amount: 45200.00, percentage: 65 },
      { period: '31-60 days', amount: 15800.00, percentage: 23 },
      { period: '61-90 days', amount: 6200.00, percentage: 9 },
      { period: '90+ days', amount: 2100.00, percentage: 3 }
    ]
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: 'AED'
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': case 'applied': return 'success';
      case 'pending': case 'unapplied': return 'warning';
      case 'overdue': return 'error';
      default: return 'default';
    }
  };

  const StatCard = ({ title, value, change, icon, color }: any) => (
    <Card>
      <CardContent>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Avatar sx={{ bgcolor: `${color}.main`, width: 56, height: 56 }}>
            {icon}
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h4" sx={{ fontWeight: 600 }}>
              {typeof value === 'number' && title.includes('Revenue') || title.includes('Amount') 
                ? formatCurrency(value) 
                : value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
            <Stack direction="row" alignItems="center" spacing={1} sx={{ mt: 1 }}>
              {change > 0 ? <TrendingUp color="success" fontSize="small" /> : <TrendingDown color="error" fontSize="small" />}
              <Typography 
                variant="caption" 
                color={change > 0 ? 'success.main' : 'error.main'}
                sx={{ fontWeight: 500 }}
              >
                {Math.abs(change)}% {change > 0 ? 'increase' : 'decrease'}
              </Typography>
            </Stack>
          </Box>
        </Stack>
      </CardContent>
    </Card>
  );

  return (
    <Box>
      {/* Key Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          Financial Overview
        </Typography>
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Revenue"
              value={dashboardData.totalRevenue.value}
              change={dashboardData.totalRevenue.change}
              icon={<TrendingUp />}
              color="success"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Outstanding Amount"
              value={dashboardData.outstandingAmount.value}
              change={dashboardData.outstandingAmount.change}
              icon={<Warning />}
              color="warning"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Invoices Issued"
              value={dashboardData.invoicesIssued.value}
              change={dashboardData.invoicesIssued.change}
              icon={<Receipt />}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Payments Received"
              value={dashboardData.paymentsReceived.value}
              change={dashboardData.paymentsReceived.change}
              icon={<Payment />}
              color="info"
            />
          </Grid>
        </Grid>
      </motion.div>

      <Grid container spacing={3}>
        {/* Recent Activity */}
        <Grid item xs={12} md={8}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Recent Activity
                  </Typography>
                  <Tooltip title="Refresh">
                    <IconButton size="small">
                      <Refresh />
                    </IconButton>
                  </Tooltip>
                </Stack>
                <Stack spacing={2}>
                  {dashboardData.recentActivity.map((activity, index) => (
                    <Box key={index}>
                      <Stack direction="row" alignItems="center" spacing={2}>
                        <Avatar sx={{ 
                          bgcolor: activity.type === 'invoice' ? 'primary.main' : 'success.main',
                          width: 40,
                          height: 40
                        }}>
                          {activity.type === 'invoice' ? <Receipt /> : <Payment />}
                        </Avatar>
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {activity.type === 'invoice' ? 'Invoice Created' : 'Payment Received'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {activity.id} • {activity.customer} • {formatCurrency(activity.amount)}
                          </Typography>
                        </Box>
                        <Stack alignItems="flex-end" spacing={1}>
                          <Chip 
                            label={activity.status} 
                            color={getStatusColor(activity.status)} 
                            size="small" 
                          />
                          <Typography variant="caption" color="text.secondary">
                            {activity.time}
                          </Typography>
                        </Stack>
                      </Stack>
                    </Box>
                  ))}
                </Stack>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Aging Analysis */}
        <Grid item xs={12} md={4}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Aging Analysis
                  </Typography>
                  <Tooltip title="Export Report">
                    <IconButton size="small">
                      <Download />
                    </IconButton>
                  </Tooltip>
                </Stack>
                <Stack spacing={3}>
                  {dashboardData.agingAnalysis.map((item, index) => (
                    <Box key={index}>
                      <Stack direction="row" justifyContent="space-between" sx={{ mb: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {item.period}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {formatCurrency(item.amount)}
                        </Typography>
                      </Stack>
                      <LinearProgress 
                        variant="determinate" 
                        value={item.percentage} 
                        sx={{ 
                          height: 8, 
                          borderRadius: 4,
                          bgcolor: 'grey.200',
                          '& .MuiLinearProgress-bar': {
                            borderRadius: 4,
                            bgcolor: index === 0 ? 'success.main' : 
                                   index === 1 ? 'warning.main' : 
                                   index === 2 ? 'error.light' : 'error.main'
                          }
                        }}
                      />
                      <Typography variant="caption" color="text.secondary">
                        {item.percentage}% of total outstanding
                      </Typography>
                    </Box>
                  ))}
                </Stack>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>
    </Box>
  );
};

export default BillingDashboard;
