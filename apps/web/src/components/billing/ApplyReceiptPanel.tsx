import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON>graphy,
  Grid,
  TextField,
  Button,
  Stack,
  Autocomplete,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
  LinearProgress
} from '@mui/material';
import {
  AccountBalance,
  Receipt,
  CheckCircle,
  Warning,
  Info,
  Refresh,
  Preview
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface ApplyReceiptPanelProps {
  token: string;
  userRoles: string[];
}

interface UnappliedReceipt {
  id: string;
  customerId: string;
  customerName: string;
  amount: number;
  appliedAmount: number;
  remainingAmount: number;
  receiptDate: string;
  paymentMethod: string;
  status: 'unapplied' | 'partial' | 'applied';
}

interface PendingInvoice {
  id: string;
  customerId: string;
  customerName: string;
  totalAmount: number;
  outstandingAmount: number;
  dueDate: string;
  status: 'pending' | 'partial' | 'overdue';
  priority: 'high' | 'medium' | 'low';
}

interface Application {
  receiptId: string;
  invoiceId: string;
  amount: number;
}

const ApplyReceiptPanel: React.FC<ApplyReceiptPanelProps> = ({ token, userRoles }) => {
  const [selectedReceipt, setSelectedReceipt] = useState<UnappliedReceipt | null>(null);
  const [applications, setApplications] = useState<Application[]>([]);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Mock data - in real implementation, these would come from API
  const unappliedReceipts: UnappliedReceipt[] = [
    {
      id: 'RCP-2024-001',
      customerId: 'CUST001',
      customerName: 'Ahmed Al Mansouri',
      amount: 2500.00,
      appliedAmount: 0,
      remainingAmount: 2500.00,
      receiptDate: '2024-01-15',
      paymentMethod: 'Bank Transfer',
      status: 'unapplied'
    },
    {
      id: 'RCP-2024-002',
      customerId: 'CUST002',
      customerName: 'Emirates Business Solutions',
      amount: 5000.00,
      appliedAmount: 2000.00,
      remainingAmount: 3000.00,
      receiptDate: '2024-01-18',
      paymentMethod: 'Credit Card',
      status: 'partial'
    },
    {
      id: 'RCP-2024-003',
      customerId: 'CUST001',
      customerName: 'Ahmed Al Mansouri',
      amount: 1200.00,
      appliedAmount: 0,
      remainingAmount: 1200.00,
      receiptDate: '2024-01-20',
      paymentMethod: 'Cash',
      status: 'unapplied'
    }
  ];

  const pendingInvoices: PendingInvoice[] = [
    {
      id: 'INV-2024-001',
      customerId: 'CUST001',
      customerName: 'Ahmed Al Mansouri',
      totalAmount: 2500.00,
      outstandingAmount: 2500.00,
      dueDate: '2024-02-15',
      status: 'pending',
      priority: 'medium'
    },
    {
      id: 'INV-2024-002',
      customerId: 'CUST002',
      customerName: 'Emirates Business Solutions',
      totalAmount: 5200.00,
      outstandingAmount: 3200.00,
      dueDate: '2024-01-20',
      status: 'overdue',
      priority: 'high'
    },
    {
      id: 'INV-2024-003',
      customerId: 'CUST001',
      customerName: 'Ahmed Al Mansouri',
      totalAmount: 1800.00,
      outstandingAmount: 1800.00,
      dueDate: '2024-02-25',
      status: 'pending',
      priority: 'low'
    }
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: 'AED'
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'applied': return 'success';
      case 'partial': return 'warning';
      case 'unapplied': return 'info';
      case 'overdue': return 'error';
      case 'pending': return 'default';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const availableInvoices = selectedReceipt 
    ? pendingInvoices.filter(inv => 
        inv.customerId === selectedReceipt.customerId && 
        inv.outstandingAmount > 0
      )
    : [];

  const totalApplicationAmount = applications.reduce((sum, app) => sum + app.amount, 0);
  const remainingReceiptAmount = selectedReceipt 
    ? selectedReceipt.remainingAmount - totalApplicationAmount 
    : 0;

  const addApplication = (invoiceId: string, amount: number) => {
    const existingIndex = applications.findIndex(app => app.invoiceId === invoiceId);
    
    if (existingIndex >= 0) {
      // Update existing application
      const updated = [...applications];
      updated[existingIndex].amount = amount;
      setApplications(updated);
    } else {
      // Add new application
      setApplications([
        ...applications,
        {
          receiptId: selectedReceipt!.id,
          invoiceId,
          amount
        }
      ]);
    }
  };

  const removeApplication = (invoiceId: string) => {
    setApplications(applications.filter(app => app.invoiceId !== invoiceId));
  };

  const handleApplyReceipts = async () => {
    if (!selectedReceipt || applications.length === 0) return;

    setLoading(true);
    try {
      // API call to apply receipts
      console.log('Applying receipts...', { selectedReceipt, applications });
      // await api.applyReceipts(token, { receiptId: selectedReceipt.id, applications });
    } catch (error) {
      console.error('Error applying receipts:', error);
    } finally {
      setLoading(false);
    }
  };

  const validateApplications = () => {
    const newErrors: Record<string, string> = {};

    if (!selectedReceipt) {
      newErrors.receipt = 'Please select a receipt to apply';
    }

    if (applications.length === 0) {
      newErrors.applications = 'Please add at least one application';
    }

    if (totalApplicationAmount > (selectedReceipt?.remainingAmount || 0)) {
      newErrors.amount = 'Total application amount cannot exceed receipt amount';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  return (
    <Box>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          Apply Receipt to Invoices
        </Typography>

        <Grid container spacing={3}>
          {/* Receipt Selection */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Select Unapplied Receipt
                </Typography>
                <Autocomplete
                  options={unappliedReceipts}
                  getOptionLabel={(option) => 
                    `${option.id} - ${option.customerName} - ${formatCurrency(option.remainingAmount)} remaining`
                  }
                  value={selectedReceipt}
                  onChange={(_, value) => {
                    setSelectedReceipt(value);
                    setApplications([]); // Clear applications when receipt changes
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Receipt"
                      error={!!errors.receipt}
                      helperText={errors.receipt || 'Select a receipt with remaining balance'}
                    />
                  )}
                  renderOption={(props, option) => (
                    <Box component="li" {...props}>
                      <Stack direction="row" justifyContent="space-between" sx={{ width: '100%' }}>
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {option.id}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {option.customerName} • {option.paymentMethod} • {option.receiptDate}
                          </Typography>
                        </Box>
                        <Box textAlign="right">
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {formatCurrency(option.remainingAmount)}
                          </Typography>
                          <Chip 
                            label={option.status} 
                            size="small" 
                            color={getStatusColor(option.status)}
                          />
                        </Box>
                      </Stack>
                    </Box>
                  )}
                />

                {selectedReceipt && (
                  <Alert severity="info" sx={{ mt: 2 }}>
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                      <Box>
                        Receipt {selectedReceipt.id}: Total {formatCurrency(selectedReceipt.amount)}, 
                        Remaining {formatCurrency(selectedReceipt.remainingAmount)}
                      </Box>
                      <Tooltip title="Refresh receipt data">
                        <IconButton size="small">
                          <Refresh />
                        </IconButton>
                      </Tooltip>
                    </Stack>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Available Invoices */}
          {selectedReceipt && (
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Outstanding Invoices for {selectedReceipt.customerName}
                  </Typography>
                  
                  {errors.applications && (
                    <Alert severity="error" sx={{ mb: 2 }}>
                      {errors.applications}
                    </Alert>
                  )}

                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Invoice</TableCell>
                          <TableCell align="right">Outstanding</TableCell>
                          <TableCell align="center">Priority</TableCell>
                          <TableCell align="center">Due Date</TableCell>
                          <TableCell align="right">Apply Amount</TableCell>
                          <TableCell align="center">Action</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {availableInvoices.map((invoice) => {
                          const application = applications.find(app => app.invoiceId === invoice.id);
                          const maxApplicable = Math.min(
                            invoice.outstandingAmount,
                            remainingReceiptAmount + (application?.amount || 0)
                          );

                          return (
                            <TableRow key={invoice.id}>
                              <TableCell>
                                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                  {invoice.id}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  Total: {formatCurrency(invoice.totalAmount)}
                                </Typography>
                              </TableCell>
                              <TableCell align="right">
                                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                  {formatCurrency(invoice.outstandingAmount)}
                                </Typography>
                              </TableCell>
                              <TableCell align="center">
                                <Chip 
                                  label={invoice.priority} 
                                  size="small" 
                                  color={getPriorityColor(invoice.priority)}
                                />
                              </TableCell>
                              <TableCell align="center">
                                <Typography variant="body2">
                                  {invoice.dueDate}
                                </Typography>
                                {invoice.status === 'overdue' && (
                                  <Chip label="Overdue" size="small" color="error" />
                                )}
                              </TableCell>
                              <TableCell align="right">
                                <TextField
                                  type="number"
                                  size="small"
                                  value={application?.amount || ''}
                                  onChange={(e) => {
                                    const amount = Number(e.target.value);
                                    if (amount > 0 && amount <= maxApplicable) {
                                      addApplication(invoice.id, amount);
                                    } else if (amount === 0) {
                                      removeApplication(invoice.id);
                                    }
                                  }}
                                  inputProps={{ 
                                    min: 0, 
                                    max: maxApplicable,
                                    step: 0.01 
                                  }}
                                  sx={{ width: 120 }}
                                  placeholder="0.00"
                                />
                              </TableCell>
                              <TableCell align="center">
                                <Button
                                  size="small"
                                  variant="outlined"
                                  onClick={() => addApplication(invoice.id, maxApplicable)}
                                  disabled={maxApplicable <= 0}
                                >
                                  Apply Max
                                </Button>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  {availableInvoices.length === 0 && (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      No outstanding invoices found for this customer.
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </Grid>
          )}

          {/* Application Summary */}
          {selectedReceipt && (
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Application Summary
                  </Typography>
                  
                  <Stack spacing={2}>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Receipt Amount:
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 500 }}>
                        {formatCurrency(selectedReceipt.remainingAmount)}
                      </Typography>
                    </Box>
                    
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Total Applied:
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 500 }}>
                        {formatCurrency(totalApplicationAmount)}
                      </Typography>
                    </Box>
                    
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Remaining:
                      </Typography>
                      <Typography 
                        variant="h6" 
                        sx={{ 
                          fontWeight: 600,
                          color: remainingReceiptAmount < 0 ? 'error.main' : 'text.primary'
                        }}
                      >
                        {formatCurrency(remainingReceiptAmount)}
                      </Typography>
                    </Box>

                    {remainingReceiptAmount < 0 && (
                      <Alert severity="error">
                        Application amount exceeds receipt balance
                      </Alert>
                    )}

                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Application Progress:
                      </Typography>
                      <LinearProgress 
                        variant="determinate" 
                        value={Math.min((totalApplicationAmount / selectedReceipt.remainingAmount) * 100, 100)}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                    </Box>

                    <Stack spacing={2} sx={{ mt: 3 }}>
                      <Button
                        variant="outlined"
                        startIcon={<Preview />}
                        onClick={() => setPreviewOpen(true)}
                        fullWidth
                        disabled={applications.length === 0}
                      >
                        Preview Application
                      </Button>
                      <Button
                        variant="contained"
                        startIcon={<CheckCircle />}
                        onClick={handleApplyReceipts}
                        fullWidth
                        disabled={loading || !validateApplications() || remainingReceiptAmount < 0}
                      >
                        Apply Receipt
                      </Button>
                    </Stack>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>

        {/* Preview Dialog */}
        <Dialog open={previewOpen} onClose={() => setPreviewOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>Receipt Application Preview</DialogTitle>
          <DialogContent>
            {selectedReceipt && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Receipt: {selectedReceipt.id}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Customer: {selectedReceipt.customerName}
                </Typography>
                
                <TableContainer sx={{ mt: 2 }}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Invoice</TableCell>
                        <TableCell align="right">Applied Amount</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {applications.map((app) => {
                        const invoice = availableInvoices.find(inv => inv.id === app.invoiceId);
                        return (
                          <TableRow key={app.invoiceId}>
                            <TableCell>{app.invoiceId}</TableCell>
                            <TableCell align="right">{formatCurrency(app.amount)}</TableCell>
                          </TableRow>
                        );
                      })}
                      <TableRow>
                        <TableCell sx={{ fontWeight: 600 }}>Total</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 600 }}>
                          {formatCurrency(totalApplicationAmount)}
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setPreviewOpen(false)}>Close</Button>
            <Button variant="contained">Generate Report</Button>
          </DialogActions>
        </Dialog>
      </motion.div>
    </Box>
  );
};

export default ApplyReceiptPanel;
