import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  Button,
  Stack,
  Autocomplete,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Add,
  Delete,
  Preview,
  Save,
  Send,
  Calculate,
  Business,
  Person
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface InvoicePanelProps {
  token: string;
  userRoles: string[];
}

interface LineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  amount: number;
  taxable: boolean;
}

interface Customer {
  id: string;
  name: string;
  type: 'individual' | 'corporate';
  vatNumber?: string;
  address: string;
}

interface Agreement {
  id: string;
  customerId: string;
  vehicleId: string;
  startDate: string;
  endDate: string;
  status: string;
}

const InvoicePanel: React.FC<InvoicePanelProps> = ({ token, userRoles }) => {
  const [formData, setFormData] = useState({
    customerId: '',
    agreementId: '',
    invoiceDate: new Date().toISOString().split('T')[0],
    dueDate: '',
    paymentTerms: '30',
    currency: 'AED',
    notes: '',
    isLPO: false,
    lpoNumber: '',
    managerOverride: false
  });

  const [lineItems, setLineItems] = useState<LineItem[]>([
    {
      id: '1',
      description: 'Vehicle Rental - Base Rate',
      quantity: 1,
      unitPrice: 0,
      amount: 0,
      taxable: true
    }
  ]);

  const [calculations, setCalculations] = useState({
    subtotal: 0,
    vatAmount: 0,
    totalAmount: 0,
    vatRate: 5 // 5% VAT for UAE
  });

  const [previewOpen, setPreviewOpen] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);

  // Mock data - in real implementation, these would come from API
  const customers: Customer[] = [
    { id: 'CUST001', name: 'Ahmed Al Mansouri', type: 'individual', address: 'Dubai, UAE' },
    { id: 'CUST002', name: 'Emirates Business Solutions', type: 'corporate', vatNumber: 'AE123456789012345', address: 'Abu Dhabi, UAE' },
    { id: 'CUST003', name: 'Sarah Johnson', type: 'individual', address: 'Sharjah, UAE' }
  ];

  const agreements: Agreement[] = [
    { id: 'AGR001', customerId: 'CUST001', vehicleId: 'VEH001', startDate: '2024-01-01', endDate: '2024-12-31', status: 'active' },
    { id: 'AGR002', customerId: 'CUST002', vehicleId: 'VEH002', startDate: '2024-02-01', endDate: '2024-08-31', status: 'active' }
  ];

  const lineItemTemplates = [
    { description: 'Vehicle Rental - Base Rate', unitPrice: 2500, taxable: true },
    { description: 'Insurance Premium', unitPrice: 300, taxable: true },
    { description: 'Salik Charges', unitPrice: 50, taxable: false },
    { description: 'Traffic Fines', unitPrice: 0, taxable: false },
    { description: 'Fuel Charges', unitPrice: 200, taxable: true },
    { description: 'Damage Assessment', unitPrice: 0, taxable: true },
    { description: 'Additional Driver Fee', unitPrice: 150, taxable: true },
    { description: 'GPS Device', unitPrice: 100, taxable: true }
  ];

  const calculateTotals = () => {
    const subtotal = lineItems.reduce((sum, item) => sum + item.amount, 0);
    const taxableAmount = lineItems.filter(item => item.taxable).reduce((sum, item) => sum + item.amount, 0);
    const vatAmount = (taxableAmount * calculations.vatRate) / 100;
    const totalAmount = subtotal + vatAmount;

    setCalculations({
      ...calculations,
      subtotal,
      vatAmount,
      totalAmount
    });
  };

  const addLineItem = () => {
    const newItem: LineItem = {
      id: Date.now().toString(),
      description: '',
      quantity: 1,
      unitPrice: 0,
      amount: 0,
      taxable: true
    };
    setLineItems([...lineItems, newItem]);
  };

  const updateLineItem = (id: string, field: keyof LineItem, value: any) => {
    setLineItems(items => items.map(item => {
      if (item.id === id) {
        const updated = { ...item, [field]: value };
        if (field === 'quantity' || field === 'unitPrice') {
          updated.amount = updated.quantity * updated.unitPrice;
        }
        return updated;
      }
      return item;
    }));
  };

  const removeLineItem = (id: string) => {
    setLineItems(items => items.filter(item => item.id !== id));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.customerId) newErrors.customerId = 'Customer is required';
    if (!formData.dueDate) newErrors.dueDate = 'Due date is required';
    if (lineItems.length === 0) newErrors.lineItems = 'At least one line item is required';
    if (lineItems.some(item => !item.description)) newErrors.lineItems = 'All line items must have descriptions';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSaveDraft = async () => {
    if (!validateForm()) return;
    
    setLoading(true);
    try {
      // API call to save draft
      console.log('Saving draft invoice...', { formData, lineItems, calculations });
      // await api.saveDraftInvoice(token, { formData, lineItems, calculations });
    } catch (error) {
      console.error('Error saving draft:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateInvoice = async () => {
    if (!validateForm()) return;
    
    setLoading(true);
    try {
      // API call to create invoice
      console.log('Creating invoice...', { formData, lineItems, calculations });
      // await api.createInvoice(token, { formData, lineItems, calculations });
    } catch (error) {
      console.error('Error creating invoice:', error);
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    calculateTotals();
  }, [lineItems]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: formData.currency
    }).format(amount);
  };

  const selectedCustomer = customers.find(c => c.id === formData.customerId);
  const availableAgreements = agreements.filter(a => a.customerId === formData.customerId);

  return (
    <Box>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          Create Invoice
        </Typography>

        <Grid container spacing={3}>
          {/* Customer & Agreement Selection */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Customer & Agreement Details
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Autocomplete
                      options={customers}
                      getOptionLabel={(option) => `${option.name} (${option.id})`}
                      value={customers.find(c => c.id === formData.customerId) || null}
                      onChange={(_, value) => setFormData({ ...formData, customerId: value?.id || '' })}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Customer"
                          error={!!errors.customerId}
                          helperText={errors.customerId}
                          InputProps={{
                            ...params.InputProps,
                            startAdornment: selectedCustomer?.type === 'corporate' ? <Business /> : <Person />
                          }}
                        />
                      )}
                      renderOption={(props, option) => (
                        <Box component="li" {...props}>
                          <Stack direction="row" alignItems="center" spacing={1}>
                            {option.type === 'corporate' ? <Business /> : <Person />}
                            <Box>
                              <Typography variant="body2">{option.name}</Typography>
                              <Typography variant="caption" color="text.secondary">
                                {option.id} • {option.type}
                              </Typography>
                            </Box>
                          </Stack>
                        </Box>
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Autocomplete
                      options={availableAgreements}
                      getOptionLabel={(option) => `${option.id} - ${option.vehicleId}`}
                      value={availableAgreements.find(a => a.id === formData.agreementId) || null}
                      onChange={(_, value) => setFormData({ ...formData, agreementId: value?.id || '' })}
                      disabled={!formData.customerId}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Agreement (Optional)"
                          helperText="Link invoice to specific agreement"
                        />
                      )}
                    />
                  </Grid>
                </Grid>

                {selectedCustomer && (
                  <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                    <Stack direction="row" spacing={2} alignItems="center">
                      <Chip 
                        label={selectedCustomer.type} 
                        color={selectedCustomer.type === 'corporate' ? 'primary' : 'default'}
                        size="small"
                      />
                      {selectedCustomer.vatNumber && (
                        <Typography variant="caption">
                          VAT: {selectedCustomer.vatNumber}
                        </Typography>
                      )}
                      <Typography variant="caption" color="text.secondary">
                        {selectedCustomer.address}
                      </Typography>
                    </Stack>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Invoice Details */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Invoice Details
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Invoice Date"
                      type="date"
                      value={formData.invoiceDate}
                      onChange={(e) => setFormData({ ...formData, invoiceDate: e.target.value })}
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Due Date"
                      type="date"
                      value={formData.dueDate}
                      onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      error={!!errors.dueDate}
                      helperText={errors.dueDate}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Payment Terms</InputLabel>
                      <Select
                        value={formData.paymentTerms}
                        onChange={(e) => setFormData({ ...formData, paymentTerms: e.target.value })}
                        label="Payment Terms"
                      >
                        <MenuItem value="0">Due on Receipt</MenuItem>
                        <MenuItem value="15">Net 15 Days</MenuItem>
                        <MenuItem value="30">Net 30 Days</MenuItem>
                        <MenuItem value="45">Net 45 Days</MenuItem>
                        <MenuItem value="60">Net 60 Days</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Currency</InputLabel>
                      <Select
                        value={formData.currency}
                        onChange={(e) => setFormData({ ...formData, currency: e.target.value })}
                        label="Currency"
                      >
                        <MenuItem value="AED">AED - UAE Dirham</MenuItem>
                        <MenuItem value="USD">USD - US Dollar</MenuItem>
                        <MenuItem value="EUR">EUR - Euro</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  {selectedCustomer?.type === 'corporate' && (
                    <>
                      <Grid item xs={12}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={formData.isLPO}
                              onChange={(e) => setFormData({ ...formData, isLPO: e.target.checked })}
                            />
                          }
                          label="LPO (Purchase Order) Invoice"
                        />
                      </Grid>
                      {formData.isLPO && (
                        <Grid item xs={12} sm={6}>
                          <TextField
                            label="LPO Number"
                            value={formData.lpoNumber}
                            onChange={(e) => setFormData({ ...formData, lpoNumber: e.target.value })}
                            fullWidth
                            helperText="Customer's Purchase Order Number"
                          />
                        </Grid>
                      )}
                    </>
                  )}
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Invoice Summary */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Invoice Summary
                </Typography>
                <Stack spacing={2}>
                  <Box>
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="body2">Subtotal:</Typography>
                      <Typography variant="body2">{formatCurrency(calculations.subtotal)}</Typography>
                    </Stack>
                  </Box>
                  <Box>
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="body2">VAT ({calculations.vatRate}%):</Typography>
                      <Typography variant="body2">{formatCurrency(calculations.vatAmount)}</Typography>
                    </Stack>
                  </Box>
                  <Divider />
                  <Box>
                    <Stack direction="row" justifyContent="space-between">
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>Total:</Typography>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        {formatCurrency(calculations.totalAmount)}
                      </Typography>
                    </Stack>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Line Items */}
        <Grid container spacing={3} sx={{ mt: 2 }}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
                  <Typography variant="h6">Line Items</Typography>
                  <Button
                    startIcon={<Add />}
                    onClick={addLineItem}
                    variant="outlined"
                    size="small"
                  >
                    Add Item
                  </Button>
                </Stack>

                {errors.lineItems && (
                  <Alert severity="error" sx={{ mb: 2 }}>
                    {errors.lineItems}
                  </Alert>
                )}

                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Description</TableCell>
                        <TableCell align="center">Qty</TableCell>
                        <TableCell align="right">Unit Price</TableCell>
                        <TableCell align="right">Amount</TableCell>
                        <TableCell align="center">Taxable</TableCell>
                        <TableCell align="center">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {lineItems.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell>
                            <Autocomplete
                              freeSolo
                              options={lineItemTemplates.map(t => t.description)}
                              value={item.description}
                              onChange={(_, value) => {
                                updateLineItem(item.id, 'description', value || '');
                                const template = lineItemTemplates.find(t => t.description === value);
                                if (template) {
                                  updateLineItem(item.id, 'unitPrice', template.unitPrice);
                                  updateLineItem(item.id, 'taxable', template.taxable);
                                }
                              }}
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  size="small"
                                  placeholder="Enter description"
                                  sx={{ minWidth: 200 }}
                                />
                              )}
                            />
                          </TableCell>
                          <TableCell>
                            <TextField
                              type="number"
                              value={item.quantity}
                              onChange={(e) => updateLineItem(item.id, 'quantity', Number(e.target.value))}
                              size="small"
                              sx={{ width: 80 }}
                              inputProps={{ min: 0, step: 0.01 }}
                            />
                          </TableCell>
                          <TableCell>
                            <TextField
                              type="number"
                              value={item.unitPrice}
                              onChange={(e) => updateLineItem(item.id, 'unitPrice', Number(e.target.value))}
                              size="small"
                              sx={{ width: 120 }}
                              inputProps={{ min: 0, step: 0.01 }}
                            />
                          </TableCell>
                          <TableCell align="right">
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {formatCurrency(item.amount)}
                            </Typography>
                          </TableCell>
                          <TableCell align="center">
                            <Switch
                              checked={item.taxable}
                              onChange={(e) => updateLineItem(item.id, 'taxable', e.target.checked)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell align="center">
                            <IconButton
                              onClick={() => removeLineItem(item.id)}
                              size="small"
                              color="error"
                              disabled={lineItems.length === 1}
                            >
                              <Delete />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Notes and Actions */}
        <Grid container spacing={3} sx={{ mt: 2 }}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Additional Notes
                </Typography>
                <TextField
                  multiline
                  rows={4}
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                  placeholder="Enter any additional notes or special instructions..."
                  fullWidth
                />

                {userRoles.includes('admin') && (
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.managerOverride}
                        onChange={(e) => setFormData({ ...formData, managerOverride: e.target.checked })}
                      />
                    }
                    label="Manager Override (for discounts/adjustments)"
                    sx={{ mt: 2 }}
                  />
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Actions
                </Typography>
                <Stack spacing={2}>
                  <Button
                    variant="outlined"
                    startIcon={<Preview />}
                    onClick={() => setPreviewOpen(true)}
                    fullWidth
                    disabled={!formData.customerId}
                  >
                    Preview Invoice
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<Save />}
                    onClick={handleSaveDraft}
                    fullWidth
                    disabled={loading}
                  >
                    Save as Draft
                  </Button>
                  <Button
                    variant="contained"
                    startIcon={<Send />}
                    onClick={handleCreateInvoice}
                    fullWidth
                    disabled={loading || !formData.customerId}
                  >
                    Create Invoice
                  </Button>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Preview Dialog */}
        <Dialog open={previewOpen} onClose={() => setPreviewOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>Invoice Preview</DialogTitle>
          <DialogContent>
            <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
              <Typography variant="h6" gutterBottom>
                INVOICE
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">Bill To:</Typography>
                  <Typography variant="body1">{selectedCustomer?.name}</Typography>
                  <Typography variant="body2">{selectedCustomer?.address}</Typography>
                  {selectedCustomer?.vatNumber && (
                    <Typography variant="body2">VAT: {selectedCustomer.vatNumber}</Typography>
                  )}
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">Invoice Date:</Typography>
                  <Typography variant="body1">{formData.invoiceDate}</Typography>
                  <Typography variant="body2" color="text.secondary">Due Date:</Typography>
                  <Typography variant="body1">{formData.dueDate}</Typography>
                </Grid>
              </Grid>

              <Box sx={{ mt: 3 }}>
                <Typography variant="body1" sx={{ fontWeight: 600 }}>
                  Total Amount: {formatCurrency(calculations.totalAmount)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  (Including VAT: {formatCurrency(calculations.vatAmount)})
                </Typography>
              </Box>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setPreviewOpen(false)}>Close</Button>
            <Button variant="contained">Download PDF</Button>
          </DialogActions>
        </Dialog>
      </motion.div>
    </Box>
  );
};

export default InvoicePanel;
