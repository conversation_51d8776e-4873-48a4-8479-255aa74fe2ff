import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  Button,
  Stack,
  Autocomplete,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  RadioGroup,
  FormControlLabel,
  Radio,
  Divider
} from '@mui/material';
import {
  Payment,
  CreditCard,
  AccountBalance,
  LocalAtm,
  Business,
  Receipt as ReceiptIcon,
  Preview,
  Save
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface ReceiptPanelProps {
  token: string;
  userRoles: string[];
}

interface PaymentMethod {
  id: string;
  name: string;
  icon: React.ReactNode;
  requiresAuth: boolean;
  fields: string[];
}

interface PendingInvoice {
  id: string;
  customerId: string;
  customerName: string;
  amount: number;
  outstandingAmount: number;
  dueDate: string;
  status: string;
}

const ReceiptPanel: React.FC<ReceiptPanelProps> = ({ token, userRoles }) => {
  const [formData, setFormData] = useState({
    customerId: '',
    invoiceId: '',
    receiptDate: new Date().toISOString().split('T')[0],
    paymentMethod: '',
    amount: '',
    currency: 'AED',
    reference: '',
    notes: '',
    // Payment method specific fields
    cardNumber: '',
    cardType: '',
    authCode: '',
    bankName: '',
    transferRef: '',
    lpoNumber: '',
    chequeNumber: '',
    chequeDate: ''
  });

  const [previewOpen, setPreviewOpen] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);

  const paymentMethods: PaymentMethod[] = [
    {
      id: 'cash',
      name: 'Cash',
      icon: <LocalAtm />,
      requiresAuth: false,
      fields: []
    },
    {
      id: 'credit_card',
      name: 'Credit Card',
      icon: <CreditCard />,
      requiresAuth: true,
      fields: ['cardNumber', 'cardType', 'authCode']
    },
    {
      id: 'bank_transfer',
      name: 'Bank Transfer',
      icon: <AccountBalance />,
      requiresAuth: false,
      fields: ['bankName', 'transferRef']
    },
    {
      id: 'lpo',
      name: 'LPO/Corporate',
      icon: <Business />,
      requiresAuth: false,
      fields: ['lpoNumber']
    },
    {
      id: 'cheque',
      name: 'Cheque',
      icon: <ReceiptIcon />,
      requiresAuth: false,
      fields: ['chequeNumber', 'chequeDate', 'bankName']
    }
  ];

  // Mock data - in real implementation, these would come from API
  const customers = [
    { id: 'CUST001', name: 'Ahmed Al Mansouri' },
    { id: 'CUST002', name: 'Emirates Business Solutions' },
    { id: 'CUST003', name: 'Sarah Johnson' }
  ];

  const pendingInvoices: PendingInvoice[] = [
    {
      id: 'INV-2024-001',
      customerId: 'CUST001',
      customerName: 'Ahmed Al Mansouri',
      amount: 2500.00,
      outstandingAmount: 2500.00,
      dueDate: '2024-02-15',
      status: 'pending'
    },
    {
      id: 'INV-2024-002',
      customerId: 'CUST002',
      customerName: 'Emirates Business Solutions',
      amount: 5200.00,
      outstandingAmount: 3200.00,
      dueDate: '2024-02-20',
      status: 'partial'
    }
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: formData.currency
    }).format(amount);
  };

  const selectedPaymentMethod = paymentMethods.find(pm => pm.id === formData.paymentMethod);
  const selectedCustomer = customers.find(c => c.id === formData.customerId);
  const availableInvoices = pendingInvoices.filter(inv => 
    !formData.customerId || inv.customerId === formData.customerId
  );
  const selectedInvoice = pendingInvoices.find(inv => inv.id === formData.invoiceId);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.paymentMethod) newErrors.paymentMethod = 'Payment method is required';
    if (!formData.amount || Number(formData.amount) <= 0) newErrors.amount = 'Valid amount is required';
    if (!formData.receiptDate) newErrors.receiptDate = 'Receipt date is required';

    // Validate payment method specific fields
    if (selectedPaymentMethod) {
      selectedPaymentMethod.fields.forEach(field => {
        if (!formData[field as keyof typeof formData]) {
          newErrors[field] = `${field.replace(/([A-Z])/g, ' $1').toLowerCase()} is required`;
        }
      });
    }

    // Validate amount doesn't exceed outstanding balance
    if (selectedInvoice && Number(formData.amount) > selectedInvoice.outstandingAmount) {
      newErrors.amount = `Amount cannot exceed outstanding balance of ${formatCurrency(selectedInvoice.outstandingAmount)}`;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreateReceipt = async () => {
    if (!validateForm()) return;
    
    setLoading(true);
    try {
      // API call to create receipt
      console.log('Creating receipt...', formData);
      // await api.createReceipt(token, formData);
    } catch (error) {
      console.error('Error creating receipt:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderPaymentMethodFields = () => {
    if (!selectedPaymentMethod) return null;

    return (
      <Grid container spacing={3}>
        {selectedPaymentMethod.fields.includes('cardNumber') && (
          <>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Card Number (Last 4 digits)"
                value={formData.cardNumber}
                onChange={(e) => setFormData({ ...formData, cardNumber: e.target.value })}
                fullWidth
                error={!!errors.cardNumber}
                helperText={errors.cardNumber}
                inputProps={{ maxLength: 4 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!errors.cardType}>
                <InputLabel>Card Type</InputLabel>
                <Select
                  value={formData.cardType}
                  onChange={(e) => setFormData({ ...formData, cardType: e.target.value })}
                  label="Card Type"
                >
                  <MenuItem value="visa">Visa</MenuItem>
                  <MenuItem value="mastercard">Mastercard</MenuItem>
                  <MenuItem value="amex">American Express</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Authorization Code"
                value={formData.authCode}
                onChange={(e) => setFormData({ ...formData, authCode: e.target.value })}
                fullWidth
                error={!!errors.authCode}
                helperText={errors.authCode}
              />
            </Grid>
          </>
        )}

        {selectedPaymentMethod.fields.includes('bankName') && (
          <Grid item xs={12} sm={6}>
            <TextField
              label="Bank Name"
              value={formData.bankName}
              onChange={(e) => setFormData({ ...formData, bankName: e.target.value })}
              fullWidth
              error={!!errors.bankName}
              helperText={errors.bankName}
            />
          </Grid>
        )}

        {selectedPaymentMethod.fields.includes('transferRef') && (
          <Grid item xs={12} sm={6}>
            <TextField
              label="Transfer Reference"
              value={formData.transferRef}
              onChange={(e) => setFormData({ ...formData, transferRef: e.target.value })}
              fullWidth
              error={!!errors.transferRef}
              helperText={errors.transferRef}
            />
          </Grid>
        )}

        {selectedPaymentMethod.fields.includes('lpoNumber') && (
          <Grid item xs={12} sm={6}>
            <TextField
              label="LPO Number"
              value={formData.lpoNumber}
              onChange={(e) => setFormData({ ...formData, lpoNumber: e.target.value })}
              fullWidth
              error={!!errors.lpoNumber}
              helperText={errors.lpoNumber}
            />
          </Grid>
        )}

        {selectedPaymentMethod.fields.includes('chequeNumber') && (
          <>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Cheque Number"
                value={formData.chequeNumber}
                onChange={(e) => setFormData({ ...formData, chequeNumber: e.target.value })}
                fullWidth
                error={!!errors.chequeNumber}
                helperText={errors.chequeNumber}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Cheque Date"
                type="date"
                value={formData.chequeDate}
                onChange={(e) => setFormData({ ...formData, chequeDate: e.target.value })}
                fullWidth
                InputLabelProps={{ shrink: true }}
                error={!!errors.chequeDate}
                helperText={errors.chequeDate}
              />
            </Grid>
          </>
        )}
      </Grid>
    );
  };

  return (
    <Box>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          Record Payment Receipt
        </Typography>

        <Grid container spacing={3}>
          {/* Customer & Invoice Selection */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Customer & Invoice Selection
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Autocomplete
                      options={customers}
                      getOptionLabel={(option) => `${option.name} (${option.id})`}
                      value={customers.find(c => c.id === formData.customerId) || null}
                      onChange={(_, value) => setFormData({ 
                        ...formData, 
                        customerId: value?.id || '',
                        invoiceId: '' // Reset invoice selection when customer changes
                      })}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Customer (Optional)"
                          helperText="Select customer to filter invoices"
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Autocomplete
                      options={availableInvoices}
                      getOptionLabel={(option) => `${option.id} - ${formatCurrency(option.outstandingAmount)} outstanding`}
                      value={availableInvoices.find(inv => inv.id === formData.invoiceId) || null}
                      onChange={(_, value) => setFormData({ 
                        ...formData, 
                        invoiceId: value?.id || '',
                        amount: value ? value.outstandingAmount.toString() : ''
                      })}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Invoice (Optional)"
                          helperText="Link payment to specific invoice"
                        />
                      )}
                      renderOption={(props, option) => (
                        <Box component="li" {...props}>
                          <Stack direction="row" justifyContent="space-between" sx={{ width: '100%' }}>
                            <Box>
                              <Typography variant="body2">{option.id}</Typography>
                              <Typography variant="caption" color="text.secondary">
                                {option.customerName}
                              </Typography>
                            </Box>
                            <Box textAlign="right">
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                {formatCurrency(option.outstandingAmount)}
                              </Typography>
                              <Chip 
                                label={option.status} 
                                size="small" 
                                color={option.status === 'pending' ? 'warning' : 'info'}
                              />
                            </Box>
                          </Stack>
                        </Box>
                      )}
                    />
                  </Grid>
                </Grid>

                {selectedInvoice && (
                  <Alert severity="info" sx={{ mt: 2 }}>
                    Invoice {selectedInvoice.id}: Total {formatCurrency(selectedInvoice.amount)}, 
                    Outstanding {formatCurrency(selectedInvoice.outstandingAmount)}, 
                    Due {selectedInvoice.dueDate}
                  </Alert>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Payment Details */}
        <Grid container spacing={3} sx={{ mt: 2 }}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Payment Details
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Receipt Date"
                      type="date"
                      value={formData.receiptDate}
                      onChange={(e) => setFormData({ ...formData, receiptDate: e.target.value })}
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      error={!!errors.receiptDate}
                      helperText={errors.receiptDate}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Amount Received"
                      type="number"
                      value={formData.amount}
                      onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                      fullWidth
                      error={!!errors.amount}
                      helperText={errors.amount}
                      inputProps={{ min: 0, step: 0.01 }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom>
                      Payment Method
                    </Typography>
                    <RadioGroup
                      value={formData.paymentMethod}
                      onChange={(e) => setFormData({ ...formData, paymentMethod: e.target.value })}
                      row
                    >
                      {paymentMethods.map((method) => (
                        <FormControlLabel
                          key={method.id}
                          value={method.id}
                          control={<Radio />}
                          label={
                            <Stack direction="row" alignItems="center" spacing={1}>
                              {method.icon}
                              <Typography variant="body2">{method.name}</Typography>
                            </Stack>
                          }
                        />
                      ))}
                    </RadioGroup>
                    {errors.paymentMethod && (
                      <Typography variant="caption" color="error">
                        {errors.paymentMethod}
                      </Typography>
                    )}
                  </Grid>
                </Grid>

                {/* Payment Method Specific Fields */}
                {selectedPaymentMethod && selectedPaymentMethod.fields.length > 0 && (
                  <Box sx={{ mt: 3 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      {selectedPaymentMethod.name} Details
                    </Typography>
                    {renderPaymentMethodFields()}
                  </Box>
                )}

                <Box sx={{ mt: 3 }}>
                  <TextField
                    label="Reference Number"
                    value={formData.reference}
                    onChange={(e) => setFormData({ ...formData, reference: e.target.value })}
                    fullWidth
                    helperText="Internal reference or transaction ID"
                  />
                </Box>

                <Box sx={{ mt: 3 }}>
                  <TextField
                    label="Notes"
                    multiline
                    rows={3}
                    value={formData.notes}
                    onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                    fullWidth
                    placeholder="Additional notes about the payment..."
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Receipt Summary & Actions */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Receipt Summary
                </Typography>
                <Stack spacing={2}>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Payment Method:
                    </Typography>
                    <Typography variant="body1">
                      {selectedPaymentMethod?.name || 'Not selected'}
                    </Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Amount:
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {formData.amount ? formatCurrency(Number(formData.amount)) : formatCurrency(0)}
                    </Typography>
                  </Box>
                  {selectedInvoice && (
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Remaining Balance:
                      </Typography>
                      <Typography variant="body1">
                        {formatCurrency(selectedInvoice.outstandingAmount - Number(formData.amount || 0))}
                      </Typography>
                    </Box>
                  )}
                  <Divider />
                  <Stack spacing={2}>
                    <Button
                      variant="outlined"
                      startIcon={<Preview />}
                      onClick={() => setPreviewOpen(true)}
                      fullWidth
                      disabled={!formData.paymentMethod || !formData.amount}
                    >
                      Preview Receipt
                    </Button>
                    <Button
                      variant="contained"
                      startIcon={<Save />}
                      onClick={handleCreateReceipt}
                      fullWidth
                      disabled={loading || !formData.paymentMethod || !formData.amount}
                    >
                      Create Receipt
                    </Button>
                  </Stack>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Preview Dialog */}
        <Dialog open={previewOpen} onClose={() => setPreviewOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Receipt Preview</DialogTitle>
          <DialogContent>
            <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
              <Typography variant="h6" gutterBottom>
                PAYMENT RECEIPT
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">Date:</Typography>
                  <Typography variant="body1">{formData.receiptDate}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">Amount:</Typography>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {formatCurrency(Number(formData.amount || 0))}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">Payment Method:</Typography>
                  <Typography variant="body1">{selectedPaymentMethod?.name}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">Reference:</Typography>
                  <Typography variant="body1">{formData.reference || 'N/A'}</Typography>
                </Grid>
                {selectedInvoice && (
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">Applied to Invoice:</Typography>
                    <Typography variant="body1">{selectedInvoice.id}</Typography>
                  </Grid>
                )}
              </Grid>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setPreviewOpen(false)}>Close</Button>
            <Button variant="contained">Download PDF</Button>
          </DialogActions>
        </Dialog>
      </motion.div>
    </Box>
  );
};

export default ReceiptPanel;
