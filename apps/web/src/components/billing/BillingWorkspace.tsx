import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Paper,
  useTheme,
  Chip,
  Stack,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Receipt,
  Payment,
  AccountBalance,
  History,
  Dashboard,
  Help,
  Settings
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import InvoicePanel from './InvoicePanel';
import ReceiptPanel from './ReceiptPanel';
import ApplyReceiptPanel from './ApplyReceiptPanel';
import BillingHistory from './BillingHistory';
import BillingDashboard from './BillingDashboard';

interface BillingWorkspaceProps {
  token: string;
  userRoles: string[];
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel({ children, value, index }: TabPanelProps) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`billing-tabpanel-${index}`}
      aria-labelledby={`billing-tab-${index}`}
    >
      {value === index && (
        <AnimatePresence mode="wait">
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Box sx={{ py: 3 }}>
              {children}
            </Box>
          </motion.div>
        </AnimatePresence>
      )}
    </div>
  );
}

const BillingWorkspace: React.FC<BillingWorkspaceProps> = ({ token, userRoles }) => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const isAdmin = userRoles.includes('admin');

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const tabs = [
    {
      label: 'Dashboard',
      icon: <Dashboard />,
      component: <BillingDashboard token={token} userRoles={userRoles} />,
      adminOnly: false
    },
    {
      label: 'Create Invoice',
      icon: <Receipt />,
      component: <InvoicePanel token={token} userRoles={userRoles} />,
      adminOnly: true
    },
    {
      label: 'Record Payment',
      icon: <Payment />,
      component: <ReceiptPanel token={token} userRoles={userRoles} />,
      adminOnly: true
    },
    {
      label: 'Apply Receipt',
      icon: <AccountBalance />,
      component: <ApplyReceiptPanel token={token} userRoles={userRoles} />,
      adminOnly: true
    },
    {
      label: 'History',
      icon: <History />,
      component: <BillingHistory token={token} userRoles={userRoles} />,
      adminOnly: false
    }
  ];

  const availableTabs = tabs.filter(tab => !tab.adminOnly || isAdmin);

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 3 }}>
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Paper
            sx={{
              p: 3,
              mb: 3,
              background: `linear-gradient(135deg, ${theme.palette.success.main}15, ${theme.palette.success.main}05)`,
              border: `1px solid ${theme.palette.success.main}30`
            }}
          >
            <Stack direction="row" alignItems="center" justifyContent="space-between">
              <Box>
                <Typography variant="h4" sx={{ fontWeight: 600, color: 'success.main' }}>
                  Billing & Payments
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Enterprise-grade invoice and payment management system
                </Typography>
              </Box>
              <Stack direction="row" spacing={1} alignItems="center">
                <Chip
                  label={isAdmin ? 'Full Access' : 'View Only'}
                  color={isAdmin ? 'success' : 'default'}
                  variant="outlined"
                />
                <Tooltip title="Help & Documentation">
                  <IconButton size="small">
                    <Help />
                  </IconButton>
                </Tooltip>
                {isAdmin && (
                  <Tooltip title="Billing Settings">
                    <IconButton size="small">
                      <Settings />
                    </IconButton>
                  </Tooltip>
                )}
              </Stack>
            </Stack>
          </Paper>
        </motion.div>

        {/* Navigation Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Paper sx={{ mb: 3 }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                borderBottom: 1,
                borderColor: 'divider',
                '& .MuiTab-root': {
                  minHeight: 72,
                  textTransform: 'none',
                  fontSize: '0.95rem',
                  fontWeight: 500
                }
              }}
            >
              {availableTabs.map((tab, index) => (
                <Tab
                  key={index}
                  icon={tab.icon}
                  label={tab.label}
                  iconPosition="start"
                  sx={{
                    '& .MuiSvgIcon-root': {
                      mr: 1
                    }
                  }}
                />
              ))}
            </Tabs>
          </Paper>
        </motion.div>

        {/* Tab Content */}
        {availableTabs.map((tab, index) => (
          <TabPanel key={index} value={activeTab} index={index}>
            {tab.component}
          </TabPanel>
        ))}
      </Box>
    </Container>
  );
};

export default BillingWorkspace;
