import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  Button,
  Stack,
  Chip,
  IconButton,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  Avatar,
  Menu,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Search,
  FilterList,
  Download,
  Visibility,
  Receipt,
  Payment,
  MoreVert,
  Print,
  Email,
  Edit
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface BillingHistoryProps {
  token: string;
  userRoles: string[];
}

interface BillingRecord {
  id: string;
  type: 'invoice' | 'receipt';
  customerId: string;
  customerName: string;
  amount: number;
  status: 'pending' | 'paid' | 'partial' | 'overdue' | 'applied' | 'unapplied';
  date: string;
  dueDate?: string;
  paymentMethod?: string;
  reference?: string;
  createdBy: string;
  lastModified: string;
}

const BillingHistory: React.FC<BillingHistoryProps> = ({ token, userRoles }) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateRange, setDateRange] = useState({ from: '', to: '' });
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedRecord, setSelectedRecord] = useState<BillingRecord | null>(null);

  const isAdmin = userRoles.includes('admin');

  // Mock data - in real implementation, this would come from API
  const billingRecords: BillingRecord[] = [
    {
      id: 'INV-2024-001',
      type: 'invoice',
      customerId: 'CUST001',
      customerName: 'Ahmed Al Mansouri',
      amount: 2500.00,
      status: 'paid',
      date: '2024-01-15',
      dueDate: '2024-02-15',
      createdBy: 'admin',
      lastModified: '2024-01-20'
    },
    {
      id: 'RCP-2024-001',
      type: 'receipt',
      customerId: 'CUST001',
      customerName: 'Ahmed Al Mansouri',
      amount: 2500.00,
      status: 'applied',
      date: '2024-01-20',
      paymentMethod: 'Bank Transfer',
      reference: 'TXN123456',
      createdBy: 'admin',
      lastModified: '2024-01-20'
    },
    {
      id: 'INV-2024-002',
      type: 'invoice',
      customerId: 'CUST002',
      customerName: 'Emirates Business Solutions',
      amount: 5200.00,
      status: 'partial',
      date: '2024-01-18',
      dueDate: '2024-01-20',
      createdBy: 'finance_user',
      lastModified: '2024-01-25'
    },
    {
      id: 'RCP-2024-002',
      type: 'receipt',
      customerId: 'CUST002',
      customerName: 'Emirates Business Solutions',
      amount: 2000.00,
      status: 'applied',
      date: '2024-01-25',
      paymentMethod: 'Credit Card',
      reference: 'CC789012',
      createdBy: 'finance_user',
      lastModified: '2024-01-25'
    },
    {
      id: 'INV-2024-003',
      type: 'invoice',
      customerId: 'CUST003',
      customerName: 'Sarah Johnson',
      amount: 1800.00,
      status: 'overdue',
      date: '2024-01-10',
      dueDate: '2024-01-25',
      createdBy: 'admin',
      lastModified: '2024-01-10'
    },
    {
      id: 'RCP-2024-003',
      type: 'receipt',
      customerId: 'CUST004',
      customerName: 'Tech Solutions Ltd',
      amount: 1200.00,
      status: 'unapplied',
      date: '2024-01-28',
      paymentMethod: 'Cash',
      reference: 'CASH001',
      createdBy: 'admin',
      lastModified: '2024-01-28'
    }
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AE', {
      style: 'currency',
      currency: 'AED'
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': case 'applied': return 'success';
      case 'pending': case 'unapplied': return 'warning';
      case 'partial': return 'info';
      case 'overdue': return 'error';
      default: return 'default';
    }
  };

  const getTypeIcon = (type: string) => {
    return type === 'invoice' ? <Receipt /> : <Payment />;
  };

  const filteredRecords = billingRecords.filter(record => {
    const matchesSearch = 
      record.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.customerId.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = typeFilter === 'all' || record.type === typeFilter;
    const matchesStatus = statusFilter === 'all' || record.status === statusFilter;
    
    const matchesDateRange = 
      (!dateRange.from || record.date >= dateRange.from) &&
      (!dateRange.to || record.date <= dateRange.to);

    return matchesSearch && matchesType && matchesStatus && matchesDateRange;
  });

  const paginatedRecords = filteredRecords.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, record: BillingRecord) => {
    setAnchorEl(event.currentTarget);
    setSelectedRecord(record);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedRecord(null);
  };

  const handleExport = () => {
    // Export functionality
    console.log('Exporting billing history...', filteredRecords);
  };

  const handleView = (record: BillingRecord) => {
    console.log('Viewing record:', record);
    handleMenuClose();
  };

  const handlePrint = (record: BillingRecord) => {
    console.log('Printing record:', record);
    handleMenuClose();
  };

  const handleEmail = (record: BillingRecord) => {
    console.log('Emailing record:', record);
    handleMenuClose();
  };

  const handleEdit = (record: BillingRecord) => {
    console.log('Editing record:', record);
    handleMenuClose();
  };

  return (
    <Box>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          Billing History
        </Typography>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Search & Filters
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder="Search by ID, customer name, or customer ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Type</InputLabel>
                  <Select
                    value={typeFilter}
                    onChange={(e) => setTypeFilter(e.target.value)}
                    label="Type"
                  >
                    <MenuItem value="all">All Types</MenuItem>
                    <MenuItem value="invoice">Invoices</MenuItem>
                    <MenuItem value="receipt">Receipts</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    label="Status"
                  >
                    <MenuItem value="all">All Statuses</MenuItem>
                    <MenuItem value="pending">Pending</MenuItem>
                    <MenuItem value="paid">Paid</MenuItem>
                    <MenuItem value="partial">Partial</MenuItem>
                    <MenuItem value="overdue">Overdue</MenuItem>
                    <MenuItem value="applied">Applied</MenuItem>
                    <MenuItem value="unapplied">Unapplied</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  fullWidth
                  label="From Date"
                  type="date"
                  value={dateRange.from}
                  onChange={(e) => setDateRange({ ...dateRange, from: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  fullWidth
                  label="To Date"
                  type="date"
                  value={dateRange.to}
                  onChange={(e) => setDateRange({ ...dateRange, to: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
            </Grid>
            <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
              <Button
                variant="outlined"
                startIcon={<FilterList />}
                onClick={() => {
                  setSearchTerm('');
                  setTypeFilter('all');
                  setStatusFilter('all');
                  setDateRange({ from: '', to: '' });
                }}
              >
                Clear Filters
              </Button>
              <Button
                variant="outlined"
                startIcon={<Download />}
                onClick={handleExport}
              >
                Export Results
              </Button>
            </Stack>
          </CardContent>
        </Card>

        {/* Results Summary */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography variant="body1">
              Showing {paginatedRecords.length} of {filteredRecords.length} records
            </Typography>
            <Stack direction="row" spacing={2}>
              <Typography variant="body2" color="text.secondary">
                Total Amount: {formatCurrency(filteredRecords.reduce((sum, record) => sum + record.amount, 0))}
              </Typography>
            </Stack>
          </Stack>
        </Paper>

        {/* Records Table */}
        <Card>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Type</TableCell>
                  <TableCell>ID</TableCell>
                  <TableCell>Customer</TableCell>
                  <TableCell align="right">Amount</TableCell>
                  <TableCell align="center">Status</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Due Date</TableCell>
                  <TableCell>Payment Method</TableCell>
                  <TableCell>Created By</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedRecords.map((record) => (
                  <TableRow key={record.id} hover>
                    <TableCell>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <Avatar sx={{ 
                          bgcolor: record.type === 'invoice' ? 'primary.main' : 'success.main',
                          width: 32,
                          height: 32
                        }}>
                          {getTypeIcon(record.type)}
                        </Avatar>
                        <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                          {record.type}
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {record.id}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {record.customerName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {record.customerId}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {formatCurrency(record.amount)}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Chip 
                        label={record.status} 
                        size="small" 
                        color={getStatusColor(record.status)}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {record.date}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {record.dueDate || '-'}
                      </Typography>
                      {record.status === 'overdue' && (
                        <Typography variant="caption" color="error">
                          Overdue
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {record.paymentMethod || '-'}
                      </Typography>
                      {record.reference && (
                        <Typography variant="caption" color="text.secondary">
                          Ref: {record.reference}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {record.createdBy}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Modified: {record.lastModified}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Tooltip title="More actions">
                        <IconButton
                          size="small"
                          onClick={(e) => handleMenuOpen(e, record)}
                        >
                          <MoreVert />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          
          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50]}
            component="div"
            count={filteredRecords.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            onRowsPerPageChange={(e) => {
              setRowsPerPage(parseInt(e.target.value, 10));
              setPage(0);
            }}
          />
        </Card>

        {/* Actions Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => selectedRecord && handleView(selectedRecord)}>
            <ListItemIcon>
              <Visibility fontSize="small" />
            </ListItemIcon>
            <ListItemText>View Details</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => selectedRecord && handlePrint(selectedRecord)}>
            <ListItemIcon>
              <Print fontSize="small" />
            </ListItemIcon>
            <ListItemText>Print PDF</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => selectedRecord && handleEmail(selectedRecord)}>
            <ListItemIcon>
              <Email fontSize="small" />
            </ListItemIcon>
            <ListItemText>Email Customer</ListItemText>
          </MenuItem>
          {isAdmin && (
            <MenuItem onClick={() => selectedRecord && handleEdit(selectedRecord)}>
              <ListItemIcon>
                <Edit fontSize="small" />
              </ListItemIcon>
              <ListItemText>Edit Record</ListItemText>
            </MenuItem>
          )}
        </Menu>
      </motion.div>
    </Box>
  );
};

export default BillingHistory;
