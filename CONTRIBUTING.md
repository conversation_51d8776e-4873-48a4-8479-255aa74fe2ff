# Contributing to A-ReALM

Thank you for your interest in contributing to A-ReALM! This document provides guidelines and information for contributors.

## 📋 Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Coding Standards](#coding-standards)
- [Testing Guidelines](#testing-guidelines)
- [Commit Convention](#commit-convention)
- [Pull Request Process](#pull-request-process)
- [Issue Reporting](#issue-reporting)

## Code of Conduct

This project adheres to a code of conduct that we expect all contributors to follow:

- **Be respectful**: Treat everyone with respect and kindness
- **Be inclusive**: Welcome newcomers and help them get started
- **Be collaborative**: Work together to improve the project
- **Be constructive**: Provide helpful feedback and suggestions

## Getting Started

### Prerequisites
- Node.js 18.17.0 or higher
- pnpm 8.15.4
- Docker and Docker Compose
- Git

### Setup Development Environment
```bash
# 1. Fork and clone the repository
git clone https://github.com/your-username/A-ReALM-V1.git
cd A-ReALM-V1

# 2. Install dependencies
pnpm install

# 3. Set up environment
cp .env.example .env
pnpm generate:secrets

# 4. Start infrastructure
docker-compose -f infra/docker-compose.yml up -d

# 5. Set up database
pnpm --filter @a-realm/db build
pnpm --filter @a-realm/db migrate:dev
pnpm seed

# 6. Start development servers
pnpm dev
```

## Development Workflow

### Branching Strategy
- **main**: Production-ready code
- **develop**: Integration branch for features
- **feature/[name]**: Individual feature development
- **hotfix/[name]**: Critical bug fixes
- **release/[version]**: Release preparation

### Creating a Feature Branch
```bash
# Start from develop branch
git checkout develop
git pull origin develop

# Create feature branch
git checkout -b feature/your-feature-name

# Make your changes
# ... code, test, commit ...

# Push branch
git push origin feature/your-feature-name

# Create pull request
```

## Coding Standards

### TypeScript Guidelines
- Use strict TypeScript configuration
- Prefer interfaces over types for object shapes
- Use meaningful variable and function names
- Add JSDoc comments for public APIs

### Code Style
- **ESLint**: Follow configured rules (run `pnpm lint`)
- **Prettier**: Auto-format code (run `pnpm format`)
- **File naming**: Use kebab-case for files, PascalCase for components
- **Import order**: External libraries, internal packages, relative imports

### Example Code Structure
```typescript
// Good: Clear interface definition
interface CustomerData {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
}

// Good: Descriptive function with JSDoc
/**
 * Creates a new customer record
 * @param customerData - The customer information
 * @returns Promise resolving to created customer
 */
async function createCustomer(customerData: CustomerData): Promise<Customer> {
  // Implementation
}
```

### Package Structure
```
packages/your-package/
├── src/
│   ├── index.ts          # Main export
│   ├── types.ts          # Type definitions
│   └── utils/            # Utility functions
├── __tests__/            # Test files
├── package.json
├── tsconfig.json
└── README.md
```

## Testing Guidelines

### Test Types
- **Unit Tests**: Test individual functions and components
- **Integration Tests**: Test service interactions
- **Security Tests**: Test authentication, authorization, input validation

### Writing Tests
```typescript
// Example unit test
describe('CustomerService', () => {
  describe('createCustomer', () => {
    it('should create customer with valid data', async () => {
      const customerData = {
        name: 'John Doe',
        email: '<EMAIL>'
      };
      
      const result = await customerService.createCustomer(customerData);
      
      expect(result).toMatchObject({
        name: 'John Doe',
        email: '<EMAIL>'
      });
      expect(result.id).toBeDefined();
    });

    it('should throw error with invalid email', async () => {
      const customerData = {
        name: 'John Doe',
        email: 'invalid-email'
      };
      
      await expect(customerService.createCustomer(customerData))
        .rejects.toThrow('Invalid email format');
    });
  });
});
```

### Running Tests
```bash
# Run all tests
pnpm test

# Run tests with coverage
pnpm test:coverage

# Run tests in watch mode
pnpm test:watch

# Run specific test file
pnpm test CustomerService.test.ts
```

## Commit Convention

We use [Conventional Commits](https://www.conventionalcommits.org/) for clear commit history:

### Format
```
type(scope): description

[optional body]

[optional footer]
```

### Types
- **feat**: New feature
- **fix**: Bug fix
- **docs**: Documentation changes
- **style**: Code style changes (formatting, etc.)
- **refactor**: Code refactoring
- **test**: Adding or updating tests
- **chore**: Maintenance tasks

### Examples
```bash
feat(auth): add password reset functionality
fix(billing): resolve invoice calculation bug
docs(readme): update installation instructions
test(sales): add customer service tests
refactor(db): optimize query performance
chore(deps): update dependencies
```

### Scope Guidelines
- **auth**: Authentication and authorization
- **sales**: Customer and sales management
- **fleet**: Vehicle fleet management
- **billing**: Billing and invoicing
- **ops**: Operations management
- **web**: Frontend application
- **gateway**: API gateway
- **db**: Database and migrations
- **ci**: CI/CD pipeline changes

## Pull Request Process

### Before Submitting
1. **Run quality checks**:
   ```bash
   pnpm lint
   pnpm typecheck
   pnpm test
   ```

2. **Update documentation** if needed
3. **Add tests** for new functionality
4. **Update CHANGELOG.md** for significant changes

### PR Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests added/updated
```

### Review Process
1. **Automated checks** must pass (CI/CD)
2. **Code review** by at least one maintainer
3. **Testing** in development environment
4. **Approval** and merge

## Issue Reporting

### Bug Reports
Use the bug report template and include:
- **Environment details** (Node.js version, OS, Docker version)
- **Steps to reproduce**
- **Expected vs actual behavior**
- **Error messages and logs**
- **Screenshots** if applicable

### Feature Requests
Use the feature request template and include:
- **Problem description**
- **Proposed solution**
- **Alternative solutions considered**
- **Additional context**

### Security Issues
Report security vulnerabilities privately to maintainers. Do not create public issues for security problems.

## Development Tips

### Useful Commands
```bash
# Start specific service
pnpm --filter @a-realm/auth-service dev

# Build specific package
pnpm --filter @a-realm/config build

# Run database migrations
pnpm --filter @a-realm/db migrate:dev

# Generate Prisma client
pnpm --filter @a-realm/db prisma:generate

# View service logs
docker-compose logs auth-service

# Reset development environment
pnpm dev:down
docker-compose down -v
pnpm dev:up
```

### Debugging
- Use `console.log` for quick debugging (remove before committing)
- Use VS Code debugger for complex issues
- Check service logs for error details
- Use health endpoints to verify service status

### Performance Considerations
- Use database indexes for frequently queried fields
- Implement proper caching strategies
- Monitor memory usage in long-running processes
- Use connection pooling for database connections

## Questions and Support

- **Documentation**: Check README.md and /docs folder
- **Discussions**: Use GitHub Discussions for questions
- **Issues**: Create issues for bugs and feature requests
- **Community**: Join project discussions and help others

Thank you for contributing to A-ReALM! 🚀
