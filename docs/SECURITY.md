# Security Guide

## JWT Secret Management

### Overview
A-ReALM uses JWT tokens for authentication. Proper secret management is critical for security.

### Requirements
- JWT secrets must be at least 64 characters long
- Secrets must be cryptographically random
- Different secrets for JWT and refresh tokens
- Unique secrets per environment (dev/staging/prod)

### Generating Secrets

#### For Development
```bash
pnpm generate:secrets update
```

#### For Production
```bash
pnpm generate:secrets generate production
```

#### Manual Generation
```bash
# Using Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# Using OpenSSL
openssl rand -hex 32
```

### Environment Setup

1. **Never use default secrets in production**
2. **Store secrets in environment variables, not in code**
3. **Use secret management services in production** (AWS Secrets Manager, Azure Key Vault, etc.)

#### Production Environment Variables
```bash
export JWT_SECRET="your-64-char-secret-here"
export JWT_REFRESH_SECRET="your-different-64-char-secret-here"
```

### Security Checklist

- [ ] JWT secrets are 64+ characters
- [ ] Secrets are cryptographically random
- [ ] Different secrets for JWT and refresh tokens
- [ ] Secrets are stored securely (not in code)
- [ ] Secrets are unique per environment
- [ ] Secrets are rotated regularly
- [ ] Old secrets are properly invalidated

### Token Security

#### Token Expiration
- Access tokens: 15 minutes (900 seconds)
- Refresh tokens: 14 days (1209600 seconds)

#### Best Practices
- Use HTTPS only in production
- Implement proper token rotation
- Store tokens securely on client side
- Implement token blacklisting for logout
- Monitor for suspicious token usage

### Incident Response

If JWT secrets are compromised:

1. **Immediately rotate secrets**
2. **Invalidate all existing tokens**
3. **Force re-authentication for all users**
4. **Audit access logs**
5. **Update security documentation**

## CORS Security

### Overview
Cross-Origin Resource Sharing (CORS) controls which domains can access the API.

### Configuration
CORS is configured via the `ALLOWED_ORIGINS` environment variable:

```bash
# Development
ALLOWED_ORIGINS=http://localhost:5173

# Production (multiple origins)
ALLOWED_ORIGINS=https://app.example.com,https://admin.example.com
```

### Security Features
- **No wildcards allowed** - Prevents unauthorized access
- **Origin validation** - Only listed domains can access APIs
- **Credentials support** - Secure cookie/auth header handling
- **Preflight caching** - Optimized for production performance
- **Security logging** - Unauthorized requests are logged

### Best Practices
- Use HTTPS origins in production
- Limit origins to necessary domains only
- Never use wildcards (*) in production
- Monitor CORS violation logs
- Test CORS configuration before deployment

## Error Handling Security

### Overview
Comprehensive error handling prevents information leakage and improves security.

### Features
- **Centralized error handling** - Consistent error responses across all services
- **Error sanitization** - Sensitive information removed from error responses
- **Security logging** - All errors logged with context for monitoring
- **Process stability** - Graceful handling of uncaught exceptions
- **Request correlation** - Errors tracked with request IDs for debugging

### Error Response Format
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "requestId": "req-123",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

### Testing Error Handling
```bash
# Test error handling across all services
pnpm test:errors
```

### Compliance

This implementation supports:
- OWASP security guidelines
- SOC 2 compliance requirements
- GDPR data protection standards
- Industry-standard JWT practices
- CORS security best practices
- Secure error handling practices
