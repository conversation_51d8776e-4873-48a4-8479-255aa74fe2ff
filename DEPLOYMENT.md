# 🚀 A-ReALM Production Deployment Guide

This guide covers deploying A-ReALM to production environments including Docker, Kubernetes, and cloud platforms.

## 📋 Table of Contents

- [Pre-deployment Checklist](#pre-deployment-checklist)
- [Docker Deployment](#docker-deployment)
- [Kubernetes Deployment](#kubernetes-deployment)
- [Cloud Platform Deployment](#cloud-platform-deployment)
- [Database Setup](#database-setup)
- [SSL/TLS Configuration](#ssltls-configuration)
- [Monitoring Setup](#monitoring-setup)
- [Backup Strategy](#backup-strategy)
- [Scaling Considerations](#scaling-considerations)

## ✅ Pre-deployment Checklist

### Security Requirements
- [ ] Generate secure JWT secrets (64+ characters)
- [ ] Configure proper CORS origins
- [ ] Set up SSL/TLS certificates
- [ ] Review and configure rate limiting
- [ ] Enable security headers
- [ ] Set up proper logging levels

### Infrastructure Requirements
- [ ] PostgreSQL database (version 15+)
- [ ] Redis cache (version 7+)
- [ ] RabbitMQ message queue (version 3+)
- [ ] Load balancer/reverse proxy
- [ ] Monitoring and alerting system
- [ ] Backup solution

### Environment Configuration
- [ ] Production environment variables
- [ ] Database connection strings
- [ ] External service credentials
- [ ] Monitoring endpoints configured
- [ ] Log aggregation setup

## 🐳 Docker Deployment

### 1. Build Production Images
```bash
# Build all services
pnpm build

# Build Docker images
docker build -t a-realm/gateway:v1.0.0 apps/gateway
docker build -t a-realm/auth-service:v1.0.0 services/auth-service
docker build -t a-realm/sales-service:v1.0.0 services/sales-service
docker build -t a-realm/fleet-service:v1.0.0 services/fleet-service
docker build -t a-realm/billing-service:v1.0.0 services/billing-service
docker build -t a-realm/ops-service:v1.0.0 services/ops-service
docker build -t a-realm/web:v1.0.0 apps/web
```

### 2. Production Docker Compose
Create `docker-compose.prod.yml`:

```yaml
version: '3.9'
services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    
  rabbitmq:
    image: rabbitmq:3-management-alpine
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD}
    restart: unless-stopped
    
  auth-service:
    image: a-realm/auth-service:v1.0.0
    environment:
      NODE_ENV: production
      POSTGRES_URL: ${POSTGRES_URL}
      JWT_SECRET: ${JWT_SECRET}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET}
      REDIS_URL: redis://redis:6379
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    
  # Add other services...
  
  gateway:
    image: a-realm/gateway:v1.0.0
    ports:
      - "80:4000"
    environment:
      NODE_ENV: production
      AUTH_URL: http://auth-service:4010
      SALES_URL: http://sales-service:4020
    depends_on:
      - auth-service
      - sales-service
    restart: unless-stopped

volumes:
  postgres_data:
```

### 3. Deploy with Docker Compose
```bash
# Create production environment file
cp .env.example .env.production

# Edit production values
nano .env.production

# Deploy
docker-compose -f docker-compose.prod.yml --env-file .env.production up -d

# Check status
docker-compose -f docker-compose.prod.yml ps
```

## ☸️ Kubernetes Deployment

### 1. Create Namespace
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: a-realm
```

### 2. Configure Secrets
```yaml
# secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: a-realm-secrets
  namespace: a-realm
type: Opaque
stringData:
  postgres-url: "****************************************/a_realm"
  jwt-secret: "your-production-jwt-secret"
  jwt-refresh-secret: "your-production-refresh-secret"
```

### 3. Database Deployment
```yaml
# postgres.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: a-realm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        env:
        - name: POSTGRES_DB
          value: "a_realm"
        - name: POSTGRES_USER
          value: "postgres"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: a-realm-secrets
              key: postgres-password
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: a-realm
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
```

### 4. Service Deployments
```yaml
# auth-service.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  namespace: a-realm
spec:
  replicas: 2
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
    spec:
      containers:
      - name: auth-service
        image: a-realm/auth-service:v1.0.0
        ports:
        - containerPort: 4010
        env:
        - name: NODE_ENV
          value: "production"
        - name: POSTGRES_URL
          valueFrom:
            secretKeyRef:
              name: a-realm-secrets
              key: postgres-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: a-realm-secrets
              key: jwt-secret
        livenessProbe:
          httpGet:
            path: /healthz
            port: 4010
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /readyz
            port: 4010
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: auth-service
  namespace: a-realm
spec:
  selector:
    app: auth-service
  ports:
  - port: 4010
    targetPort: 4010
```

### 5. Ingress Configuration
```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: a-realm-ingress
  namespace: a-realm
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - yourdomain.com
    secretName: a-realm-tls
  rules:
  - host: yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gateway
            port:
              number: 4000
```

### 6. Deploy to Kubernetes
```bash
# Apply configurations
kubectl apply -f namespace.yaml
kubectl apply -f secrets.yaml
kubectl apply -f postgres.yaml
kubectl apply -f auth-service.yaml
kubectl apply -f gateway.yaml
kubectl apply -f ingress.yaml

# Check deployment status
kubectl get pods -n a-realm
kubectl get services -n a-realm
kubectl get ingress -n a-realm
```

## ☁️ Cloud Platform Deployment

### AWS ECS Deployment
```json
{
  "family": "a-realm-auth-service",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "auth-service",
      "image": "your-registry/a-realm/auth-service:v1.0.0",
      "portMappings": [
        {
          "containerPort": 4010,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "POSTGRES_URL",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:a-realm/postgres-url"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/a-realm-auth-service",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

### Google Cloud Run Deployment
```bash
# Build and push to Google Container Registry
docker build -t gcr.io/your-project/a-realm-auth-service:v1.0.0 services/auth-service
docker push gcr.io/your-project/a-realm-auth-service:v1.0.0

# Deploy to Cloud Run
gcloud run deploy auth-service \
  --image gcr.io/your-project/a-realm-auth-service:v1.0.0 \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars NODE_ENV=production \
  --set-secrets POSTGRES_URL=postgres-url:latest,JWT_SECRET=jwt-secret:latest
```

## 🗄️ Database Setup

### Production Database Configuration
```sql
-- Create production database
CREATE DATABASE a_realm_prod;
CREATE USER a_realm_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE a_realm_prod TO a_realm_user;

-- Configure connection limits
ALTER USER a_realm_user CONNECTION LIMIT 20;

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
```

### Run Migrations
```bash
# Set production database URL
export POSTGRES_URL="******************************************************/a_realm_prod"

# Run migrations
pnpm --filter @a-realm/db migrate:deploy

# Verify migration status
pnpm --filter @a-realm/db migrate:status
```

## 🔒 SSL/TLS Configuration

### Nginx Configuration
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /etc/ssl/certs/yourdomain.com.crt;
    ssl_certificate_key /etc/ssl/private/yourdomain.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    location / {
        proxy_pass http://localhost:5173;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/ {
        proxy_pass http://localhost:4000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Let's Encrypt Setup
```bash
# Install certbot
sudo apt install certbot python3-certbot-nginx

# Get certificate
sudo certbot --nginx -d yourdomain.com

# Set up auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 Monitoring Setup

### Prometheus Configuration
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'a-realm-services'
    static_configs:
      - targets: 
        - 'auth-service:4010'
        - 'sales-service:4020'
        - 'fleet-service:4030'
    metrics_path: '/metrics'
    scrape_interval: 30s
```

### Grafana Dashboard
Import the provided dashboard configuration from `infra/monitoring/grafana-dashboard.json`

### Health Check Monitoring
```bash
# Set up health check monitoring
curl -f http://localhost:4010/healthz || exit 1
curl -f http://localhost:4020/healthz || exit 1
curl -f http://localhost:4030/healthz || exit 1
```

## 💾 Backup Strategy

### Database Backups
```bash
#!/bin/bash
# backup-database.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/postgres"
DB_NAME="a_realm_prod"

# Create backup
pg_dump -h localhost -U a_realm_user -d $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/backup_$DATE.sql

# Remove backups older than 30 days
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +30 -delete
```

### Application Data Backups
```bash
# Backup uploaded files and logs
tar -czf /backups/app_data_$(date +%Y%m%d).tar.gz /app/uploads /app/logs

# Backup to cloud storage (AWS S3 example)
aws s3 cp /backups/ s3://your-backup-bucket/a-realm/ --recursive
```

## 📈 Scaling Considerations

### Horizontal Scaling
- Use load balancers for multiple service instances
- Implement session storage in Redis
- Use database connection pooling
- Configure auto-scaling based on CPU/memory metrics

### Database Scaling
- Set up read replicas for read-heavy workloads
- Implement database connection pooling
- Consider database sharding for large datasets
- Monitor query performance and optimize indexes

### Caching Strategy
- Use Redis for session storage and application caching
- Implement CDN for static assets
- Cache frequently accessed data
- Set appropriate cache expiration times

### Performance Monitoring
- Monitor response times and error rates
- Set up alerts for high CPU/memory usage
- Track database query performance
- Monitor disk space and network usage

---

## 🚨 Emergency Procedures

### Service Recovery
```bash
# Restart failed service
docker-compose restart auth-service

# Check service logs
docker-compose logs auth-service

# Scale service in Kubernetes
kubectl scale deployment auth-service --replicas=3 -n a-realm
```

### Database Recovery
```bash
# Restore from backup
gunzip backup_20240101_120000.sql.gz
psql -h localhost -U a_realm_user -d a_realm_prod < backup_20240101_120000.sql
```

### Rollback Deployment
```bash
# Docker rollback
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml up -d

# Kubernetes rollback
kubectl rollout undo deployment/auth-service -n a-realm
```

This deployment guide ensures your A-ReALM application runs securely and reliably in production environments. Always test deployments in staging environments before applying to production.
