name: CI

on:
  pull_request:
  push:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      - uses: pnpm/action-setup@v4
        with:
          version: 8
      - name: Install
        run: pnpm install --frozen-lockfile=false
      - name: Run linting
        run: pnpm run lint

      - name: Run type checking
        run: pnpm run typecheck

      - name: Run security audit
        run: pnpm audit --audit-level moderate

      - name: Run tests
        run: pnpm run test:ci
      - name: Build
        run: pnpm run build
      - name: Build Docker images
        run: |
          docker build -t a-realm/gateway:ci apps/gateway
          docker build -t a-realm/auth-service:ci services/auth-service
          docker build -t a-realm/sales-service:ci services/sales-service
          docker build -t a-realm/fleet-service:ci services/fleet-service
          docker build -t a-realm/billing-service:ci services/billing-service
          docker build -t a-realm/ops-service:ci services/ops-service

      - name: Run Docker security scan
        run: |
          # Install Trivy for container scanning
          sudo apt-get update
          sudo apt-get install wget apt-transport-https gnupg lsb-release
          wget -qO - https://aquasecurity.github.io/trivy-repo/deb/public.key | sudo apt-key add -
          echo "deb https://aquasecurity.github.io/trivy-repo/deb $(lsb_release -sc) main" | sudo tee -a /etc/apt/sources.list.d/trivy.list
          sudo apt-get update
          sudo apt-get install trivy

          # Scan Docker images for vulnerabilities
          trivy image --exit-code 1 --severity HIGH,CRITICAL a-realm/gateway:ci
          trivy image --exit-code 1 --severity HIGH,CRITICAL a-realm/auth-service:ci
          trivy image --exit-code 1 --severity HIGH,CRITICAL a-realm/sales-service:ci
