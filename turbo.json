{"$schema": "https://turbo.build/schema.json", "pipeline": {"dev": {"cache": false, "persistent": true}, "build": {"outputs": ["dist/**"], "dependsOn": ["^build"]}, "lint": {"outputs": [], "dependsOn": ["^build"]}, "lint:fix": {"outputs": [], "dependsOn": ["^build"]}, "format": {"outputs": []}, "format:check": {"outputs": []}, "test": {"dependsOn": ["^build"]}, "typecheck": {"dependsOn": ["^build"]}}}