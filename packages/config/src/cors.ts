import { CorsOptions } from 'cors';
import { config } from './index';

/**
 * Secure CORS configuration for A-ReALM services
 * 
 * This configuration:
 * - Only allows specific origins (no wildcards)
 * - Enables credentials only for trusted origins
 * - Sets secure headers for production
 * - Implements proper preflight handling
 */
export function createCorsConfig(): CorsOptions {
  const isDevelopment = config.nodeEnv === 'development';
  const isProduction = config.nodeEnv === 'production';

  return {
    // Only allow specific origins - never use true in production
    origin: (origin, callback) => {
      // Reject empty strings explicitly
      if (origin === '') {
        return callback(new Error('CORS: Empty origin not allowed'));
      }

      // Allow requests with no origin (mobile apps, Postman, tests, etc.) in development and test
      if (!origin && (isDevelopment || config.nodeEnv === 'test')) {
        return callback(null, true);
      }

      // Check if origin is in allowed list
      if (origin && config.allowedOrigins.includes(origin)) {
        return callback(null, true);
      }

      // Log security violation
      console.warn(`🚨 CORS: Blocked request from unauthorized origin: ${origin}`);
      
      // In development, show helpful error
      if (isDevelopment) {
        return callback(new Error(
          `CORS: Origin '${origin}' not allowed. Add it to ALLOWED_ORIGINS in .env file.`
        ));
      }

      // In production, generic error
      return callback(new Error('CORS: Origin not allowed'));
    },

    // Enable credentials (cookies, auth headers) only for trusted origins
    credentials: true,

    // Allowed methods - be restrictive
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],

    // Allowed headers
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-Org-Id',
      'X-Request-ID'
    ],

    // Expose headers that client can access
    exposedHeaders: [
      'X-Request-ID',
      'X-RateLimit-Limit',
      'X-RateLimit-Remaining',
      'X-RateLimit-Reset'
    ],

    // Cache preflight requests for 24 hours in production
    maxAge: isProduction ? 86400 : 300,

    // Handle preflight requests
    preflightContinue: false,
    optionsSuccessStatus: 204
  };
}

/**
 * Validate CORS configuration
 */
export function validateCorsConfig(): void {
  const origins = config.allowedOrigins;

  // Check for security issues
  if (origins.includes('*')) {
    throw new Error('SECURITY: Wildcard (*) origins are not allowed');
  }

  if (origins.some(origin => origin.includes('*'))) {
    throw new Error('SECURITY: Wildcard patterns in origins are not allowed');
  }

  // Warn about insecure origins in production
  if (config.nodeEnv === 'production') {
    const insecureOrigins = origins.filter(origin => 
      origin.startsWith('http://') && !origin.includes('localhost')
    );
    
    if (insecureOrigins.length > 0) {
      console.warn('⚠️  WARNING: HTTP origins in production:', insecureOrigins);
      console.warn('   Consider using HTTPS for security');
    }
  }

  if (config.nodeEnv !== 'production') {
    console.log('✅ CORS configuration validated');
    console.log('📋 Allowed origins:', origins);
  }
}
