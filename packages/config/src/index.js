"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateCorsConfig = exports.createCorsConfig = exports.config = void 0;
exports.loadConfig = loadConfig;
const dotenv_1 = __importDefault(require("dotenv"));
const joi_1 = __importDefault(require("joi"));
const path_1 = __importDefault(require("path"));
// Load .env from workspace root - try multiple locations
const envPaths = [
    path_1.default.resolve(process.cwd(), '.env'),
    path_1.default.resolve(process.cwd(), '../../.env'),
    path_1.default.resolve(__dirname, '../../../.env'),
    path_1.default.resolve(__dirname, '../../../../.env')
];
let envLoaded = false;
for (const envPath of envPaths) {
    const result = dotenv_1.default.config({ path: envPath });
    if (!result.error) {
        if (process.env.NODE_ENV !== 'production') {
            console.log(`✅ Environment loaded from: ${envPath}`);
        }
        envLoaded = true;
        break;
    }
}
if (!envLoaded && process.env.NODE_ENV !== 'production') {
    console.warn('⚠️  No .env file found in any of the expected locations:', envPaths);
}
const schema = joi_1.default.object({
    NODE_ENV: joi_1.default.string().valid('development', 'test', 'production').default('development'),
    LOG_LEVEL: joi_1.default.string().default('info'),
    POSTGRES_URL: joi_1.default.string().uri().allow('').default(''),
    REDIS_URL: joi_1.default.string().uri().allow('').default(''),
    RABBITMQ_URL: joi_1.default.string().uri().allow('').default(''),
    JWT_SECRET: joi_1.default.string().min(32).required().messages({
        'any.required': 'JWT_SECRET is required. Generate secure secrets using: pnpm generate:secrets',
        'string.min': 'JWT_SECRET must be at least 32 characters long for security'
    }),
    JWT_REFRESH_SECRET: joi_1.default.string().min(32).required().messages({
        'any.required': 'JWT_REFRESH_SECRET is required. Generate secure secrets using: pnpm generate:secrets',
        'string.min': 'JWT_REFRESH_SECRET must be at least 32 characters long for security'
    }),
    TOKEN_TTL_SECONDS: joi_1.default.number().integer().default(900),
    REFRESH_TTL_SECONDS: joi_1.default.number().integer().default(1209600),
    PORT: joi_1.default.number().integer().min(1).max(65535).default(3000),
    SERVICE_NAME: joi_1.default.string().default('service'),
    // Optional upstreams for gateway
    AUTH_URL: joi_1.default.string().uri().allow('').default(''),
    SALES_URL: joi_1.default.string().uri().allow('').default(''),
    FLEET_URL: joi_1.default.string().uri().allow('').default(''),
    BILLING_URL: joi_1.default.string().uri().allow('').default(''),
    OPS_URL: joi_1.default.string().uri().allow('').default(''),
    INTEGRATIONS_URL: joi_1.default.string().uri().allow('').default(''),
    // CORS Security
    ALLOWED_ORIGINS: joi_1.default.string().default('http://localhost:5173'),
    // OpenTelemetry
    OTEL_EXPORTER_OTLP_ENDPOINT: joi_1.default.string().uri().allow('').default(''),
    // Multi-tenant org default (demo)
    DEFAULT_ORG_ID: joi_1.default.string()
        .guid({ version: ['uuidv4', 'uuidv5'] })
        .default('00000000-0000-0000-0000-000000000001')
}).unknown(true);
function loadConfig(overrides) {
    const { value, error } = schema.validate({ ...process.env, ...overrides }, { abortEarly: false });
    if (error) {
        throw new Error(`Invalid environment configuration: ${error.message}`);
    }
    return {
        nodeEnv: value.NODE_ENV,
        logLevel: value.LOG_LEVEL,
        postgresUrl: value.POSTGRES_URL || undefined,
        redisUrl: value.REDIS_URL || undefined,
        rabbitmqUrl: value.RABBITMQ_URL || undefined,
        jwtSecret: value.JWT_SECRET,
        jwtRefreshSecret: value.JWT_REFRESH_SECRET,
        tokenTtlSeconds: value.TOKEN_TTL_SECONDS,
        refreshTtlSeconds: value.REFRESH_TTL_SECONDS,
        port: value.PORT,
        serviceName: value.SERVICE_NAME,
        authUrl: value.AUTH_URL || undefined,
        salesUrl: value.SALES_URL || undefined,
        fleetUrl: value.FLEET_URL || undefined,
        billingUrl: value.BILLING_URL || undefined,
        opsUrl: value.OPS_URL || undefined,
        integrationsUrl: value.INTEGRATIONS_URL || undefined,
        allowedOrigins: value.ALLOWED_ORIGINS.split(',').map((origin) => origin.trim()),
        otelOtlpEndpoint: value.OTEL_EXPORTER_OTLP_ENDPOINT || undefined,
        defaultOrgId: value.DEFAULT_ORG_ID
    };
}
exports.config = loadConfig();
// Export CORS utilities
var cors_1 = require("./cors");
Object.defineProperty(exports, "createCorsConfig", { enumerable: true, get: function () { return cors_1.createCorsConfig; } });
Object.defineProperty(exports, "validateCorsConfig", { enumerable: true, get: function () { return cors_1.validateCorsConfig; } });
//# sourceMappingURL=index.js.map