import dotenv from 'dotenv';
import Jo<PERSON> from 'joi';
import path from 'path';

// Load .env from workspace root - try multiple locations
const envPaths = [
  path.resolve(process.cwd(), '.env'),
  path.resolve(process.cwd(), '../../.env'),
  path.resolve(__dirname, '../../../.env'),
  path.resolve(__dirname, '../../../../.env')
];

let envLoaded = false;
for (const envPath of envPaths) {
  const result = dotenv.config({ path: envPath });
  if (!result.error) {
    if (process.env.NODE_ENV !== 'production') {
      console.log(`✅ Environment loaded from: ${envPath}`);
    }
    envLoaded = true;
    break;
  }
}

if (!envLoaded && process.env.NODE_ENV !== 'production') {
  console.warn('⚠️  No .env file found in any of the expected locations:', envPaths);
}

const schema = Joi.object({
  NODE_ENV: Joi.string().valid('development', 'test', 'production').default('development'),
  LOG_LEVEL: Joi.string().default('info'),

  POSTGRES_URL: Joi.string().uri().allow('').default(''),
  REDIS_URL: Joi.string().uri().allow('').default(''),
  RABBITMQ_URL: Joi.string().uri().allow('').default(''),

  JWT_SECRET: Joi.string().min(32).required().messages({
    'any.required': 'JWT_SECRET is required. Generate secure secrets using: pnpm generate:secrets',
    'string.min': 'JWT_SECRET must be at least 32 characters long for security'
  }),
  JWT_REFRESH_SECRET: Joi.string().min(32).required().messages({
    'any.required': 'JWT_REFRESH_SECRET is required. Generate secure secrets using: pnpm generate:secrets',
    'string.min': 'JWT_REFRESH_SECRET must be at least 32 characters long for security'
  }),
  TOKEN_TTL_SECONDS: Joi.number().integer().default(900),
  REFRESH_TTL_SECONDS: Joi.number().integer().default(1209600),

  PORT: Joi.number().integer().min(1).max(65535).default(3000),
  SERVICE_NAME: Joi.string().default('service'),

  // Optional upstreams for gateway
  AUTH_URL: Joi.string().uri().allow('').default(''),
  SALES_URL: Joi.string().uri().allow('').default(''),
  FLEET_URL: Joi.string().uri().allow('').default(''),
  BILLING_URL: Joi.string().uri().allow('').default(''),
  OPS_URL: Joi.string().uri().allow('').default(''),
  INTEGRATIONS_URL: Joi.string().uri().allow('').default(''),

  // CORS Security
  ALLOWED_ORIGINS: Joi.string().default('http://localhost:5173'),

  // OpenTelemetry
  OTEL_EXPORTER_OTLP_ENDPOINT: Joi.string().uri().allow('').default(''),

  // Multi-tenant org default (demo)
  DEFAULT_ORG_ID: Joi.string()
    .guid({ version: ['uuidv4', 'uuidv5'] })
    .default('00000000-0000-0000-0000-000000000001')
}).unknown(true);

type Config = {
  nodeEnv: 'development' | 'test' | 'production';
  logLevel: string;
  postgresUrl?: string;
  redisUrl?: string;
  rabbitmqUrl?: string;
  jwtSecret: string;
  jwtRefreshSecret: string;
  tokenTtlSeconds: number;
  refreshTtlSeconds: number;
  port: number;
  serviceName: string;
  // Upstreams
  authUrl?: string;
  salesUrl?: string;
  fleetUrl?: string;
  billingUrl?: string;
  opsUrl?: string;
  integrationsUrl?: string;
  // CORS
  allowedOrigins: string[];
  otelOtlpEndpoint?: string;
  defaultOrgId: string;
  exportDir?: string;
};

export function loadConfig(overrides?: Partial<Record<string, string>>): Config {
  const { value, error } = schema.validate({ ...process.env, ...overrides }, { abortEarly: false });
  if (error) {
    throw new Error(`Invalid environment configuration: ${error.message}`);
  }
  return {
    nodeEnv: value.NODE_ENV,
    logLevel: value.LOG_LEVEL,
    postgresUrl: value.POSTGRES_URL || undefined,
    redisUrl: value.REDIS_URL || undefined,
    rabbitmqUrl: value.RABBITMQ_URL || undefined,
    jwtSecret: value.JWT_SECRET,
    jwtRefreshSecret: value.JWT_REFRESH_SECRET,
    tokenTtlSeconds: value.TOKEN_TTL_SECONDS,
    refreshTtlSeconds: value.REFRESH_TTL_SECONDS,
    port: value.PORT,
    serviceName: value.SERVICE_NAME,
    authUrl: value.AUTH_URL || undefined,
    salesUrl: value.SALES_URL || undefined,
    fleetUrl: value.FLEET_URL || undefined,
    billingUrl: value.BILLING_URL || undefined,
    opsUrl: value.OPS_URL || undefined,
    integrationsUrl: value.INTEGRATIONS_URL || undefined,
    allowedOrigins: value.ALLOWED_ORIGINS.split(',').map((origin: string) => origin.trim()),
    otelOtlpEndpoint: value.OTEL_EXPORTER_OTLP_ENDPOINT || undefined,
    defaultOrgId: value.DEFAULT_ORG_ID
  };
}

export const config = loadConfig();

// Export CORS utilities
export { createCorsConfig, validateCorsConfig } from './cors';
