import { CorsOptions } from 'cors';
/**
 * Secure CORS configuration for A-ReALM services
 *
 * This configuration:
 * - Only allows specific origins (no wildcards)
 * - Enables credentials only for trusted origins
 * - Sets secure headers for production
 * - Implements proper preflight handling
 */
export declare function createCorsConfig(): CorsOptions;
/**
 * Validate CORS configuration
 */
export declare function validateCorsConfig(): void;
