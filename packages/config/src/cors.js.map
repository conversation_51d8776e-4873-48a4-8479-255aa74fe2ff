{"version": 3, "file": "cors.js", "sourceRoot": "", "sources": ["cors.ts"], "names": [], "mappings": ";;AAYA,4CAoEC;AAKD,gDA4BC;AAhHD,mCAAiC;AAEjC;;;;;;;;GAQG;AACH,SAAgB,gBAAgB;IAC9B,MAAM,aAAa,GAAG,cAAM,CAAC,OAAO,KAAK,aAAa,CAAC;IACvD,MAAM,YAAY,GAAG,cAAM,CAAC,OAAO,KAAK,YAAY,CAAC;IAErD,OAAO;QACL,6DAA6D;QAC7D,MAAM,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;YAC3B,kCAAkC;YAClC,IAAI,MAAM,KAAK,EAAE,EAAE,CAAC;gBAClB,OAAO,QAAQ,CAAC,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC,CAAC;YAC/D,CAAC;YAED,4FAA4F;YAC5F,IAAI,CAAC,MAAM,IAAI,CAAC,aAAa,IAAI,cAAM,CAAC,OAAO,KAAK,MAAM,CAAC,EAAE,CAAC;gBAC5D,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC9B,CAAC;YAED,qCAAqC;YACrC,IAAI,MAAM,IAAI,cAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACrD,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC9B,CAAC;YAED,yBAAyB;YACzB,OAAO,CAAC,IAAI,CAAC,sDAAsD,MAAM,EAAE,CAAC,CAAC;YAE7E,qCAAqC;YACrC,IAAI,aAAa,EAAE,CAAC;gBAClB,OAAO,QAAQ,CAAC,IAAI,KAAK,CACvB,iBAAiB,MAAM,wDAAwD,CAChF,CAAC,CAAC;YACL,CAAC;YAED,+BAA+B;YAC/B,OAAO,QAAQ,CAAC,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,sEAAsE;QACtE,WAAW,EAAE,IAAI;QAEjB,mCAAmC;QACnC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;QAE7D,kBAAkB;QAClB,cAAc,EAAE;YACd,QAAQ;YACR,kBAAkB;YAClB,cAAc;YACd,QAAQ;YACR,eAAe;YACf,UAAU;YACV,cAAc;SACf;QAED,wCAAwC;QACxC,cAAc,EAAE;YACd,cAAc;YACd,mBAAmB;YACnB,uBAAuB;YACvB,mBAAmB;SACpB;QAED,sDAAsD;QACtD,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG;QAElC,4BAA4B;QAC5B,iBAAiB,EAAE,KAAK;QACxB,oBAAoB,EAAE,GAAG;KAC1B,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,kBAAkB;IAChC,MAAM,OAAO,GAAG,cAAM,CAAC,cAAc,CAAC;IAEtC,4BAA4B;IAC5B,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QAC1B,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;IACpE,CAAC;IAED,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;QACjD,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;IAC5E,CAAC;IAED,4CAA4C;IAC5C,IAAI,cAAM,CAAC,OAAO,KAAK,YAAY,EAAE,CAAC;QACpC,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAC9C,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAC9D,CAAC;QAEF,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,OAAO,CAAC,IAAI,CAAC,0CAA0C,EAAE,eAAe,CAAC,CAAC;YAC1E,OAAO,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED,IAAI,cAAM,CAAC,OAAO,KAAK,YAAY,EAAE,CAAC;QACpC,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;AACH,CAAC"}