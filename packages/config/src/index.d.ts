type Config = {
    nodeEnv: 'development' | 'test' | 'production';
    logLevel: string;
    postgresUrl?: string;
    redisUrl?: string;
    rabbitmqUrl?: string;
    jwtSecret: string;
    jwtRefreshSecret: string;
    tokenTtlSeconds: number;
    refreshTtlSeconds: number;
    port: number;
    serviceName: string;
    authUrl?: string;
    salesUrl?: string;
    fleetUrl?: string;
    billingUrl?: string;
    opsUrl?: string;
    integrationsUrl?: string;
    allowedOrigins: string[];
    otelOtlpEndpoint?: string;
    defaultOrgId: string;
    exportDir?: string;
};
export declare function loadConfig(overrides?: Partial<Record<string, string>>): Config;
export declare const config: Config;
export { createCorsConfig, validateCorsConfig } from './cors';
