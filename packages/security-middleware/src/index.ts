import { Request, Response, NextFunction } from 'express';
import helmet from 'helmet';
import { createLogger } from '@a-realm/logger';

const logger = createLogger('security');

export interface SecurityConfig {
  enforceHttps?: boolean;
  allowedOrigins?: string[];
  contentSecurityPolicy?: {
    directives?: Record<string, string[]>;
    reportOnly?: boolean;
  };
  hsts?: {
    maxAge?: number;
    includeSubDomains?: boolean;
    preload?: boolean;
  };
  rateLimiting?: {
    windowMs?: number;
    max?: number;
  };
}

/**
 * HTTPS enforcement middleware
 */
export function enforceHttps(req: Request, res: Response, next: NextFunction): void {
  if (process.env.NODE_ENV === 'production' && !req.secure && req.get('x-forwarded-proto') !== 'https') {
    logger.warn('Redirecting HTTP request to HTTPS', {
      url: req.url,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    return res.redirect(301, `https://${req.get('host')}${req.url}`);
  }
  
  next();
}

/**
 * Security headers middleware using Helmet
 */
export function securityHeaders(config: SecurityConfig = {}) {
  const {
    contentSecurityPolicy = {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "https:"],
        scriptSrc: ["'self'"],
        connectSrc: ["'self'"],
        frameSrc: ["'none'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        manifestSrc: ["'self'"],
        workerSrc: ["'self'"],
        upgradeInsecureRequests: []
      }
    },
    hsts = {
      maxAge: 31536000, // 1 year
      includeSubDomains: true,
      preload: true
    }
  } = config;

  return helmet({
    // Content Security Policy
    contentSecurityPolicy: {
      directives: contentSecurityPolicy.directives,
      reportOnly: contentSecurityPolicy.reportOnly || false
    },
    
    // HTTP Strict Transport Security
    hsts: process.env.NODE_ENV === 'production' ? hsts : false,
    
    // X-Frame-Options
    frameguard: { action: 'deny' },
    
    // X-Content-Type-Options
    noSniff: true,
    
    // X-XSS-Protection
    xssFilter: true,
    
    // Referrer Policy
    referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
    
    // Permissions Policy
    permissionsPolicy: {
      camera: [],
      microphone: [],
      geolocation: [],
      payment: [],
      usb: [],
      magnetometer: [],
      gyroscope: [],
      accelerometer: []
    },
    
    // Remove X-Powered-By header
    hidePoweredBy: true,
    
    // DNS Prefetch Control
    dnsPrefetchControl: { allow: false },
    
    // IE No Open
    ieNoOpen: true,
    
    // Don't infer MIME type
    noSniff: true,
    
    // Origin Agent Cluster
    originAgentCluster: true,
    
    // Cross Origin Embedder Policy
    crossOriginEmbedderPolicy: false, // Set to true if needed
    
    // Cross Origin Opener Policy
    crossOriginOpenerPolicy: { policy: 'same-origin' },
    
    // Cross Origin Resource Policy
    crossOriginResourcePolicy: { policy: 'cross-origin' }
  });
}

/**
 * Request size limiting middleware
 */
export function requestSizeLimiter(maxSize: string = '1mb') {
  return (req: Request, res: Response, next: NextFunction): void => {
    const contentLength = req.get('content-length');
    
    if (contentLength) {
      const sizeInBytes = parseInt(contentLength, 10);
      const maxSizeInBytes = parseSize(maxSize);
      
      if (sizeInBytes > maxSizeInBytes) {
        logger.warn('Request size limit exceeded', {
          contentLength: sizeInBytes,
          maxSize: maxSizeInBytes,
          ip: req.ip,
          url: req.url
        });
        
        return res.status(413).json({
          error: 'Request entity too large',
          message: `Request size exceeds limit of ${maxSize}`,
          timestamp: new Date().toISOString()
        });
      }
    }
    
    next();
  };
}

/**
 * Security event logger middleware
 */
export function securityLogger() {
  return (req: Request, res: Response, next: NextFunction): void => {
    // Log suspicious patterns
    const suspiciousPatterns = [
      /\.\./,  // Directory traversal
      /<script/i,  // XSS attempts
      /union.*select/i,  // SQL injection
      /javascript:/i,  // JavaScript protocol
      /vbscript:/i,  // VBScript protocol
      /data:.*base64/i  // Data URLs with base64
    ];
    
    const url = req.url;
    const userAgent = req.get('User-Agent') || '';
    const referer = req.get('Referer') || '';
    
    for (const pattern of suspiciousPatterns) {
      if (pattern.test(url) || pattern.test(userAgent) || pattern.test(referer)) {
        logger.warn('Suspicious request detected', {
          pattern: pattern.toString(),
          url,
          userAgent,
          referer,
          ip: req.ip,
          method: req.method
        });
        break;
      }
    }
    
    next();
  };
}

/**
 * API key validation middleware
 */
export function validateApiKey(validApiKeys: string[]) {
  return (req: Request, res: Response, next: NextFunction): void => {
    const apiKey = req.get('X-API-Key') || req.query.apiKey as string;
    
    if (!apiKey) {
      logger.warn('Missing API key', {
        ip: req.ip,
        url: req.url,
        userAgent: req.get('User-Agent')
      });
      
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'API key required',
        timestamp: new Date().toISOString()
      });
    }
    
    if (!validApiKeys.includes(apiKey)) {
      logger.warn('Invalid API key', {
        apiKey: apiKey.substring(0, 8) + '...',
        ip: req.ip,
        url: req.url,
        userAgent: req.get('User-Agent')
      });
      
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid API key',
        timestamp: new Date().toISOString()
      });
    }
    
    next();
  };
}

/**
 * Helper function to parse size strings like "1mb", "500kb"
 */
function parseSize(size: string): number {
  const units: Record<string, number> = {
    b: 1,
    kb: 1024,
    mb: 1024 * 1024,
    gb: 1024 * 1024 * 1024
  };
  
  const match = size.toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/);
  
  if (!match) {
    throw new Error(`Invalid size format: ${size}`);
  }
  
  const value = parseFloat(match[1]);
  const unit = match[2] || 'b';
  
  return Math.floor(value * units[unit]);
}

/**
 * Complete security middleware setup
 */
export function setupSecurity(config: SecurityConfig = {}) {
  const middlewares = [];
  
  // HTTPS enforcement (production only)
  if (config.enforceHttps !== false) {
    middlewares.push(enforceHttps);
  }
  
  // Security headers
  middlewares.push(securityHeaders(config));
  
  // Request size limiting
  middlewares.push(requestSizeLimiter());
  
  // Security logging
  middlewares.push(securityLogger());
  
  return middlewares;
}

// Export individual middlewares and utilities
export {
  helmet
};
