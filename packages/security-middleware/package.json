{"name": "@a-realm/security-middleware", "version": "0.1.0", "private": true, "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc -p tsconfig.json", "dev": "tsc -p tsconfig.json --watch", "test": "jest", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "dependencies": {"helmet": "^7.1.0", "express": "^4.19.2", "@a-realm/logger": "workspace:*"}, "devDependencies": {"@types/express": "^4.17.21", "typescript": "^5.4.5"}}