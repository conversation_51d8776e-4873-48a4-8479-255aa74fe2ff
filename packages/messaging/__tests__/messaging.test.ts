import { publishEvent, consumeEvents } from '../src';

describe('Messaging (RabbitMQ optional)', () => {
  const orig = process.env.RABBITMQ_URL;

  beforeAll(() => {
    delete process.env.RABBITMQ_URL; // ensure disabled
  });

  afterAll(() => {
    if (orig) process.env.RABBITMQ_URL = orig;
  });

  it('publishEvent should no-op and not throw when RABBITMQ_URL is missing', async () => {
    await expect(publishEvent('test.key', { type: 'Test', timestamp: new Date().toISOString(), data: {} })).resolves.not.toThrow;
  });

  it('consumeEvents should return false when messaging is disabled', async () => {
    const res: any = await consumeEvents('test.#', async () => {});
    expect(res).toBe(false);
  });
});

