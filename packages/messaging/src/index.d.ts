export type DomainEvent = {
    type: string;
    timestamp: string;
    data: any;
};
/**
 * Get or create a RabbitMQ channel. Gracefully disables messaging when
 * RABBITMQ_URL is not configured or a connection cannot be established.
 */
export declare function getChannel(): Promise<any | null>;
/**
 * Publish a domain event. No-ops when messaging is disabled.
 */
export declare function publishEvent(routingKey: string, event: DomainEvent, options?: any): Promise<any>;
/**
 * Consume events matching a binding key. Returns false when messaging is disabled.
 */
export declare function consumeEvents(bindingKey: string, handler: (event: DomainEvent) => Promise<void> | void): Promise<boolean>;
