import amqp from 'amqplib';
import { config } from '@a-realm/config';
import { createLogger } from '@a-realm/logger';

let connection: any;
let channel: any;
let disabled = false;
const EXCHANGE = 'domain.events';
const logger = createLogger('messaging');

export type DomainEvent = {
  type: string;
  timestamp: string;
  data: any;
};

/**
 * Get or create a RabbitMQ channel. Gracefully disables messaging when
 * RABBITMQ_URL is not configured or a connection cannot be established.
 */
export async function getChannel(): Promise<any | null> {
  if (disabled) return null;
  if (channel) return channel;

  if (!config.rabbitmqUrl) {
    logger.warn('Messaging disabled: RABBITMQ_URL not configured');
    disabled = true;
    return null;
  }

  try {
    connection = await amqp.connect(config.rabbitmqUrl);
    channel = await connection.createChannel();
    await channel.assertExchange(EXCHANGE, 'topic', { durable: true });

    // Handle connection/channel close to avoid crashes
    connection.on('close', () => {
      logger.warn('RabbitMQ connection closed; disabling messaging');
      disabled = true;
      connection = null;
      channel = null;
    });
    connection.on('error', (err: any) => {
      logger.error('RabbitMQ connection error', { error: err?.message });
    });
    return channel;
  } catch (err: any) {
    logger.warn('Messaging disabled: failed to connect to RabbitMQ', { error: err?.message });
    disabled = true;
    connection = null;
    channel = null;
    return null;
  }
}

/**
 * Publish a domain event. No-ops when messaging is disabled.
 */
export async function publishEvent(routingKey: string, event: DomainEvent, options?: any) {
  const ch = await getChannel();
  if (!ch) {
    logger.info('Event publish skipped (messaging disabled)', { routingKey, type: event?.type });
    return false;
  }
  try {
    const payload = Buffer.from(JSON.stringify(event));
    return ch.publish(EXCHANGE, routingKey, payload, { contentType: 'application/json', persistent: true, ...(options || {}) });
  } catch (e: any) {
    logger.error('Failed to publish event', { routingKey, error: e?.message });
    return false;
  }
}

/**
 * Consume events matching a binding key. Returns false when messaging is disabled.
 */
export async function consumeEvents(bindingKey: string, handler: (event: DomainEvent) => Promise<void> | void) {
  const ch = await getChannel();
  if (!ch) {
    logger.info('Event consumer not started (messaging disabled)', { bindingKey });
    return false;
  }
  const q = await ch.assertQueue('', { exclusive: true, durable: false, autoDelete: true });
  await ch.bindQueue(q.queue, EXCHANGE, bindingKey);
  await ch.consume(q.queue, async (msg: any) => {
    if (!msg) return;
    try {
      const content = JSON.parse(msg.content.toString());
      await handler(content);
      ch.ack(msg);
    } catch (e) {
      ch.nack(msg, false, false); // dead-letter
    }
  });
  return true;
}
