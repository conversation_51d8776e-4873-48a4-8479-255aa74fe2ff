"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getChannel = getChannel;
exports.publishEvent = publishEvent;
exports.consumeEvents = consumeEvents;
const amqplib_1 = __importDefault(require("amqplib"));
const config_1 = require("@a-realm/config");
const logger_1 = require("@a-realm/logger");
let connection;
let channel;
let disabled = false;
const EXCHANGE = 'domain.events';
const logger = (0, logger_1.createLogger)('messaging');
/**
 * Get or create a RabbitMQ channel. Gracefully disables messaging when
 * RABBITMQ_URL is not configured or a connection cannot be established.
 */
async function getChannel() {
    if (disabled)
        return null;
    if (channel)
        return channel;
    if (!config_1.config.rabbitmqUrl) {
        logger.warn('Messaging disabled: RABBITMQ_URL not configured');
        disabled = true;
        return null;
    }
    try {
        connection = await amqplib_1.default.connect(config_1.config.rabbitmqUrl);
        channel = await connection.createChannel();
        await channel.assertExchange(EXCHANGE, 'topic', { durable: true });
        // Handle connection/channel close to avoid crashes
        connection.on('close', () => {
            logger.warn('RabbitMQ connection closed; disabling messaging');
            disabled = true;
            connection = null;
            channel = null;
        });
        connection.on('error', (err) => {
            logger.error('RabbitMQ connection error', { error: err?.message });
        });
        return channel;
    }
    catch (err) {
        logger.warn('Messaging disabled: failed to connect to RabbitMQ', { error: err?.message });
        disabled = true;
        connection = null;
        channel = null;
        return null;
    }
}
/**
 * Publish a domain event. No-ops when messaging is disabled.
 */
async function publishEvent(routingKey, event, options) {
    const ch = await getChannel();
    if (!ch) {
        logger.info('Event publish skipped (messaging disabled)', { routingKey, type: event?.type });
        return false;
    }
    try {
        const payload = Buffer.from(JSON.stringify(event));
        return ch.publish(EXCHANGE, routingKey, payload, { contentType: 'application/json', persistent: true, ...(options || {}) });
    }
    catch (e) {
        logger.error('Failed to publish event', { routingKey, error: e?.message });
        return false;
    }
}
/**
 * Consume events matching a binding key. Returns false when messaging is disabled.
 */
async function consumeEvents(bindingKey, handler) {
    const ch = await getChannel();
    if (!ch) {
        logger.info('Event consumer not started (messaging disabled)', { bindingKey });
        return false;
    }
    const q = await ch.assertQueue('', { exclusive: true, durable: false, autoDelete: true });
    await ch.bindQueue(q.queue, EXCHANGE, bindingKey);
    await ch.consume(q.queue, async (msg) => {
        if (!msg)
            return;
        try {
            const content = JSON.parse(msg.content.toString());
            await handler(content);
            ch.ack(msg);
        }
        catch (e) {
            ch.nack(msg, false, false); // dead-letter
        }
    });
    return true;
}
//# sourceMappingURL=index.js.map