// Generate TypeScript types from running service OpenAPI docs.
// Ensure services are running locally before executing.
// Usage: pnpm --filter @a-realm/api-types generate

import openapiTS from 'openapi-typescript';
import { writeFile } from 'node:fs/promises';

const targets = [
  { name: 'sales', url: process.env.SALES_OPENAPI_URL || 'http://localhost:4020/docs-json' },
  { name: 'fleet', url: process.env.FLEET_OPENAPI_URL || 'http://localhost:4030/docs-json' },
  { name: 'billing', url: process.env.BILLING_OPENAPI_URL || 'http://localhost:4040/docs-json' },
  { name: 'ops', url: process.env.OPS_OPENAPI_URL || 'http://localhost:4050/docs-json' }
];

for (const t of targets) {
  const types = await openapiTS(t.url, { transform: (schemaObject) => schemaObject });
  await writeFile(new URL(`../dist/${t.name}.d.ts`, import.meta.url), types);
  // eslint-disable-next-line no-console
  console.log(`Generated ${t.name} types`);
}

