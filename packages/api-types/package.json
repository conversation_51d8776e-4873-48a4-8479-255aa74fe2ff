{"name": "@a-realm/api-types", "version": "0.1.0", "private": true, "types": "dist/index.d.ts", "scripts": {"format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "generate": "node scripts/generate.js", "lint": "echo 'No source files to lint in api-types'", "lint:fix": "echo 'No source files to lint in api-types'"}, "devDependencies": {"openapi-typescript": "^7.4.2"}}