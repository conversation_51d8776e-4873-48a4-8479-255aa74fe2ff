import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';
import { createClient } from 'redis';
import type { Request, Response, NextFunction } from 'express';

import { config } from '@a-realm/config';
import { createLogger } from '@a-realm/logger';

const logger = createLogger('rate-limiter');

// Redis client for distributed rate limiting
let redisClient: ReturnType<typeof createClient> | null = null;

/**
 * Initialize Redis client for distributed rate limiting
 */
export async function initializeRateLimiter() {
  if (config.redisUrl) {
    try {
      redisClient = createClient({ url: config.redisUrl });
      await redisClient.connect();
      logger.info('Rate limiter connected to Redis for distributed limiting');
    } catch (error) {
      logger.warn('Failed to connect to Redis, using in-memory rate limiting', { error });
      redisClient = null;
    }
  } else {
    logger.info('No Redis URL configured, using in-memory rate limiting');
  }
}

/**
 * Redis store for express-rate-limit
 */
class RedisStore {
  constructor(private client: ReturnType<typeof createClient>) {}

  async increment(key: string): Promise<{ totalHits: number; resetTime?: Date }> {
    const multi = this.client.multi();
    multi.incr(key);
    multi.expire(key, 60); // 1 minute window
    const results = await multi.exec();
    
    return {
      totalHits: results?.[0] as number || 1,
      resetTime: new Date(Date.now() + 60000)
    };
  }

  async decrement(key: string): Promise<void> {
    await this.client.decr(key);
  }

  async resetKey(key: string): Promise<void> {
    await this.client.del(key);
  }
}

/**
 * Create rate limiter middleware with configurable options
 */
export function createRateLimiter(options: {
  windowMs?: number;
  max?: number;
  message?: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: Request) => string;
}) {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    max = 100, // requests per window
    message = 'Too many requests from this IP, please try again later',
    skipSuccessfulRequests = false,
    skipFailedRequests = false,
    keyGenerator
  } = options;

  const limiterConfig: any = {
    windowMs,
    max,
    message: {
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message,
        retryAfter: Math.ceil(windowMs / 1000)
      }
    },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests,
    skipFailedRequests,
    keyGenerator: keyGenerator || ((req: Request) => {
      // Use IP + User ID if authenticated, otherwise just IP
      const ip = req.ip || req.connection.remoteAddress || 'unknown';
      const userId = (req as any).user?.sub || '';
      return `${ip}:${userId}`;
    }),
    handler: (req: Request, res: Response) => {
      logger.warn('Rate limit exceeded', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.path,
        method: req.method
      });
      
      res.status(429).json({
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message,
          retryAfter: Math.ceil(windowMs / 1000),
          timestamp: new Date().toISOString()
        }
      });
    }
  };

  // Use Redis store if available
  if (redisClient) {
    limiterConfig.store = new RedisStore(redisClient);
  }

  return rateLimit(limiterConfig);
}

/**
 * Create slow down middleware to gradually increase response time
 */
export function createSlowDown(options: {
  windowMs?: number;
  delayAfter?: number;
  delayMs?: number;
  maxDelayMs?: number;
}) {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    delayAfter = 50, // Start slowing down after 50 requests
    delayMs = 100, // Add 100ms delay per request
    maxDelayMs = 5000 // Maximum 5 second delay
  } = options;

  return slowDown({
    windowMs,
    delayAfter,
    delayMs,
    maxDelayMs,
    skipSuccessfulRequests: false,
    skipFailedRequests: false
  });
}

/**
 * Strict rate limiter for authentication endpoints
 */
export const authRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: 'Too many authentication attempts, please try again later',
  skipSuccessfulRequests: true // Don't count successful logins
});

/**
 * General API rate limiter
 */
export const apiRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per window
  message: 'Too many API requests, please try again later'
});

/**
 * Strict rate limiter for sensitive operations
 */
export const sensitiveRateLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 requests per hour
  message: 'Too many sensitive operations, please try again later'
});

/**
 * Cleanup function to close Redis connection
 */
export async function closeRateLimiter() {
  if (redisClient) {
    await redisClient.quit();
    redisClient = null;
    logger.info('Rate limiter Redis connection closed');
  }
}
