{"name": "@a-realm/test-utils", "version": "0.1.0", "private": true, "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc -p tsconfig.json", "dev": "tsx watch src/index.ts", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "typecheck": "tsc -p tsconfig.json"}, "dependencies": {"@a-realm/config": "workspace:*", "@a-realm/db": "workspace:*", "@a-realm/error-handler": "workspace:*", "@a-realm/logger": "workspace:*", "@prisma/client": "^5.15.0", "supertest": "^7.0.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3"}, "devDependencies": {"@types/supertest": "^6.0.2", "@types/jsonwebtoken": "^9.0.6", "@types/bcryptjs": "^2.4.6", "tsx": "^4.11.0", "typescript": "^5.4.5"}}