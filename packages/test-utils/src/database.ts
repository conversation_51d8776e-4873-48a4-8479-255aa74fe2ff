import { PrismaClient } from '@prisma/client';
import { execSync } from 'child_process';
import { randomBytes } from 'crypto';

/**
 * Test database utilities for isolated testing
 */
export class TestDatabase {
  private static instance: TestDatabase;
  private prisma: PrismaClient | null = null;
  private databaseUrl: string | null = null;

  static getInstance(): TestDatabase {
    if (!TestDatabase.instance) {
      TestDatabase.instance = new TestDatabase();
    }
    return TestDatabase.instance;
  }

  /**
   * Create a test database with unique name
   */
  async setup(): Promise<PrismaClient> {
    if (this.prisma) {
      return this.prisma;
    }

    // Generate unique database name for this test run
    const testId = randomBytes(8).toString('hex');
    const testDbName = `test_a_realm_${testId}`;
    
    // Get base database URL
    const baseUrl = process.env.POSTGRES_URL || 'postgresql://postgres:postgres@localhost:5432/a_realm';
    const baseUrlObj = new URL(baseUrl);
    
    // Create test database URL
    this.databaseUrl = `${baseUrlObj.protocol}//${baseUrlObj.username}:${baseUrlObj.password}@${baseUrlObj.host}/${testDbName}`;

    try {
      // Create test database
      const createDbUrl = `${baseUrlObj.protocol}//${baseUrlObj.username}:${baseUrlObj.password}@${baseUrlObj.host}/postgres`;
      execSync(`psql "${createDbUrl}" -c "CREATE DATABASE ${testDbName};"`, { stdio: 'pipe' });

      // Initialize Prisma client with test database
      this.prisma = new PrismaClient({
        datasources: {
          db: {
            url: this.databaseUrl
          }
        },
        log: ['error']
      });

      // Run migrations
      process.env.DATABASE_URL = this.databaseUrl;
      execSync(`npx prisma db push --force-reset`, {
        stdio: 'pipe',
        cwd: process.cwd()
      });

      await this.prisma.$connect();
      
      console.log(`✅ Test database created: ${testDbName}`);
      return this.prisma;
    } catch (error) {
      console.error('Failed to setup test database:', error);
      throw error;
    }
  }

  /**
   * Clean up test database
   */
  async teardown(): Promise<void> {
    if (this.prisma) {
      await this.prisma.$disconnect();
      this.prisma = null;
    }

    if (this.databaseUrl) {
      try {
        const urlObj = new URL(this.databaseUrl);
        const dbName = urlObj.pathname.slice(1);
        const baseUrl = `${urlObj.protocol}//${urlObj.username}:${urlObj.password}@${urlObj.host}/postgres`;
        
        execSync(`psql "${baseUrl}" -c "DROP DATABASE IF EXISTS ${dbName};"`, { stdio: 'pipe' });
        console.log(`✅ Test database cleaned up: ${dbName}`);
      } catch (error) {
        console.warn('Failed to cleanup test database:', error);
      }
      
      this.databaseUrl = null;
    }
  }

  /**
   * Get current Prisma client
   */
  getPrisma(): PrismaClient {
    if (!this.prisma) {
      throw new Error('Test database not initialized. Call setup() first.');
    }
    return this.prisma;
  }

  /**
   * Reset database to clean state
   */
  async reset(): Promise<void> {
    if (!this.prisma) {
      throw new Error('Test database not initialized. Call setup() first.');
    }

    // Delete all data in reverse dependency order
    await this.prisma.userRole.deleteMany();
    await this.prisma.receipt.deleteMany();
    await this.prisma.invoice.deleteMany();
    await this.prisma.agreement.deleteMany();
    await this.prisma.reservation.deleteMany();
    await this.prisma.vehicle.deleteMany();
    await this.prisma.customer.deleteMany();
    await this.prisma.user.deleteMany();
    await this.prisma.role.deleteMany();
    await this.prisma.org.deleteMany();
  }
}

/**
 * Global test database instance
 */
export const testDb = TestDatabase.getInstance();
