import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { config } from '@a-realm/config';
import { testDb } from './database';

/**
 * Authentication utilities for testing
 */
export class TestAuth {
  /**
   * Create a test user with specified roles
   */
  static async createUser(options: {
    email?: string;
    password?: string;
    roles?: string[];
    orgId?: string;
  } = {}) {
    const {
      email = '<EMAIL>',
      password = 'password123',
      roles = ['user'],
      orgId
    } = options;

    const prisma = testDb.getPrisma();

    // Create or get organization
    let org;
    if (orgId) {
      org = await prisma.org.findUnique({ where: { id: orgId } });
      if (!org) {
        throw new Error(`Organization with id ${orgId} not found`);
      }
    } else {
      org = await prisma.org.create({
        data: {
          name: 'Test Organization'
        }
      });
    }

    // Create user
    const passwordHash = await bcrypt.hash(password, 10);
    const user = await prisma.user.create({
      data: {
        orgId: org.id,
        email,
        passwordHash
      }
    });

    // Create roles and assign to user
    for (const roleName of roles) {
      let role = await prisma.role.findUnique({
        where: { orgId_name: { orgId: org.id, name: roleName } }
      });

      if (!role) {
        const permissions = roleName === 'admin' 
          ? ['*'] 
          : ['agreements:read', 'vehicles:read'];
          
        role = await prisma.role.create({
          data: {
            orgId: org.id,
            name: roleName,
            permissions
          }
        });
      }

      await prisma.userRole.create({
        data: {
          userId: user.id,
          roleId: role.id
        }
      });
    }

    return { user, org };
  }

  /**
   * Generate JWT token for user
   */
  static generateToken(user: { id: string; email: string }, roles: string[] = ['user']): string {
    return jwt.sign(
      { 
        sub: user.id, 
        email: user.email,
        roles 
      },
      config.jwtSecret,
      { expiresIn: config.tokenTtlSeconds }
    );
  }

  /**
   * Generate refresh token for user
   */
  static generateRefreshToken(user: { id: string; email: string }): string {
    return jwt.sign(
      { 
        sub: user.id,
        email: user.email
      },
      config.jwtRefreshSecret,
      { expiresIn: config.refreshTtlSeconds }
    );
  }

  /**
   * Create user and return auth tokens
   */
  static async createUserWithTokens(options: {
    email?: string;
    password?: string;
    roles?: string[];
    orgId?: string;
  } = {}) {
    const { user, org } = await this.createUser(options);
    const roles = options.roles || ['user'];
    
    const accessToken = this.generateToken(user, roles);
    const refreshToken = this.generateRefreshToken(user);

    return {
      user,
      org,
      accessToken,
      refreshToken,
      authHeader: `Bearer ${accessToken}`
    };
  }

  /**
   * Verify JWT token
   */
  static verifyToken(token: string): any {
    return jwt.verify(token, config.jwtSecret);
  }

  /**
   * Verify refresh token
   */
  static verifyRefreshToken(token: string): any {
    return jwt.verify(token, config.jwtRefreshSecret);
  }
}
