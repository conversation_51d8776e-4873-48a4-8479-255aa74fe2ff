import { Express } from 'express';
import request, { Test } from 'supertest';

/**
 * Test server utilities for API testing
 */
export class TestServer {
  private app: Express;

  constructor(app: Express) {
    this.app = app;
  }

  /**
   * Make authenticated GET request
   */
  async get(path: string, authHeader?: string): Promise<Test> {
    const req = request(this.app).get(path);
    if (authHeader) {
      req.set('Authorization', authHeader);
    }
    return req;
  }

  /**
   * Make authenticated POST request
   */
  async post(path: string, data?: any, authHeader?: string): Promise<Test> {
    const req = request(this.app).post(path);
    if (authHeader) {
      req.set('Authorization', authHeader);
    }
    if (data) {
      req.send(data);
    }
    return req;
  }

  /**
   * Make authenticated PUT request
   */
  async put(path: string, data?: any, authHeader?: string): Promise<Test> {
    const req = request(this.app).put(path);
    if (authHeader) {
      req.set('Authorization', authHeader);
    }
    if (data) {
      req.send(data);
    }
    return req;
  }

  /**
   * Make authenticated DELETE request
   */
  async delete(path: string, authHeader?: string): Promise<Test> {
    const req = request(this.app).delete(path);
    if (authHeader) {
      req.set('Authorization', authHeader);
    }
    return req;
  }

  /**
   * Make authenticated PATCH request
   */
  async patch(path: string, data?: any, authHeader?: string): Promise<Test> {
    const req = request(this.app).patch(path);
    if (authHeader) {
      req.set('Authorization', authHeader);
    }
    if (data) {
      req.send(data);
    }
    return req;
  }

  /**
   * Test health endpoint
   */
  async testHealth(): Promise<Test> {
    return this.get('/health');
  }

  /**
   * Test readiness endpoint
   */
  async testReadiness(): Promise<Test> {
    return this.get('/readyz');
  }

  /**
   * Test metrics endpoint
   */
  async testMetrics(): Promise<Test> {
    return this.get('/metrics');
  }

  /**
   * Test 404 handling
   */
  async test404(): Promise<Test> {
    return this.get('/nonexistent-endpoint');
  }

  /**
   * Test CORS headers
   */
  async testCors(origin: string = 'http://localhost:5173'): Promise<Test> {
    return request(this.app)
      .options('/health')
      .set('Origin', origin)
      .set('Access-Control-Request-Method', 'GET');
  }
}

/**
 * Create test server instance
 */
export function createTestServer(app: Express): TestServer {
  return new TestServer(app);
}
