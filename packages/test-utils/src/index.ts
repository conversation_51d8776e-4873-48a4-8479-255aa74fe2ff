// Export all test utilities
export { TestDatabase, testDb } from './database';
export { TestAuth } from './auth';
export { TestFixtures } from './fixtures';
export { TestServer, createTestServer } from './server';

// Re-export commonly used testing libraries
export { default as request } from 'supertest';

// Import testDb for use in TestSetup
import { testDb } from './database';

/**
 * Common test setup and teardown utilities
 */
export class TestSetup {
  /**
   * Setup test environment
   */
  static async beforeAll() {
    // Setup test database
    await testDb.setup();
  }

  /**
   * Clean up after each test
   */
  static async afterEach() {
    // Reset database to clean state
    await testDb.reset();
  }

  /**
   * Teardown test environment
   */
  static async afterAll() {
    // Cleanup test database
    await testDb.teardown();
  }
}

/**
 * Test configuration overrides
 */
export const testConfig = {
  nodeEnv: 'test',
  logLevel: 'error',
  tokenTtlSeconds: 3600, // 1 hour for tests
  refreshTtlSeconds: 86400 // 1 day for tests
};
