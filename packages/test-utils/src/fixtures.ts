import { testDb } from './database';

/**
 * Test data fixtures for consistent testing
 */
export class TestFixtures {
  /**
   * Create a test organization
   */
  static async createOrg(data: { name?: string } = {}) {
    const prisma = testDb.getPrisma();
    
    return await prisma.org.create({
      data: {
        name: data.name || 'Test Organization'
      }
    });
  }

  /**
   * Create a test customer
   */
  static async createCustomer(orgId: string, data: Partial<{
    type: 'CASH' | 'CREDIT';
    name: string;
    email: string;
    phone: string;
  }> = {}) {
    const prisma = testDb.getPrisma();
    
    return await prisma.customer.create({
      data: {
        orgId,
        type: data.type || 'CASH',
        name: data.name || 'Test Customer',
        email: data.email || '<EMAIL>',
        phone: data.phone || '+971501234567'
      }
    });
  }

  /**
   * Create a test vehicle
   */
  static async createVehicle(orgId: string, data: Partial<{
    vin: string;
    plateNumber: string;
    businessType: 'STR' | 'LTR' | 'SCHOOL' | 'CHAUFFEUR' | 'IB';
    location: string;
    registrationNumber: string;
  }> = {}) {
    const prisma = testDb.getPrisma();
    
    return await prisma.vehicle.create({
      data: {
        orgId,
        vin: data.vin || 'TEST123456789',
        plateNumber: data.plateNumber || 'ABC123',
        businessType: data.businessType || 'STR',
        location: data.location || 'Dubai',
        registrationNumber: data.registrationNumber || 'REG123'
      }
    });
  }

  /**
   * Create a test reservation
   */
  static async createReservation(orgId: string, customerId: string, data: Partial<{
    vehicleId: string;
    fromDate: Date;
    toDate: Date;
    notes: string;
  }> = {}) {
    const prisma = testDb.getPrisma();
    
    return await prisma.reservation.create({
      data: {
        orgId,
        customerId,
        vehicleId: data.vehicleId,
        fromDate: data.fromDate || new Date(),
        toDate: data.toDate || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        notes: data.notes || 'Test reservation'
      }
    });
  }

  /**
   * Create a test agreement
   */
  static async createAgreement(orgId: string, customerId: string, vehicleId: string, data: Partial<{
    type: 'STR' | 'LTR' | 'SCHOOL' | 'CHAUFFEUR' | 'IB';
    reservationId: string;
    startDate: Date;
    endDate: Date;
    total: number;
    currency: string;
  }> = {}) {
    const prisma = testDb.getPrisma();

    return await prisma.agreement.create({
      data: {
        orgId,
        type: data.type || 'STR',
        customerId,
        vehicleId,
        reservationId: data.reservationId,
        startDate: data.startDate || new Date(),
        endDate: data.endDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        total: data.total || 1000,
        currency: data.currency || 'AED'
      }
    });
  }

  /**
   * Create a test invoice
   */
  static async createInvoice(orgId: string, customerId: string, data: Partial<{
    agreementId: string;
    amount: number;
    currency: string;
    dueDate: Date;
  }> = {}) {
    const prisma = testDb.getPrisma();
    
    return await prisma.invoice.create({
      data: {
        orgId,
        customerId,
        agreementId: data.agreementId,
        amount: data.amount || 500,
        currency: data.currency || 'AED',
        dueDate: data.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
      }
    });
  }

  /**
   * Create a test receipt
   */
  static async createReceipt(orgId: string, customerId: string, data: Partial<{
    invoiceId: string;
    amount: number;
    type: 'STANDARD' | 'PDC';
  }> = {}) {
    const prisma = testDb.getPrisma();
    
    return await prisma.receipt.create({
      data: {
        orgId,
        customerId,
        invoiceId: data.invoiceId,
        amount: data.amount || 500,
        type: data.type || 'STANDARD'
      }
    });
  }

  /**
   * Create a complete test scenario with org, customer, vehicle, and agreement
   */
  static async createCompleteScenario() {
    const org = await this.createOrg();
    const customer = await this.createCustomer(org.id);
    const vehicle = await this.createVehicle(org.id);
    const reservation = await this.createReservation(org.id, customer.id, { vehicleId: vehicle.id });
    const agreement = await this.createAgreement(org.id, customer.id, vehicle.id, { reservationId: reservation.id });
    const invoice = await this.createInvoice(org.id, customer.id, { agreementId: agreement.id });
    const receipt = await this.createReceipt(org.id, customer.id, { invoiceId: invoice.id });

    return {
      org,
      customer,
      vehicle,
      reservation,
      agreement,
      invoice,
      receipt
    };
  }
}
