import { Request, Response, NextFunction } from 'express';
import DOMPurify from 'dompurify';
import { J<PERSON><PERSON> } from 'jsdom';
import validator from 'validator';
import hpp from 'hpp';

import { createLogger } from '@a-realm/logger';

const logger = createLogger('input-sanitizer');

// Create a JSDOM window for DOMPurify
const window = new JSDOM('').window;
const purify = DOMPurify(window as any);

/**
 * Configuration options for input sanitization
 */
export interface SanitizationOptions {
  sanitizeHtml?: boolean;
  sanitizeStrings?: boolean;
  maxStringLength?: number;
  allowedTags?: string[];
  allowedAttributes?: string[];
  logSanitization?: boolean;
}

/**
 * Default sanitization options
 */
const defaultOptions: SanitizationOptions = {
  sanitizeHtml: true,
  sanitizeStrings: true,
  maxStringLength: 10000,
  allowedTags: [],
  allowedAttributes: [],
  logSanitization: true
};

/**
 * Sanitize a string value
 */
function sanitizeString(value: string, options: SanitizationOptions): string {
  let sanitized = value;

  // Trim whitespace
  sanitized = sanitized.trim();

  // Limit string length
  if (options.maxStringLength && sanitized.length > options.maxStringLength) {
    sanitized = sanitized.substring(0, options.maxStringLength);
    if (options.logSanitization) {
      logger.warn('String truncated due to length limit', { 
        originalLength: value.length, 
        maxLength: options.maxStringLength 
      });
    }
  }

  // Sanitize HTML if enabled
  if (options.sanitizeHtml) {
    const originalLength = sanitized.length;
    sanitized = purify.sanitize(sanitized, {
      ALLOWED_TAGS: options.allowedTags || [],
      ALLOWED_ATTR: options.allowedAttributes || [],
      KEEP_CONTENT: true
    });
    
    if (options.logSanitization && sanitized.length !== originalLength) {
      logger.warn('HTML content sanitized', { 
        originalLength, 
        sanitizedLength: sanitized.length 
      });
    }
  }

  // Escape dangerous characters
  if (options.sanitizeStrings) {
    sanitized = validator.escape(sanitized);
  }

  return sanitized;
}

/**
 * Recursively sanitize an object
 */
function sanitizeObject(obj: any, options: SanitizationOptions): any {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'string') {
    return sanitizeString(obj, options);
  }

  if (typeof obj === 'number' || typeof obj === 'boolean') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item, options));
  }

  if (typeof obj === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      // Sanitize the key as well
      const sanitizedKey = sanitizeString(key, options);
      sanitized[sanitizedKey] = sanitizeObject(value, options);
    }
    return sanitized;
  }

  return obj;
}

/**
 * Create input sanitization middleware
 */
export function createInputSanitizer(options: Partial<SanitizationOptions> = {}) {
  const config = { ...defaultOptions, ...options };

  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      // Sanitize request body
      if (req.body && typeof req.body === 'object') {
        req.body = sanitizeObject(req.body, config);
      }

      // Sanitize query parameters
      if (req.query && typeof req.query === 'object') {
        req.query = sanitizeObject(req.query, config);
      }

      // Sanitize URL parameters
      if (req.params && typeof req.params === 'object') {
        req.params = sanitizeObject(req.params, config);
      }

      next();
    } catch (error) {
      logger.error('Input sanitization failed', { 
        error: error instanceof Error ? error.message : 'Unknown error',
        path: req.path,
        method: req.method
      });
      
      res.status(400).json({
        error: {
          code: 'INVALID_INPUT',
          message: 'Request contains invalid or malicious content',
          timestamp: new Date().toISOString()
        }
      });
    }
  };
}

/**
 * Strict input sanitizer for sensitive endpoints
 */
export const strictInputSanitizer = createInputSanitizer({
  sanitizeHtml: true,
  sanitizeStrings: true,
  maxStringLength: 1000,
  allowedTags: [],
  allowedAttributes: [],
  logSanitization: true
});

/**
 * Lenient input sanitizer for content endpoints
 */
export const lenientInputSanitizer = createInputSanitizer({
  sanitizeHtml: true,
  sanitizeStrings: false,
  maxStringLength: 50000,
  allowedTags: ['b', 'i', 'em', 'strong', 'p', 'br'],
  allowedAttributes: [],
  logSanitization: true
});

/**
 * Basic input sanitizer for general use
 */
export const basicInputSanitizer = createInputSanitizer({
  sanitizeHtml: true,
  sanitizeStrings: true,
  maxStringLength: 5000,
  allowedTags: [],
  allowedAttributes: [],
  logSanitization: false
});

/**
 * HTTP Parameter Pollution (HPP) protection middleware
 */
export const hppProtection: any = hpp({
  whitelist: ['tags', 'categories'] // Allow arrays for these parameters
});

/**
 * Validate email format
 */
export function validateEmail(email: string): boolean {
  return validator.isEmail(email);
}

/**
 * Validate URL format
 */
export function validateUrl(url: string): boolean {
  return validator.isURL(url);
}

/**
 * Validate UUID format
 */
export function validateUuid(uuid: string): boolean {
  return validator.isUUID(uuid);
}

/**
 * Sanitize filename for safe file operations
 */
export function sanitizeFilename(filename: string): string {
  return filename
    .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace unsafe characters
    .replace(/_{2,}/g, '_') // Replace multiple underscores with single
    .substring(0, 255); // Limit length
}
