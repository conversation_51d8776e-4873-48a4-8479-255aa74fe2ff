{"name": "@a-realm/input-sanitizer", "version": "0.1.0", "private": true, "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc -p tsconfig.json", "dev": "tsc -w -p tsconfig.json", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "typecheck": "tsc -p tsconfig.json"}, "dependencies": {"@a-realm/logger": "workspace:*", "express": "^4.19.2", "dompurify": "^3.0.11", "jsdom": "^24.1.0", "validator": "^13.12.0", "hpp": "^0.2.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/dompurify": "^3.0.5", "@types/jsdom": "^21.1.7", "@types/validator": "^13.12.0", "@types/hpp": "^0.2.6", "typescript": "^5.4.5"}}