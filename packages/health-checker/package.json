{"name": "@a-realm/health-checker", "version": "0.1.0", "private": true, "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc -p tsconfig.json", "dev": "tsc -w -p tsconfig.json", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "typecheck": "tsc -p tsconfig.json"}, "dependencies": {"@a-realm/config": "workspace:*", "@a-realm/db": "workspace:*", "@a-realm/logger": "workspace:*", "express": "^4.19.2", "redis": "^4.6.13"}, "devDependencies": {"@types/express": "^4.17.21", "typescript": "^5.4.5"}}