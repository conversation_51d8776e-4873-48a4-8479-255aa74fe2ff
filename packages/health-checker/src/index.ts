import { Request, Response } from 'express';
import { createClient } from 'redis';

import { config } from '@a-realm/config';
import { checkDatabaseHealth } from '@a-realm/db';
import { createLogger } from '@a-realm/logger';

const logger = createLogger('health-checker');

/**
 * Health check status
 */
export interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  checks: {
    [key: string]: {
      status: 'pass' | 'fail' | 'warn';
      message?: string;
      responseTime?: number;
      details?: any;
    };
  };
}

/**
 * Dependency check function type
 */
export type HealthCheck = () => Promise<{
  status: 'pass' | 'fail' | 'warn';
  message?: string;
  responseTime?: number;
  details?: any;
}>;

/**
 * Health checker class
 */
export class HealthChecker {
  private checks: Map<string, HealthCheck> = new Map();
  private startTime: number = Date.now();

  /**
   * Add a health check
   */
  addCheck(name: string, check: HealthCheck): void {
    this.checks.set(name, check);
  }

  /**
   * Remove a health check
   */
  removeCheck(name: string): void {
    this.checks.delete(name);
  }

  /**
   * Run all health checks
   */
  async runChecks(): Promise<HealthStatus> {
    const timestamp = new Date().toISOString();
    const uptime = Math.floor((Date.now() - this.startTime) / 1000);
    const checks: HealthStatus['checks'] = {};

    // Run all checks in parallel
    const checkPromises = Array.from(this.checks.entries()).map(async ([name, check]) => {
      const startTime = Date.now();
      try {
        const result = await Promise.race([
          check(),
          new Promise<{ status: 'fail'; message: string }>((_, reject) =>
            setTimeout(() => reject(new Error('Health check timeout')), 5000)
          )
        ]);
        
        checks[name] = {
          ...result,
          responseTime: Date.now() - startTime
        };
      } catch (error) {
        checks[name] = {
          status: 'fail',
          message: error instanceof Error ? error.message : 'Unknown error',
          responseTime: Date.now() - startTime
        };
      }
    });

    await Promise.allSettled(checkPromises);

    // Determine overall status
    const statuses = Object.values(checks).map(check => check.status);
    let overallStatus: HealthStatus['status'] = 'healthy';
    
    if (statuses.includes('fail')) {
      overallStatus = 'unhealthy';
    } else if (statuses.includes('warn')) {
      overallStatus = 'degraded';
    }

    return {
      status: overallStatus,
      timestamp,
      uptime,
      version: process.env.npm_package_version || '1.0.0',
      environment: config.nodeEnv,
      checks
    };
  }

  /**
   * Express middleware for health endpoint
   */
  middleware() {
    return async (req: Request, res: Response) => {
      try {
        const health = await this.runChecks();
        
        // Set appropriate HTTP status code
        let statusCode = 200;
        if (health.status === 'unhealthy') {
          statusCode = 503; // Service Unavailable
        } else if (health.status === 'degraded') {
          statusCode = 200; // OK but with warnings
        }

        res.status(statusCode).json(health);
      } catch (error) {
        logger.error('Health check failed', { 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
        
        res.status(503).json({
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          uptime: Math.floor((Date.now() - this.startTime) / 1000),
          version: process.env.npm_package_version || '1.0.0',
          environment: config.nodeEnv,
          checks: {
            system: {
              status: 'fail',
              message: 'Health check system failure'
            }
          }
        });
      }
    };
  }
}

/**
 * Database health check
 */
export const databaseHealthCheck: HealthCheck = async () => {
  try {
    const result = await checkDatabaseHealth();
    return {
      status: result.healthy ? 'pass' : 'fail',
      message: result.error || 'Database connection successful'
    };
  } catch (error) {
    return {
      status: 'fail',
      message: error instanceof Error ? error.message : 'Database check failed'
    };
  }
};

/**
 * Redis health check
 */
export const redisHealthCheck: HealthCheck = async () => {
  if (!config.redisUrl) {
    return {
      status: 'warn',
      message: 'Redis not configured'
    };
  }

  let client: ReturnType<typeof createClient> | null = null;
  try {
    client = createClient({ url: config.redisUrl });
    await client.connect();
    await client.ping();
    return {
      status: 'pass',
      message: 'Redis connection successful'
    };
  } catch (error) {
    return {
      status: 'fail',
      message: error instanceof Error ? error.message : 'Redis check failed'
    };
  } finally {
    if (client) {
      try {
        await client.quit();
      } catch {
        // Ignore cleanup errors
      }
    }
  }
};

/**
 * Memory usage health check
 */
export const memoryHealthCheck: HealthCheck = async () => {
  const usage = process.memoryUsage();
  const totalMB = Math.round(usage.rss / 1024 / 1024);
  const heapUsedMB = Math.round(usage.heapUsed / 1024 / 1024);
  const heapTotalMB = Math.round(usage.heapTotal / 1024 / 1024);
  
  // Warn if using more than 512MB RSS or 80% heap
  const heapUsagePercent = (usage.heapUsed / usage.heapTotal) * 100;
  
  let status: 'pass' | 'warn' | 'fail' = 'pass';
  let message = `Memory usage: ${totalMB}MB RSS, ${heapUsedMB}/${heapTotalMB}MB heap (${heapUsagePercent.toFixed(1)}%)`;
  
  if (totalMB > 1024 || heapUsagePercent > 90) {
    status = 'fail';
    message = `High memory usage: ${message}`;
  } else if (totalMB > 512 || heapUsagePercent > 80) {
    status = 'warn';
    message = `Elevated memory usage: ${message}`;
  }
  
  return {
    status,
    message,
    details: {
      rss: totalMB,
      heapUsed: heapUsedMB,
      heapTotal: heapTotalMB,
      heapUsagePercent: Math.round(heapUsagePercent)
    }
  };
};

/**
 * Disk space health check
 */
export const diskHealthCheck: HealthCheck = async () => {
  try {
    const { execSync } = await import('child_process');
    const output = execSync('df -h / | tail -1', { encoding: 'utf8' });
    const parts = output.trim().split(/\s+/);
    const usagePercent = parseInt(parts[4].replace('%', ''));
    
    let status: 'pass' | 'warn' | 'fail' = 'pass';
    let message = `Disk usage: ${parts[4]} (${parts[2]} used of ${parts[1]})`;
    
    if (usagePercent > 90) {
      status = 'fail';
      message = `Critical disk usage: ${message}`;
    } else if (usagePercent > 80) {
      status = 'warn';
      message = `High disk usage: ${message}`;
    }
    
    return {
      status,
      message,
      details: {
        usagePercent,
        used: parts[2],
        total: parts[1],
        available: parts[3]
      }
    };
  } catch (error) {
    return {
      status: 'warn',
      message: 'Could not check disk usage'
    };
  }
};

/**
 * Create a default health checker with common checks
 */
export function createDefaultHealthChecker(): HealthChecker {
  const checker = new HealthChecker();
  
  checker.addCheck('database', databaseHealthCheck);
  checker.addCheck('redis', redisHealthCheck);
  checker.addCheck('memory', memoryHealthCheck);
  checker.addCheck('disk', diskHealthCheck);
  
  return checker;
}
