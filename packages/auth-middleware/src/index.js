"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireAuth = requireAuth;
exports.requireAnyRole = requireAnyRole;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = require("@a-realm/config");
function requireAuth() {
    return (req, res, next) => {
        // Allow health and docs unauthenticated if mounted before this
        const header = req.headers.authorization;
        if (!header || !header.startsWith('Bearer '))
            return res.status(401).json({ message: 'missing token' });
        const token = header.slice(7);
        try {
            const payload = jsonwebtoken_1.default.verify(token, config_1.config.jwtSecret);
            req.user = payload;
            next();
        }
        catch (e) {
            return res.status(401).json({ message: 'invalid token' });
        }
    };
}
function requireAnyRole(roles) {
    return (req, res, next) => {
        const userRoles = req.user?.roles || [];
        if (roles.some((r) => userRoles.includes(r)))
            return next();
        return res.status(403).json({ message: 'forbidden' });
    };
}
//# sourceMappingURL=index.js.map