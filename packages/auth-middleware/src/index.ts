import jwt from 'jsonwebtoken';
import type { Request, Response, NextFunction } from 'express';
import { config } from '@a-realm/config';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request { user?: any }
  }
}

export function requireAuth() {
  return (req: Request, res: Response, next: NextFunction) => {
    // Allow health and docs unauthenticated if mounted before this
    const header = req.headers.authorization;
    if (!header || !header.startsWith('Bearer ')) return res.status(401).json({ message: 'missing token' });
    const token = header.slice(7);
    try {
      const payload = jwt.verify(token, config.jwtSecret);
      req.user = payload;
      next();
    } catch (e) {
      return res.status(401).json({ message: 'invalid token' });
    }
  };
}

export function requireAnyRole(roles: string[]) {
  return (req: Request, res: Response, next: NextFunction) => {
    const userRoles: string[] = (req.user?.roles as string[]) || [];
    if (roles.some((r) => userRoles.includes(r))) return next();
    return res.status(403).json({ message: 'forbidden' });
  };
}

