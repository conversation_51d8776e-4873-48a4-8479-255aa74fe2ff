{"name": "@a-realm/otel", "version": "0.1.0", "private": true, "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc -p tsconfig.json", "dev": "tsc -w -p tsconfig.json", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "typecheck": "tsc -p tsconfig.json"}, "dependencies": {"@opentelemetry/api": "^1.9.0", "@opentelemetry/sdk-node": "^0.50.0", "@opentelemetry/auto-instrumentations-node": "^0.50.0", "@opentelemetry/exporter-trace-otlp-http": "^0.50.0", "@a-realm/config": "workspace:*"}, "devDependencies": {"typescript": "^5.4.5"}}