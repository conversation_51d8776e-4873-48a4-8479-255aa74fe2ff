import { NodeSDK } from '@opentelemetry/sdk-node';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { config as appConfig } from '@a-realm/config';

let sdk: NodeSDK | undefined;

export function startTracing(serviceName?: string) {
  if (sdk) return; // already started
  const endpoint = appConfig.otelOtlpEndpoint;
  if (!endpoint) return; // tracing disabled unless endpoint provided
  const exporter = new OTLPTraceExporter({ url: `${endpoint}/v1/traces` });
  sdk = new NodeSDK({
    traceExporter: exporter,
    serviceName: serviceName || appConfig.serviceName,
    instrumentations: [getNodeAutoInstrumentations()]
  });
  try {
    sdk.start();
  } catch (e: any) {
    // eslint-disable-next-line no-console
    console.error('OTel start error', e);
  }
}

export async function shutdownTracing() {
  if (!sdk) return;
  try {
    await sdk.shutdown();
  } catch (e: any) {
    // eslint-disable-next-line no-console
    console.error('OTel shutdown error', e);
  }
}

