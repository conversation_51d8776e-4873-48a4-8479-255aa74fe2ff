{"name": "@a-realm/logger", "version": "0.1.0", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc -p tsconfig.json", "dev": "tsc -w -p tsconfig.json", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "typecheck": "tsc -p tsconfig.json"}, "dependencies": {"winston": "^3.13.0", "uuid": "^9.0.1", "on-finished": "^2.4.1"}, "devDependencies": {"typescript": "^5.4.5"}}