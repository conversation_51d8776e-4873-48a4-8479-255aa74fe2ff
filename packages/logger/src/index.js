"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createLogger = createLogger;
exports.requestIdMiddleware = requestIdMiddleware;
exports.requestLogger = requestLogger;
const winston_1 = __importDefault(require("winston"));
const uuid_1 = require("uuid");
const on_finished_1 = __importDefault(require("on-finished"));
function createLogger(level = 'info') {
    return winston_1.default.createLogger({
        level,
        transports: [new winston_1.default.transports.Console()],
        format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json())
    });
}
function requestIdMiddleware(req, res, next) {
    const header = req.headers['x-request-id'];
    const id = (Array.isArray(header) ? header[0] : header) || (0, uuid_1.v4)();
    req.requestId = id;
    res.setHeader('x-request-id', id);
    next();
}
function requestLogger(logger) {
    return function (req, res, next) {
        const start = Date.now();
        const reqId = req.requestId;
        logger.info({ msg: 'request:start', method: req.method, path: req.originalUrl, reqId });
        (0, on_finished_1.default)(res, () => {
            const duration = Date.now() - start;
            logger.info({ msg: 'request:end', method: req.method, path: req.originalUrl, status: res.statusCode, duration, reqId });
        });
        next();
    };
}
//# sourceMappingURL=index.js.map