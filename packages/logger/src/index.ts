import winston from 'winston';
import { v4 as uuidv4 } from 'uuid';
import onFinished from 'on-finished';
import type { Request, Response, NextFunction } from 'express';

export function createLogger(level = 'info') {
  return winston.createLogger({
    level,
    transports: [new winston.transports.Console()],
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json()
    )
  });
}

export function requestIdMiddleware(req: Request, res: Response, next: NextFunction) {
  const header = req.headers['x-request-id'];
  const id = (Array.isArray(header) ? header[0] : header) || uuidv4();
  (req as any).requestId = id;
  res.setHeader('x-request-id', id);
  next();
}

export function requestLogger(logger: winston.Logger) {
  return function (req: Request, res: Response, next: NextFunction) {
    const start = Date.now();
    const reqId = (req as any).requestId;
    logger.info({ msg: 'request:start', method: req.method, path: req.originalUrl, reqId });
    onFinished(res, () => {
      const duration = Date.now() - start;
      logger.info({ msg: 'request:end', method: req.method, path: req.originalUrl, status: res.statusCode, duration, reqId });
    });
    next();
  };
}

