import { Decimal } from 'decimal.js';

// Enum types (matching Prisma schema)
export type AgreementType = 'STR' | 'LTR' | 'SCHOOL' | 'CHAUFFEUR' | 'IB';
export type AgreementStatus = 'CREATED' | 'ON_HIRE' | 'CLOSED';
export type VehicleStatus = 'NEW' | 'AVAILABLE' | 'ON_HIRE' | 'MAINTENANCE' | 'SOLD';
export type ReservationStatus = 'CREATED' | 'CONFIRMED' | 'CANCELLED';
export type InvoiceStatus = 'OPEN' | 'PAID' | 'VOID' | 'CREDITED';
export type BusinessType = 'STR' | 'LTR' | 'SCHOOL' | 'CHAUFFEUR' | 'IB';
export type CustomerType = 'CASH' | 'CREDIT';

// Basic entity types (simplified for business logic)
export interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  type: CustomerType;
}

export interface Vehicle {
  id: string;
  make: string;
  model: string;
  year: number;
  status: VehicleStatus;
}

export interface Agreement {
  id: string;
  customerId: string;
  vehicleId: string;
  type: AgreementType;
  status: AgreementStatus;
  startDate: Date;
  endDate?: Date;
  total?: number;
}

export interface Invoice {
  id: string;
  agreementId?: string;
  customerId: string;
  amount: number;
  status: InvoiceStatus;
  dueDate?: Date;
}

// Enhanced business types
export interface BusinessContext {
  orgId: string;
  userId: string;
  userRole: string;
  timestamp: Date;
}

export interface RentalPeriod {
  startDate: Date;
  endDate: Date;
  durationDays: number;
  isWeekend: boolean;
  isHoliday: boolean;
}

export interface PricingContext {
  agreementType: AgreementType;
  businessType: BusinessType;
  customerType: CustomerType;
  period: RentalPeriod;
  vehicleCategory: string;
  seasonalMultiplier: number;
  discountPercentage: number;
}

export interface RentalRates {
  dailyRate: Decimal;
  weeklyRate: Decimal;
  monthlyRate: Decimal;
  weekendSurcharge: Decimal;
  holidaySurcharge: Decimal;
  insuranceRate: Decimal;
  salikRate: Decimal;
  fuelDeposit: Decimal;
  securityDeposit: Decimal;
}

export interface VehicleAvailability {
  vehicleId: string;
  isAvailable: boolean;
  availableFrom: Date;
  availableUntil: Date;
  conflictingReservations: string[];
  maintenanceScheduled: boolean;
  currentStatus: VehicleStatus;
}

export interface ReservationConflict {
  type: 'OVERLAP' | 'MAINTENANCE' | 'UNAVAILABLE';
  conflictingId: string;
  conflictingPeriod: RentalPeriod;
  severity: 'HIGH' | 'MEDIUM' | 'LOW';
  resolution: string;
}

export interface AgreementValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  businessRuleViolations: string[];
}

export interface BillingCalculation {
  baseAmount: Decimal;
  additionalCharges: Decimal;
  discounts: Decimal;
  taxes: Decimal;
  totalAmount: Decimal;
  breakdown: BillingLineItem[];
}

export interface BillingLineItem {
  description: string;
  quantity: number;
  unitPrice: Decimal;
  totalPrice: Decimal;
  category: 'RENTAL' | 'INSURANCE' | 'FUEL' | 'SALIK' | 'PENALTY' | 'DISCOUNT' | 'TAX';
}

export interface VehicleHandover {
  agreementId: string;
  vehicleId: string;
  customerId: string;
  handoverDate: Date;
  odometerReading: number;
  fuelLevel: number;
  vehicleCondition: VehicleConditionReport;
  documentsProvided: string[];
  handoverNotes: string;
  completedBy: string;
}

export interface VehicleReturn {
  agreementId: string;
  vehicleId: string;
  returnDate: Date;
  odometerReading: number;
  fuelLevel: number;
  vehicleCondition: VehicleConditionReport;
  damageReport: DamageReport[];
  additionalCharges: BillingLineItem[];
  returnNotes: string;
  processedBy: string;
}

export interface VehicleConditionReport {
  exterior: ConditionItem[];
  interior: ConditionItem[];
  mechanical: ConditionItem[];
  accessories: ConditionItem[];
  overallRating: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR';
}

export interface ConditionItem {
  component: string;
  condition: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR' | 'DAMAGED';
  notes: string;
  photos: string[];
}

export interface DamageReport {
  type: 'SCRATCH' | 'DENT' | 'CRACK' | 'MISSING' | 'MECHANICAL';
  severity: 'MINOR' | 'MODERATE' | 'MAJOR';
  location: string;
  description: string;
  estimatedCost: Decimal;
  photos: string[];
  repairRequired: boolean;
}

export interface MaintenanceSchedule {
  vehicleId: string;
  type: 'ROUTINE' | 'PREVENTIVE' | 'REPAIR' | 'INSPECTION';
  scheduledDate: Date;
  estimatedDuration: number; // hours
  description: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  cost: Decimal;
  serviceProvider: string;
}

export interface BusinessMetrics {
  period: RentalPeriod;
  totalRevenue: Decimal;
  totalRentals: number;
  averageRentalDuration: number;
  fleetUtilization: number;
  customerSatisfaction: number;
  maintenanceCosts: Decimal;
  profitMargin: number;
}

export interface CustomerProfile {
  customer: Customer;
  creditScore: number;
  rentalHistory: Agreement[];
  paymentHistory: Invoice[];
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  preferredVehicleTypes: BusinessType[];
  averageRentalDuration: number;
  totalLifetimeValue: Decimal;
}

// Workflow state types
export interface WorkflowState {
  id: string;
  type: 'RESERVATION' | 'AGREEMENT' | 'BILLING' | 'MAINTENANCE';
  status: string;
  currentStep: string;
  nextSteps: string[];
  data: Record<string, any>;
  history: WorkflowStep[];
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkflowStep {
  step: string;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 'SKIPPED';
  startedAt: Date;
  completedAt?: Date;
  performedBy: string;
  notes: string;
  data: Record<string, any>;
}

// Event types for business events
export interface BusinessEvent {
  id: string;
  type: string;
  entityType: 'CUSTOMER' | 'VEHICLE' | 'RESERVATION' | 'AGREEMENT' | 'INVOICE';
  entityId: string;
  orgId: string;
  data: Record<string, any>;
  timestamp: string;
  userId: string;
}

// Search and filter types
export interface SearchCriteria {
  query?: string;
  filters: Record<string, any>;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  page?: number;
  limit?: number;
}

export interface SearchResult<T> {
  items: T[];
  totalCount: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Configuration types
export interface BusinessConfiguration {
  orgId: string;
  rentalRates: RentalRates;
  businessRules: BusinessRules;
  workflowSettings: WorkflowSettings;
  integrationSettings: IntegrationSettings;
}

export interface BusinessRules {
  minimumRentalDays: number;
  maximumRentalDays: number;
  advanceBookingDays: number;
  cancellationPolicy: CancellationPolicy;
  lateFeePolicy: LateFeePolicy;
  damagePolicy: DamagePolicy;
  fuelPolicy: FuelPolicy;
}

export interface CancellationPolicy {
  freeHours: number;
  penaltyPercentage: number;
  noShowPenalty: Decimal;
}

export interface LateFeePolicy {
  graceMinutes: number;
  hourlyRate: Decimal;
  dailyRate: Decimal;
  maximumFee: Decimal;
}

export interface DamagePolicy {
  assessmentFee: Decimal;
  deductible: Decimal;
  maximumLiability: Decimal;
}

export interface FuelPolicy {
  returnLevel: number; // percentage
  refuelCharge: Decimal; // per liter
  serviceFee: Decimal;
}

export interface WorkflowSettings {
  autoConfirmReservations: boolean;
  requireApprovalForHighValue: boolean;
  autoGenerateInvoices: boolean;
  sendReminderNotifications: boolean;
}

export interface IntegrationSettings {
  paymentGateway: string;
  smsProvider: string;
  emailProvider: string;
  documentStorage: string;
  gpsTracking: boolean;
}
