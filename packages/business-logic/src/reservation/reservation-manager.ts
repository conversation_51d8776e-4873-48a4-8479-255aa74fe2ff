import { getPrisma } from '@a-realm/db';
import { createLogger } from '@a-realm/logger';
import { publishEvent } from '@a-realm/messaging';
import { addDays, differenceInDays, isWeekend, parseISO } from 'date-fns';
import { Decimal } from 'decimal.js';

import {
  BusinessContext,
  RentalPeriod,
  VehicleAvailability,
  ReservationConflict,
  SearchCriteria,
  SearchResult,
  BusinessEvent
} from '../types/business-types';

const logger = createLogger('reservation-manager');

export class ReservationManager {
  private prisma = getPrisma();

  /**
   * Check vehicle availability for a given period
   */
  async checkVehicleAvailability(
    vehicleId: string,
    startDate: Date,
    endDate: Date,
    excludeReservationId?: string
  ): Promise<VehicleAvailability> {
    try {
      // Get vehicle current status
      const vehicle = await this.prisma.vehicle.findUnique({
        where: { id: vehicleId }
      });

      if (!vehicle) {
        throw new Error(`Vehicle ${vehicleId} not found`);
      }

      // Check for conflicting reservations
      const conflictingReservations = await this.prisma.reservation.findMany({
        where: {
          vehicleId,
          status: { in: ['CREATED', 'CONFIRMED'] },
          id: excludeReservationId ? { not: excludeReservationId } : undefined,
          OR: [
            {
              fromDate: { lte: endDate },
              toDate: { gte: startDate }
            }
          ]
        },
        select: { id: true, fromDate: true, toDate: true }
      });

      // Check for maintenance schedules (simplified - would integrate with maintenance system)
      const maintenanceScheduled = false; // TODO: Implement maintenance checking

      const isAvailable = 
        vehicle.status === 'AVAILABLE' && 
        conflictingReservations.length === 0 && 
        !maintenanceScheduled;

      const nextAvailableDate = isAvailable ? startDate : await this.calculateNextAvailableDate(vehicleId, startDate);

      return {
        vehicleId,
        isAvailable,
        availableFrom: nextAvailableDate,
        availableUntil: isAvailable ? endDate : startDate,
        conflictingReservations: conflictingReservations.map(r => r.id),
        maintenanceScheduled,
        currentStatus: vehicle.status
      };
    } catch (error) {
      logger.error('Failed to check vehicle availability', { vehicleId, error });
      throw error;
    }
  }

  /**
   * Find available vehicles for a given period and criteria
   */
  async findAvailableVehicles(
    orgId: string,
    startDate: Date,
    endDate: Date,
    criteria: {
      businessType?: string;
      location?: string;
      minSeats?: number;
      maxPrice?: number;
    } = {}
  ): Promise<VehicleAvailability[]> {
    try {
      // Get vehicles matching criteria
      const vehicles = await this.prisma.vehicle.findMany({
        where: {
          orgId,
          status: 'AVAILABLE',
          businessType: criteria.businessType as any,
          location: criteria.location
        }
      });

      // Check availability for each vehicle
      const availabilityChecks = await Promise.all(
        vehicles.map(vehicle => 
          this.checkVehicleAvailability(vehicle.id, startDate, endDate)
        )
      );

      return availabilityChecks.filter(availability => availability.isAvailable);
    } catch (error) {
      logger.error('Failed to find available vehicles', { orgId, criteria, error });
      throw error;
    }
  }

  /**
   * Create a new reservation with business rule validation
   */
  async createReservation(
    context: BusinessContext,
    data: {
      customerId: string;
      vehicleId?: string;
      fromDate: Date;
      toDate: Date;
      notes?: string;
      requireVehicleType?: string;
    }
  ): Promise<any> {
    try {
      // Validate rental period
      const period = this.createRentalPeriod(data.fromDate, data.toDate);
      await this.validateRentalPeriod(period, context.orgId);

      // If no specific vehicle, find available one
      let vehicleId = data.vehicleId;
      if (!vehicleId && data.requireVehicleType) {
        const availableVehicles = await this.findAvailableVehicles(
          context.orgId,
          data.fromDate,
          data.toDate,
          { businessType: data.requireVehicleType }
        );

        if (availableVehicles.length === 0) {
          throw new Error(`No vehicles available for the requested period`);
        }

        vehicleId = availableVehicles[0].vehicleId;
      }

      // Check availability if vehicle specified
      if (vehicleId) {
        const availability = await this.checkVehicleAvailability(
          vehicleId,
          data.fromDate,
          data.toDate
        );

        if (!availability.isAvailable) {
          throw new Error(`Vehicle is not available for the requested period`);
        }
      }

      // Create reservation
      const reservation = await this.prisma.reservation.create({
        data: {
          orgId: context.orgId,
          customerId: data.customerId,
          vehicleId,
          fromDate: data.fromDate,
          toDate: data.toDate,
          notes: data.notes,
          status: 'CREATED'
        },
        include: {
          customer: true,
          vehicle: true
        }
      });

      // Publish event
      await this.publishReservationEvent('reservation.created', reservation, context);

      logger.info('Reservation created successfully', { 
        reservationId: reservation.id,
        customerId: data.customerId,
        vehicleId
      });

      return reservation;
    } catch (error) {
      logger.error('Failed to create reservation', { data, error });
      throw error;
    }
  }

  /**
   * Confirm a reservation
   */
  async confirmReservation(reservationId: string, context: BusinessContext): Promise<any> {
    try {
      const reservation = await this.prisma.reservation.findUnique({
        where: { id: reservationId },
        include: { vehicle: true, customer: true }
      });

      if (!reservation) {
        throw new Error(`Reservation ${reservationId} not found`);
      }

      if (reservation.status !== 'CREATED') {
        throw new Error(`Reservation ${reservationId} cannot be confirmed in status ${reservation.status}`);
      }

      // Re-check availability
      if (reservation.vehicleId) {
        const availability = await this.checkVehicleAvailability(
          reservation.vehicleId,
          reservation.fromDate,
          reservation.toDate,
          reservationId
        );

        if (!availability.isAvailable) {
          throw new Error(`Vehicle is no longer available for confirmation`);
        }
      }

      // Update reservation status
      const updatedReservation = await this.prisma.reservation.update({
        where: { id: reservationId },
        data: { status: 'CONFIRMED' },
        include: { customer: true, vehicle: true }
      });

      // Publish event
      await this.publishReservationEvent('reservation.confirmed', updatedReservation, context);

      logger.info('Reservation confirmed successfully', { reservationId });

      return updatedReservation;
    } catch (error) {
      logger.error('Failed to confirm reservation', { reservationId, error });
      throw error;
    }
  }

  /**
   * Cancel a reservation
   */
  async cancelReservation(
    reservationId: string,
    context: BusinessContext,
    reason?: string
  ): Promise<any> {
    try {
      const reservation = await this.prisma.reservation.findUnique({
        where: { id: reservationId },
        include: { customer: true, vehicle: true }
      });

      if (!reservation) {
        throw new Error(`Reservation ${reservationId} not found`);
      }

      if (reservation.status === 'CANCELLED') {
        throw new Error(`Reservation ${reservationId} is already cancelled`);
      }

      // Calculate cancellation penalties if applicable
      const cancellationFee = await this.calculateCancellationFee(reservation, new Date());

      // Update reservation status
      const updatedReservation = await this.prisma.reservation.update({
        where: { id: reservationId },
        data: { 
          status: 'CANCELLED',
          notes: reservation.notes ? 
            `${reservation.notes}\nCancelled: ${reason || 'No reason provided'}` :
            `Cancelled: ${reason || 'No reason provided'}`
        },
        include: { customer: true, vehicle: true }
      });

      // Publish event
      await this.publishReservationEvent('reservation.cancelled', updatedReservation, context);

      logger.info('Reservation cancelled successfully', { 
        reservationId, 
        reason,
        cancellationFee: cancellationFee.toString()
      });

      return {
        reservation: updatedReservation,
        cancellationFee
      };
    } catch (error) {
      logger.error('Failed to cancel reservation', { reservationId, error });
      throw error;
    }
  }

  /**
   * Search reservations with filters
   */
  async searchReservations(
    orgId: string,
    criteria: SearchCriteria
  ): Promise<SearchResult<any>> {
    try {
      const where: any = { orgId };

      // Apply filters
      if (criteria.filters.customerId) {
        where.customerId = criteria.filters.customerId;
      }
      if (criteria.filters.vehicleId) {
        where.vehicleId = criteria.filters.vehicleId;
      }
      if (criteria.filters.status) {
        where.status = criteria.filters.status;
      }
      if (criteria.filters.fromDate) {
        where.fromDate = { gte: parseISO(criteria.filters.fromDate) };
      }
      if (criteria.filters.toDate) {
        where.toDate = { lte: parseISO(criteria.filters.toDate) };
      }

      // Count total
      const totalCount = await this.prisma.reservation.count({ where });

      // Get paginated results
      const page = criteria.page || 1;
      const limit = criteria.limit || 20;
      const skip = (page - 1) * limit;

      const reservations = await this.prisma.reservation.findMany({
        where,
        include: {
          customer: true,
          vehicle: true
        },
        orderBy: criteria.sortBy ?
          { [criteria.sortBy]: (criteria.sortOrder?.toLowerCase() as 'asc' | 'desc') || 'desc' } :
          { createdAt: 'desc' },
        skip,
        take: limit
      });

      return {
        items: reservations,
        totalCount,
        page,
        limit,
        hasMore: skip + limit < totalCount
      };
    } catch (error) {
      logger.error('Failed to search reservations', { orgId, criteria, error });
      throw error;
    }
  }

  // Private helper methods

  private createRentalPeriod(startDate: Date, endDate: Date): RentalPeriod {
    const durationDays = differenceInDays(endDate, startDate);
    return {
      startDate,
      endDate,
      durationDays,
      isWeekend: isWeekend(startDate) || isWeekend(endDate),
      isHoliday: false // TODO: Implement holiday checking
    };
  }

  private async validateRentalPeriod(period: RentalPeriod, orgId: string) {
    // Get business rules for organization
    const minDays = 1; // TODO: Get from org configuration
    const maxDays = 365; // TODO: Get from org configuration
    const maxAdvanceDays = 90; // TODO: Get from org configuration

    if (period.durationDays < minDays) {
      throw new Error(`Minimum rental period is ${minDays} days`);
    }

    if (period.durationDays > maxDays) {
      throw new Error(`Maximum rental period is ${maxDays} days`);
    }

    const advanceDays = differenceInDays(period.startDate, new Date());
    if (advanceDays > maxAdvanceDays) {
      throw new Error(`Cannot book more than ${maxAdvanceDays} days in advance`);
    }

    if (period.startDate < new Date()) {
      throw new Error('Cannot book in the past');
    }
  }

  private async calculateNextAvailableDate(vehicleId: string, fromDate: Date): Promise<Date> {
    const nextReservation = await this.prisma.reservation.findFirst({
      where: {
        vehicleId,
        status: { in: ['CREATED', 'CONFIRMED'] },
        fromDate: { gte: fromDate }
      },
      orderBy: { toDate: 'asc' }
    });

    return nextReservation ? addDays(nextReservation.toDate, 1) : fromDate;
  }

  private async calculateCancellationFee(reservation: any, cancellationDate: Date): Promise<Decimal> {
    const hoursUntilStart = differenceInDays(reservation.fromDate, cancellationDate) * 24;
    
    // TODO: Get cancellation policy from org configuration
    const freeHours = 24;
    const penaltyPercentage = 0.1;

    if (hoursUntilStart >= freeHours) {
      return new Decimal(0);
    }

    // Calculate penalty based on estimated rental value
    const estimatedValue = new Decimal(100); // TODO: Calculate actual rental value
    return estimatedValue.mul(penaltyPercentage);
  }

  private async publishReservationEvent(
    eventType: string, 
    reservation: any, 
    context: BusinessContext
  ) {
    const event: BusinessEvent = {
      id: `${eventType}-${reservation.id}-${Date.now()}`,
      type: eventType,
      entityType: 'RESERVATION',
      entityId: reservation.id,
      orgId: context.orgId,
      data: {
        reservationId: reservation.id,
        customerId: reservation.customerId,
        vehicleId: reservation.vehicleId,
        fromDate: reservation.fromDate,
        toDate: reservation.toDate,
        status: reservation.status
      },
      timestamp: new Date().toISOString(),
      userId: context.userId
    };

    await publishEvent(eventType, event);
  }
}
