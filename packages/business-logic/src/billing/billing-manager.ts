import { getPrisma } from '@a-realm/db';
import { createLogger } from '@a-realm/logger';
import { publishEvent } from '@a-realm/messaging';
import { addDays, differenceInDays, format } from 'date-fns';
import { Decimal } from 'decimal.js';

import {
  BusinessContext,
  BillingCalculation,
  BillingLineItem,
  BusinessEvent,
  SearchCriteria,
  SearchResult
} from '../types/business-types';

const logger = createLogger('billing-manager');

export class BillingManager {
  private prisma = getPrisma();

  /**
   * Generate invoice for an agreement
   */
  async generateAgreementInvoice(
    agreementId: string,
    context: BusinessContext,
    options: {
      dueDate?: Date;
      includeDeposit?: boolean;
      paymentTerms?: string;
      notes?: string;
    } = {}
  ): Promise<any> {
    try {
      const agreement = await this.prisma.agreement.findUnique({
        where: { id: agreementId },
        include: {
          customer: true,
          vehicle: true,
          invoices: true
        }
      });

      if (!agreement) {
        throw new Error(`Agreement ${agreementId} not found`);
      }

      // Check if invoice already exists
      const existingInvoice = agreement.invoices.find(inv => inv.status === 'OPEN');
      if (existingInvoice) {
        throw new Error(`Open invoice already exists for agreement ${agreementId}`);
      }

      // Calculate billing amount
      const billing = await this.calculateAgreementBilling(agreement, options);

      // Create invoice
      const invoice = await this.prisma.invoice.create({
        data: {
          orgId: context.orgId,
          agreementId,
          customerId: agreement.customerId,
          amount: billing.totalAmount.toNumber(),
          currency: agreement.currency || 'AED',
          status: 'OPEN',
          dueDate: options.dueDate || addDays(new Date(), 30),
          issueDate: new Date()
        },
        include: {
          customer: true,
          agreement: true
        }
      });

      // Publish event
      await this.publishBillingEvent('invoice.generated', invoice, context);

      logger.info('Invoice generated successfully', {
        invoiceId: invoice.id,
        agreementId,
        amount: billing.totalAmount.toString()
      });

      return {
        invoice,
        billing
      };
    } catch (error) {
      logger.error('Failed to generate agreement invoice', { agreementId, error });
      throw error;
    }
  }

  /**
   * Process payment receipt
   */
  async processPayment(
    invoiceId: string,
    paymentData: {
      amount: number;
      paymentMethod: 'CASH' | 'CARD' | 'BANK_TRANSFER' | 'CHEQUE';
      reference?: string;
      notes?: string;
    },
    context: BusinessContext
  ): Promise<any> {
    try {
      const invoice = await this.prisma.invoice.findUnique({
        where: { id: invoiceId },
        include: {
          customer: true,
          receipts: true
        }
      });

      if (!invoice) {
        throw new Error(`Invoice ${invoiceId} not found`);
      }

      if (invoice.status !== 'OPEN') {
        throw new Error(`Cannot process payment for invoice with status ${invoice.status}`);
      }

      const paymentAmount = new Decimal(paymentData.amount);
      const invoiceAmount = new Decimal(invoice.amount);

      // Calculate total payments received
      const totalPaid = invoice.receipts
        .filter(r => r.applied)
        .reduce((sum, receipt) => sum.add(receipt.amount), new Decimal(0));

      const remainingAmount = invoiceAmount.sub(totalPaid);

      if (paymentAmount.gt(remainingAmount)) {
        throw new Error(`Payment amount ${paymentAmount} exceeds remaining balance ${remainingAmount}`);
      }

      // Create receipt
      const receipt = await this.prisma.receipt.create({
        data: {
          orgId: context.orgId,
          customerId: invoice.customerId,
          invoiceId,
          amount: paymentAmount.toNumber(),
          type: 'STANDARD',
          applied: true
        }
      });

      // Check if invoice is fully paid
      const newTotalPaid = totalPaid.add(paymentAmount);
      if (newTotalPaid.gte(invoiceAmount)) {
        await this.prisma.invoice.update({
          where: { id: invoiceId },
          data: { status: 'PAID' }
        });
      }

      // Publish event
      await this.publishBillingEvent('payment.received', { invoice, receipt, paymentData }, context);

      logger.info('Payment processed successfully', {
        invoiceId,
        receiptId: receipt.id,
        amount: paymentAmount.toString(),
        remainingBalance: remainingAmount.sub(paymentAmount).toString()
      });

      return {
        receipt,
        remainingBalance: remainingAmount.sub(paymentAmount),
        isFullyPaid: newTotalPaid.gte(invoiceAmount)
      };
    } catch (error) {
      logger.error('Failed to process payment', { invoiceId, paymentData, error });
      throw error;
    }
  }

  /**
   * Generate recurring invoices for long-term agreements
   */
  async generateRecurringInvoices(
    orgId: string,
    context: BusinessContext
  ): Promise<any[]> {
    try {
      // Find LTR agreements that need invoicing
      const agreements = await this.prisma.agreement.findMany({
        where: {
          orgId,
          type: 'LTR',
          status: 'ON_HIRE',
          // Add logic to find agreements due for invoicing
        },
        include: {
          customer: true,
          vehicle: true,
          invoices: {
            orderBy: { createdAt: 'desc' },
            take: 1
          }
        }
      });

      const results = [];

      for (const agreement of agreements) {
        try {
          // Check if invoice is due
          const lastInvoice = agreement.invoices[0];
          const daysSinceLastInvoice = lastInvoice ? 
            differenceInDays(new Date(), lastInvoice.createdAt) : 
            differenceInDays(new Date(), agreement.startDate);

          // Generate monthly invoices for LTR
          if (daysSinceLastInvoice >= 30) {
            const result = await this.generateAgreementInvoice(agreement.id, context);
            results.push(result);
          }
        } catch (error) {
          logger.error('Failed to generate recurring invoice for agreement', {
            agreementId: agreement.id,
            error
          });
        }
      }

      logger.info('Recurring invoices generated', {
        orgId,
        count: results.length
      });

      return results;
    } catch (error) {
      logger.error('Failed to generate recurring invoices', { orgId, error });
      throw error;
    }
  }

  /**
   * Calculate overdue penalties
   */
  async calculateOverduePenalties(orgId: string) {
    try {
      const overdueInvoices = await this.prisma.invoice.findMany({
        where: {
          orgId,
          status: 'OPEN',
          dueDate: { lt: new Date() }
        },
        include: {
          customer: true,
          agreement: true
        }
      });

      const penalties = [];

      for (const invoice of overdueInvoices) {
        const daysOverdue = differenceInDays(new Date(), invoice.dueDate!);
        
        // Calculate penalty based on days overdue
        const penaltyRate = this.getPenaltyRate(daysOverdue);
        const penaltyAmount = new Decimal(invoice.amount).mul(penaltyRate);

        if (penaltyAmount.gt(0)) {
          penalties.push({
            invoiceId: invoice.id,
            customerId: invoice.customerId,
            daysOverdue,
            penaltyRate,
            penaltyAmount,
            originalAmount: new Decimal(invoice.amount)
          });
        }
      }

      return penalties;
    } catch (error) {
      logger.error('Failed to calculate overdue penalties', { orgId, error });
      throw error;
    }
  }

  /**
   * Generate financial reports
   */
  async generateFinancialReport(
    orgId: string,
    period: { startDate: Date; endDate: Date },
    reportType: 'REVENUE' | 'OUTSTANDING' | 'COLLECTIONS'
  ): Promise<any> {
    try {
      switch (reportType) {
        case 'REVENUE':
          return await this.generateRevenueReport(orgId, period);
        case 'OUTSTANDING':
          return await this.generateOutstandingReport(orgId);
        case 'COLLECTIONS':
          return await this.generateCollectionsReport(orgId, period);
        default:
          throw new Error(`Unknown report type: ${reportType}`);
      }
    } catch (error) {
      logger.error('Failed to generate financial report', { orgId, reportType, error });
      throw error;
    }
  }

  /**
   * Search invoices with filters
   */
  async searchInvoices(
    orgId: string,
    criteria: SearchCriteria
  ): Promise<SearchResult<any>> {
    try {
      const where: any = { orgId };

      // Apply filters
      if (criteria.filters.customerId) {
        where.customerId = criteria.filters.customerId;
      }
      if (criteria.filters.status) {
        where.status = criteria.filters.status;
      }
      if (criteria.filters.fromDate) {
        where.issueDate = { gte: new Date(criteria.filters.fromDate) };
      }
      if (criteria.filters.toDate) {
        where.issueDate = { ...where.issueDate, lte: new Date(criteria.filters.toDate) };
      }

      // Count total
      const totalCount = await this.prisma.invoice.count({ where });

      // Get paginated results
      const page = criteria.page || 1;
      const limit = criteria.limit || 20;
      const skip = (page - 1) * limit;

      const invoices = await this.prisma.invoice.findMany({
        where,
        include: {
          customer: true,
          agreement: true,
          receipts: true
        },
        orderBy: criteria.sortBy ?
          { [criteria.sortBy]: (criteria.sortOrder?.toLowerCase() as 'asc' | 'desc') || 'desc' } :
          { createdAt: 'desc' },
        skip,
        take: limit
      });

      return {
        items: invoices,
        totalCount,
        page,
        limit,
        hasMore: skip + limit < totalCount
      };
    } catch (error) {
      logger.error('Failed to search invoices', { orgId, criteria, error });
      throw error;
    }
  }

  // Private helper methods

  private async calculateAgreementBilling(
    agreement: any,
    options: any
  ): Promise<BillingCalculation> {
    const breakdown: BillingLineItem[] = [];
    let totalAmount = new Decimal(0);

    // Base rental amount
    if (agreement.total) {
      const baseAmount = new Decimal(agreement.total);
      totalAmount = totalAmount.add(baseAmount);
      
      const durationDays = differenceInDays(agreement.endDate, agreement.startDate);
      breakdown.push({
        description: `Rental (${format(agreement.startDate, 'dd/MM/yyyy')} - ${format(agreement.endDate, 'dd/MM/yyyy')})`,
        quantity: durationDays,
        unitPrice: baseAmount.div(durationDays),
        totalPrice: baseAmount,
        category: 'RENTAL'
      });
    }

    // Security deposit
    if (options.includeDeposit) {
      const depositAmount = new Decimal(500); // TODO: Get from configuration
      totalAmount = totalAmount.add(depositAmount);
      breakdown.push({
        description: 'Security Deposit',
        quantity: 1,
        unitPrice: depositAmount,
        totalPrice: depositAmount,
        category: 'RENTAL'
      });
    }

    // Insurance
    const insuranceAmount = new Decimal(50); // TODO: Calculate based on vehicle and coverage
    totalAmount = totalAmount.add(insuranceAmount);
    breakdown.push({
      description: 'Insurance Coverage',
      quantity: 1,
      unitPrice: insuranceAmount,
      totalPrice: insuranceAmount,
      category: 'INSURANCE'
    });

    // VAT (5% in UAE)
    const vatRate = new Decimal(0.05);
    const vatAmount = totalAmount.mul(vatRate);
    totalAmount = totalAmount.add(vatAmount);
    breakdown.push({
      description: 'VAT (5%)',
      quantity: 1,
      unitPrice: vatAmount,
      totalPrice: vatAmount,
      category: 'TAX'
    });

    return {
      baseAmount: new Decimal(agreement.total || 0),
      additionalCharges: insuranceAmount,
      discounts: new Decimal(0),
      taxes: vatAmount,
      totalAmount,
      breakdown
    };
  }

  private getPenaltyRate(daysOverdue: number): number {
    if (daysOverdue <= 7) return 0;
    if (daysOverdue <= 30) return 0.01; // 1% per month
    if (daysOverdue <= 60) return 0.02; // 2% per month
    return 0.03; // 3% per month for 60+ days
  }

  private async generateRevenueReport(orgId: string, period: { startDate: Date; endDate: Date }) {
    const invoices = await this.prisma.invoice.findMany({
      where: {
        orgId,
        issueDate: {
          gte: period.startDate,
          lte: period.endDate
        }
      },
      include: {
        receipts: true,
        agreement: true
      }
    });

    const totalInvoiced = invoices.reduce((sum, inv) => sum.add(inv.amount), new Decimal(0));
    const totalCollected = invoices.reduce((sum, inv) => {
      const collected = inv.receipts
        .filter(r => r.applied)
        .reduce((receiptSum, receipt) => receiptSum.add(receipt.amount), new Decimal(0));
      return sum.add(collected);
    }, new Decimal(0));

    return {
      period,
      totalInvoiced,
      totalCollected,
      collectionRate: totalInvoiced.gt(0) ? totalCollected.div(totalInvoiced).mul(100) : new Decimal(0),
      invoiceCount: invoices.length,
      averageInvoiceValue: invoices.length > 0 ? totalInvoiced.div(invoices.length) : new Decimal(0)
    };
  }

  private async generateOutstandingReport(orgId: string) {
    const openInvoices = await this.prisma.invoice.findMany({
      where: {
        orgId,
        status: 'OPEN'
      },
      include: {
        customer: true,
        receipts: true
      }
    });

    const outstanding = openInvoices.map(invoice => {
      const totalPaid = invoice.receipts
        .filter(r => r.applied)
        .reduce((sum, receipt) => sum.add(receipt.amount), new Decimal(0));
      
      const balance = new Decimal(invoice.amount).sub(totalPaid);
      const daysOverdue = invoice.dueDate ? differenceInDays(new Date(), invoice.dueDate) : 0;

      return {
        invoiceId: invoice.id,
        customerId: invoice.customerId,
        customerName: invoice.customer.name,
        invoiceAmount: new Decimal(invoice.amount),
        paidAmount: totalPaid,
        balance,
        daysOverdue: Math.max(0, daysOverdue),
        isOverdue: daysOverdue > 0
      };
    });

    const totalOutstanding = outstanding.reduce((sum, item) => sum.add(item.balance), new Decimal(0));
    const overdueAmount = outstanding
      .filter(item => item.isOverdue)
      .reduce((sum, item) => sum.add(item.balance), new Decimal(0));

    return {
      totalOutstanding,
      overdueAmount,
      currentAmount: totalOutstanding.sub(overdueAmount),
      invoiceCount: outstanding.length,
      overdueCount: outstanding.filter(item => item.isOverdue).length,
      details: outstanding
    };
  }

  private async generateCollectionsReport(orgId: string, period: { startDate: Date; endDate: Date }) {
    const receipts = await this.prisma.receipt.findMany({
      where: {
        orgId,
        applied: true,
        createdAt: {
          gte: period.startDate,
          lte: period.endDate
        }
      },
      include: {
        customer: true,
        invoice: true
      }
    });

    const totalCollected = receipts.reduce((sum, receipt) => sum.add(receipt.amount), new Decimal(0));
    const collectionsByType = receipts.reduce((acc, receipt) => {
      const type = receipt.type;
      acc[type] = (acc[type] || new Decimal(0)).add(receipt.amount);
      return acc;
    }, {} as Record<string, Decimal>);

    return {
      period,
      totalCollected,
      collectionsByType,
      receiptCount: receipts.length,
      averageReceiptValue: receipts.length > 0 ? totalCollected.div(receipts.length) : new Decimal(0),
      details: receipts
    };
  }

  private async publishBillingEvent(
    eventType: string,
    data: any,
    context: BusinessContext
  ) {
    const event: BusinessEvent = {
      id: `${eventType}-${data.id || data.invoice?.id}-${Date.now()}`,
      type: eventType,
      entityType: 'INVOICE',
      entityId: data.id || data.invoice?.id,
      orgId: context.orgId,
      data,
      timestamp: new Date().toISOString(),
      userId: context.userId
    };

    await publishEvent(eventType, event);
  }
}
