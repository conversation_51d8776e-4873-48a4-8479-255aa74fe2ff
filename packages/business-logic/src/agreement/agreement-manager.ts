import { getPrisma } from '@a-realm/db';
import { createLogger } from '@a-realm/logger';
import { publishEvent } from '@a-realm/messaging';
import { addDays, differenceInDays, differenceInHours } from 'date-fns';
import { Decimal } from 'decimal.js';

import {
  BusinessContext,
  RentalPeriod,
  AgreementValidation,
  BillingCalculation,
  VehicleHandover,
  VehicleReturn,
  BusinessEvent,
  PricingContext,
  RentalRates
} from '../types/business-types';

const logger = createLogger('agreement-manager');

export class AgreementManager {
  private prisma = getPrisma();

  /**
   * Create a rental agreement from a reservation
   */
  async createAgreementFromReservation(
    reservationId: string,
    context: BusinessContext,
    additionalData: {
      agreementType?: 'STR' | 'LTR' | 'SCHOOL' | 'CHAUFFEUR' | 'IB';
      customTerms?: string;
      securityDeposit?: number;
      insuranceRequired?: boolean;
    } = {}
  ): Promise<any> {
    try {
      // Get reservation with related data
      const reservation = await this.prisma.reservation.findUnique({
        where: { id: reservationId },
        include: {
          customer: true,
          vehicle: true
        }
      });

      if (!reservation) {
        throw new Error(`Reservation ${reservationId} not found`);
      }

      if (reservation.status !== 'CONFIRMED') {
        throw new Error(`Cannot create agreement from reservation with status ${reservation.status}`);
      }

      if (!reservation.vehicleId) {
        throw new Error('Reservation must have a vehicle assigned');
      }

      // Validate agreement creation
      const validation = await this.validateAgreementCreation(reservation, additionalData);
      if (!validation.isValid) {
        throw new Error(`Agreement validation failed: ${validation.errors.join(', ')}`);
      }

      // Calculate pricing
      const pricing = await this.calculateAgreementPricing(reservation, additionalData);

      // Determine agreement type
      const agreementType = additionalData.agreementType || this.determineAgreementType(reservation);

      // Create agreement
      const agreement = await this.prisma.agreement.create({
        data: {
          orgId: context.orgId,
          type: agreementType,
          customerId: reservation.customerId,
          vehicleId: reservation.vehicleId,
          reservationId: reservation.id,
          status: 'CREATED',
          startDate: reservation.fromDate,
          endDate: reservation.toDate,
          total: pricing.totalAmount.toNumber(),
          currency: 'AED'
        },
        include: {
          customer: true,
          vehicle: true,
          reservation: true
        }
      });

      // Update vehicle status
      await this.prisma.vehicle.update({
        where: { id: reservation.vehicleId },
        data: { status: 'ON_HIRE' }
      });

      // Publish event
      await this.publishAgreementEvent('agreement.created', agreement, context);

      logger.info('Agreement created successfully', {
        agreementId: agreement.id,
        reservationId,
        type: agreementType,
        total: pricing.totalAmount.toString()
      });

      return {
        agreement,
        pricing
      };
    } catch (error) {
      logger.error('Failed to create agreement from reservation', { reservationId, error });
      throw error;
    }
  }

  /**
   * Process vehicle handover
   */
  async processVehicleHandover(
    agreementId: string,
    handoverData: Omit<VehicleHandover, 'agreementId'>,
    context: BusinessContext
  ): Promise<any> {
    try {
      const agreement = await this.prisma.agreement.findUnique({
        where: { id: agreementId },
        include: { customer: true, vehicle: true }
      });

      if (!agreement) {
        throw new Error(`Agreement ${agreementId} not found`);
      }

      if (agreement.status !== 'CREATED') {
        throw new Error(`Cannot handover vehicle for agreement in status ${agreement.status}`);
      }

      // Record initial odometer reading
      await this.prisma.odometerReading.create({
        data: {
          agreementId,
          value: handoverData.odometerReading,
          recordedAt: handoverData.handoverDate
        }
      });

      // Record initial fuel reading
      await this.prisma.fuelReading.create({
        data: {
          agreementId,
          levelPct: handoverData.fuelLevel,
          recordedAt: handoverData.handoverDate
        }
      });

      // Update agreement status to ON_HIRE
      const updatedAgreement = await this.prisma.agreement.update({
        where: { id: agreementId },
        data: { status: 'ON_HIRE' },
        include: { customer: true, vehicle: true }
      });

      // Publish event
      await this.publishAgreementEvent('agreement.handover_completed', updatedAgreement, context);

      logger.info('Vehicle handover completed', {
        agreementId,
        vehicleId: agreement.vehicleId,
        odometerReading: handoverData.odometerReading,
        fuelLevel: handoverData.fuelLevel
      });

      return {
        agreement: updatedAgreement,
        handover: { ...handoverData, agreementId }
      };
    } catch (error) {
      logger.error('Failed to process vehicle handover', { agreementId, error });
      throw error;
    }
  }

  /**
   * Process vehicle return
   */
  async processVehicleReturn(
    agreementId: string,
    returnData: Omit<VehicleReturn, 'agreementId'>,
    context: BusinessContext
  ): Promise<any> {
    try {
      const agreement = await this.prisma.agreement.findUnique({
        where: { id: agreementId },
        include: { 
          customer: true, 
          vehicle: true,
          odometerReadings: { orderBy: { recordedAt: 'asc' } },
          fuelReadings: { orderBy: { recordedAt: 'asc' } }
        }
      });

      if (!agreement) {
        throw new Error(`Agreement ${agreementId} not found`);
      }

      if (agreement.status !== 'ON_HIRE') {
        throw new Error(`Cannot return vehicle for agreement in status ${agreement.status}`);
      }

      // Record final odometer reading
      await this.prisma.odometerReading.create({
        data: {
          agreementId,
          value: returnData.odometerReading,
          recordedAt: returnData.returnDate
        }
      });

      // Record final fuel reading
      await this.prisma.fuelReading.create({
        data: {
          agreementId,
          levelPct: returnData.fuelLevel,
          recordedAt: returnData.returnDate
        }
      });

      // Calculate additional charges
      const additionalCharges = await this.calculateReturnCharges(
        agreement,
        returnData,
        context.orgId
      );

      // Update agreement status and end date
      const updatedAgreement = await this.prisma.agreement.update({
        where: { id: agreementId },
        data: { 
          status: 'CLOSED',
          endDate: returnData.returnDate
        },
        include: { customer: true, vehicle: true }
      });

      // Update vehicle status back to available
      await this.prisma.vehicle.update({
        where: { id: agreement.vehicleId },
        data: { status: 'AVAILABLE' }
      });

      // Generate final invoice if there are additional charges
      if (additionalCharges.totalAmount.gt(0)) {
        await this.generateAdditionalChargesInvoice(agreement, additionalCharges, context);
      }

      // Publish event
      await this.publishAgreementEvent('agreement.return_completed', updatedAgreement, context);

      logger.info('Vehicle return completed', {
        agreementId,
        vehicleId: agreement.vehicleId,
        additionalCharges: additionalCharges.totalAmount.toString()
      });

      return {
        agreement: updatedAgreement,
        return: { ...returnData, agreementId },
        additionalCharges
      };
    } catch (error) {
      logger.error('Failed to process vehicle return', { agreementId, error });
      throw error;
    }
  }

  /**
   * Extend an existing agreement
   */
  async extendAgreement(
    agreementId: string,
    newEndDate: Date,
    context: BusinessContext
  ): Promise<any> {
    try {
      const agreement = await this.prisma.agreement.findUnique({
        where: { id: agreementId },
        include: { customer: true, vehicle: true }
      });

      if (!agreement) {
        throw new Error(`Agreement ${agreementId} not found`);
      }

      if (agreement.status !== 'ON_HIRE') {
        throw new Error(`Cannot extend agreement in status ${agreement.status}`);
      }

      if (newEndDate <= agreement.endDate!) {
        throw new Error('New end date must be after current end date');
      }

      // Check vehicle availability for extension period
      const availability = await this.checkVehicleAvailabilityForExtension(
        agreement.vehicleId,
        agreement.endDate!,
        newEndDate,
        agreementId
      );

      if (!availability.isAvailable) {
        throw new Error('Vehicle is not available for the extension period');
      }

      // Calculate additional charges for extension
      const extensionCharges = await this.calculateExtensionCharges(
        agreement,
        newEndDate,
        context.orgId
      );

      // Update agreement
      const updatedAgreement = await this.prisma.agreement.update({
        where: { id: agreementId },
        data: { 
          endDate: newEndDate,
          total: new Decimal(agreement.total || 0).add(extensionCharges.totalAmount).toNumber()
        },
        include: { customer: true, vehicle: true }
      });

      // Generate invoice for extension charges
      if (extensionCharges.totalAmount.gt(0)) {
        await this.generateExtensionInvoice(agreement, extensionCharges, context);
      }

      // Publish event
      await this.publishAgreementEvent('agreement.extended', updatedAgreement, context);

      logger.info('Agreement extended successfully', {
        agreementId,
        originalEndDate: agreement.endDate,
        newEndDate,
        extensionCharges: extensionCharges.totalAmount.toString()
      });

      return {
        agreement: updatedAgreement,
        extensionCharges
      };
    } catch (error) {
      logger.error('Failed to extend agreement', { agreementId, error });
      throw error;
    }
  }

  /**
   * Calculate late return penalties
   */
  async calculateLateReturnPenalty(
    agreementId: string,
    actualReturnDate: Date
  ): Promise<BillingCalculation> {
    try {
      const agreement = await this.prisma.agreement.findUnique({
        where: { id: agreementId }
      });

      if (!agreement || !agreement.endDate) {
        throw new Error('Agreement or end date not found');
      }

      const hoursLate = differenceInHours(actualReturnDate, agreement.endDate);
      
      if (hoursLate <= 0) {
        return {
          baseAmount: new Decimal(0),
          additionalCharges: new Decimal(0),
          discounts: new Decimal(0),
          taxes: new Decimal(0),
          totalAmount: new Decimal(0),
          breakdown: []
        };
      }

      // TODO: Get late fee policy from org configuration
      const graceHours = 2;
      const hourlyRate = new Decimal(50);
      const dailyRate = new Decimal(200);
      const maxDailyFee = new Decimal(500);

      const chargeableHours = Math.max(0, hoursLate - graceHours);
      const days = Math.floor(chargeableHours / 24);
      const remainingHours = chargeableHours % 24;

      let penalty = new Decimal(0);
      const breakdown = [];

      if (days > 0) {
        const dailyPenalty = dailyRate.mul(days).lessThan(maxDailyFee.mul(days)) ?
          dailyRate.mul(days) : maxDailyFee.mul(days);
        penalty = penalty.add(dailyPenalty);
        breakdown.push({
          description: `Late return penalty (${days} days)`,
          quantity: days,
          unitPrice: dailyRate,
          totalPrice: dailyPenalty,
          category: 'PENALTY' as const
        });
      }

      if (remainingHours > 0) {
        const hourlyPenalty = hourlyRate.mul(remainingHours);
        penalty = penalty.add(hourlyPenalty);
        breakdown.push({
          description: `Late return penalty (${remainingHours} hours)`,
          quantity: remainingHours,
          unitPrice: hourlyRate,
          totalPrice: hourlyPenalty,
          category: 'PENALTY' as const
        });
      }

      return {
        baseAmount: penalty,
        additionalCharges: new Decimal(0),
        discounts: new Decimal(0),
        taxes: new Decimal(0),
        totalAmount: penalty,
        breakdown
      };
    } catch (error) {
      logger.error('Failed to calculate late return penalty', { agreementId, error });
      throw error;
    }
  }

  // Private helper methods

  private async validateAgreementCreation(
    reservation: any,
    additionalData: any
  ): Promise<AgreementValidation> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const businessRuleViolations: string[] = [];

    // Check customer eligibility
    const customer = reservation.customer;
    if (!customer.email && !customer.phone) {
      warnings.push('Customer has no contact information');
    }

    // Check vehicle condition
    const vehicle = reservation.vehicle;
    if (vehicle.status !== 'AVAILABLE') {
      errors.push(`Vehicle is not available (status: ${vehicle.status})`);
    }

    // Check rental period
    const durationDays = differenceInDays(reservation.toDate, reservation.fromDate);
    if (durationDays < 1) {
      errors.push('Rental period must be at least 1 day');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      businessRuleViolations
    };
  }

  private determineAgreementType(reservation: any): 'STR' | 'LTR' | 'SCHOOL' | 'CHAUFFEUR' | 'IB' {
    const durationDays = differenceInDays(reservation.toDate, reservation.fromDate);
    
    if (durationDays <= 30) {
      return 'STR'; // Short Term Rental
    } else {
      return 'LTR'; // Long Term Rental
    }
  }

  private async calculateAgreementPricing(
    reservation: any,
    additionalData: any
  ): Promise<BillingCalculation> {
    // TODO: Implement comprehensive pricing calculation
    // This is a simplified version
    const durationDays = differenceInDays(reservation.toDate, reservation.fromDate);
    const dailyRate = new Decimal(100); // TODO: Get from vehicle/rate configuration
    
    const baseAmount = dailyRate.mul(durationDays);
    const securityDeposit = new Decimal(additionalData.securityDeposit || 500);
    const insuranceFee = additionalData.insuranceRequired ? new Decimal(50) : new Decimal(0);
    
    const totalAmount = baseAmount.add(securityDeposit).add(insuranceFee);

    return {
      baseAmount,
      additionalCharges: securityDeposit.add(insuranceFee),
      discounts: new Decimal(0),
      taxes: new Decimal(0),
      totalAmount,
      breakdown: [
        {
          description: `Rental (${durationDays} days)`,
          quantity: durationDays,
          unitPrice: dailyRate,
          totalPrice: baseAmount,
          category: 'RENTAL'
        },
        {
          description: 'Security Deposit',
          quantity: 1,
          unitPrice: securityDeposit,
          totalPrice: securityDeposit,
          category: 'RENTAL'
        }
      ]
    };
  }

  private async checkVehicleAvailabilityForExtension(
    vehicleId: string,
    currentEndDate: Date,
    newEndDate: Date,
    excludeAgreementId: string
  ) {
    const conflictingAgreements = await this.prisma.agreement.findMany({
      where: {
        vehicleId,
        id: { not: excludeAgreementId },
        status: { in: ['CREATED', 'ON_HIRE'] },
        startDate: { lte: newEndDate },
        endDate: { gte: currentEndDate }
      }
    });

    return {
      isAvailable: conflictingAgreements.length === 0,
      conflicts: conflictingAgreements
    };
  }

  private async calculateReturnCharges(
    agreement: any,
    returnData: any,
    orgId: string
  ): Promise<BillingCalculation> {
    let totalCharges = new Decimal(0);
    const breakdown = [];

    // Calculate fuel charges
    const initialFuel = agreement.fuelReadings[0]?.levelPct || 100;
    const finalFuel = returnData.fuelLevel;
    const fuelDifference = initialFuel - finalFuel;

    if (fuelDifference > 5) { // 5% tolerance
      const fuelCharge = new Decimal(fuelDifference).mul(2); // AED 2 per percentage point
      totalCharges = totalCharges.add(fuelCharge);
      breakdown.push({
        description: `Fuel refill charge (${fuelDifference}%)`,
        quantity: fuelDifference,
        unitPrice: new Decimal(2),
        totalPrice: fuelCharge,
        category: 'FUEL' as const
      });
    }

    // Calculate damage charges
    for (const damage of returnData.damageReport || []) {
      totalCharges = totalCharges.add(damage.estimatedCost);
      breakdown.push({
        description: `Damage repair: ${damage.description}`,
        quantity: 1,
        unitPrice: damage.estimatedCost,
        totalPrice: damage.estimatedCost,
        category: 'PENALTY' as const
      });
    }

    // Calculate late return penalty
    if (returnData.returnDate > agreement.endDate) {
      const latePenalty = await this.calculateLateReturnPenalty(agreement.id, returnData.returnDate);
      totalCharges = totalCharges.add(latePenalty.totalAmount);
      breakdown.push(...latePenalty.breakdown);
    }

    return {
      baseAmount: totalCharges,
      additionalCharges: new Decimal(0),
      discounts: new Decimal(0),
      taxes: new Decimal(0),
      totalAmount: totalCharges,
      breakdown
    };
  }

  private async calculateExtensionCharges(
    agreement: any,
    newEndDate: Date,
    orgId: string
  ): Promise<BillingCalculation> {
    const extensionDays = differenceInDays(newEndDate, agreement.endDate!);
    const dailyRate = new Decimal(100); // TODO: Get from configuration
    const extensionAmount = dailyRate.mul(extensionDays);

    return {
      baseAmount: extensionAmount,
      additionalCharges: new Decimal(0),
      discounts: new Decimal(0),
      taxes: new Decimal(0),
      totalAmount: extensionAmount,
      breakdown: [
        {
          description: `Agreement extension (${extensionDays} days)`,
          quantity: extensionDays,
          unitPrice: dailyRate,
          totalPrice: extensionAmount,
          category: 'RENTAL'
        }
      ]
    };
  }

  private async generateAdditionalChargesInvoice(
    agreement: any,
    charges: BillingCalculation,
    context: BusinessContext
  ) {
    await this.prisma.invoice.create({
      data: {
        orgId: context.orgId,
        agreementId: agreement.id,
        customerId: agreement.customerId,
        amount: charges.totalAmount.toNumber(),
        currency: 'AED',
        status: 'OPEN'
      }
    });
  }

  private async generateExtensionInvoice(
    agreement: any,
    charges: BillingCalculation,
    context: BusinessContext
  ) {
    await this.prisma.invoice.create({
      data: {
        orgId: context.orgId,
        agreementId: agreement.id,
        customerId: agreement.customerId,
        amount: charges.totalAmount.toNumber(),
        currency: 'AED',
        status: 'OPEN'
      }
    });
  }

  private async publishAgreementEvent(
    eventType: string,
    agreement: any,
    context: BusinessContext
  ) {
    const event: BusinessEvent = {
      id: `${eventType}-${agreement.id}-${Date.now()}`,
      type: eventType,
      entityType: 'AGREEMENT',
      entityId: agreement.id,
      orgId: context.orgId,
      data: {
        agreementId: agreement.id,
        customerId: agreement.customerId,
        vehicleId: agreement.vehicleId,
        type: agreement.type,
        status: agreement.status,
        startDate: agreement.startDate,
        endDate: agreement.endDate,
        total: agreement.total
      },
      timestamp: new Date().toISOString(),
      userId: context.userId
    };

    await publishEvent(eventType, event);
  }
}
