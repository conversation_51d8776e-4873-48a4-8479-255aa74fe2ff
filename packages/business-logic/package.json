{"name": "@a-realm/business-logic", "version": "0.1.0", "private": true, "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc -p tsconfig.json", "dev": "tsc -w -p tsconfig.json", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "typecheck": "tsc -p tsconfig.json"}, "dependencies": {"@a-realm/config": "workspace:*", "@a-realm/db": "workspace:*", "@a-realm/logger": "workspace:*", "@a-realm/messaging": "workspace:*", "decimal.js": "^10.4.3", "date-fns": "^3.6.0"}, "devDependencies": {"typescript": "^5.4.5"}}