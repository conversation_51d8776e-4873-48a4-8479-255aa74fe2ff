// Export all error handling utilities
export { AppError, ErrorFactory } from './AppError';
export { globalErrorHandler, asyncHandler, notFoundHandler } from './middleware';
export { setupProcessHandlers, isErrorHandlerHealthy } from './process-handlers';
export * from './types';

// Re-export for convenience
export {
  ErrorCode,
  HttpStatus,
  type AppError as IAppError,
  type ErrorResponse,
  type ErrorHandlerOptions
} from './types';
