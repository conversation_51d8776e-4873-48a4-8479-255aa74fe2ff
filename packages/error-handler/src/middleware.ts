import { Request, Response, NextFunction } from 'express';
import { createLogger } from '@a-realm/logger';
import { config } from '@a-realm/config';
import { AppError } from './AppError';
import { ErrorCode, HttpStatus, ErrorResponse, ErrorHandlerOptions } from './types';

const logger = createLogger(config.logLevel);

/**
 * Global error handling middleware
 */
export function globalErrorHandler(options: ErrorHandlerOptions = {}) {
  const {
    includeStack = config.nodeEnv === 'development',
    includeContext = config.nodeEnv === 'development',
    logErrors = true,
    trustProxy = false
  } = options;

  return (error: Error, req: Request, res: Response, next: NextFunction) => {
    // Skip if response already sent
    if (res.headersSent) {
      return next(error);
    }

    const requestId = (req as any).requestId || 'unknown';
    let appError: AppError;

    // Convert to AppError if not already
    if (error instanceof AppError) {
      appError = error;
      // Create new AppError with requestId if not set
      if (!appError.requestId) {
        appError = new AppError(
          appError.code,
          appError.message,
          appError.statusCode,
          appError.isOperational,
          appError.context,
          requestId
        );
      }
    } else {
      // Handle known error types
      appError = convertToAppError(error, requestId);
    }

    // Log error
    if (logErrors) {
      logError(appError, req, trustProxy);
    }

    // Send error response
    const errorResponse = createErrorResponse(appError, {
      includeStack,
      includeContext
    });

    res.status(appError.statusCode).json(errorResponse);
  };
}

/**
 * Convert unknown errors to AppError
 */
function convertToAppError(error: Error, requestId: string): AppError {
  // JWT errors
  if (error.name === 'JsonWebTokenError') {
    return new AppError(
      ErrorCode.INVALID_TOKEN,
      'Invalid token',
      HttpStatus.UNAUTHORIZED,
      true,
      undefined,
      requestId
    );
  }

  if (error.name === 'TokenExpiredError') {
    return new AppError(
      ErrorCode.TOKEN_EXPIRED,
      'Token expired',
      HttpStatus.UNAUTHORIZED,
      true,
      undefined,
      requestId
    );
  }

  // Validation errors (Zod)
  if (error.name === 'ZodError') {
    return new AppError(
      ErrorCode.VALIDATION_ERROR,
      'Validation failed',
      HttpStatus.BAD_REQUEST,
      true,
      { details: (error as any).errors },
      requestId
    );
  }

  // Prisma errors
  if (error.name === 'PrismaClientKnownRequestError') {
    const prismaError = error as any;
    
    // Unique constraint violation
    if (prismaError.code === 'P2002') {
      return new AppError(
        ErrorCode.RESOURCE_CONFLICT,
        'Resource already exists',
        HttpStatus.CONFLICT,
        true,
        { field: prismaError.meta?.target },
        requestId
      );
    }

    // Record not found
    if (prismaError.code === 'P2025') {
      return new AppError(
        ErrorCode.RESOURCE_NOT_FOUND,
        'Record not found',
        HttpStatus.NOT_FOUND,
        true,
        undefined,
        requestId
      );
    }

    // Foreign key constraint violation
    if (prismaError.code === 'P2003') {
      return new AppError(
        ErrorCode.VALIDATION_ERROR,
        'Invalid reference',
        HttpStatus.BAD_REQUEST,
        true,
        { field: prismaError.meta?.field_name },
        requestId
      );
    }
  }

  // Database connection errors
  if (error.name === 'PrismaClientInitializationError' || 
      error.name === 'PrismaClientUnknownRequestError') {
    return new AppError(
      ErrorCode.DATABASE_ERROR,
      'Database connection error',
      HttpStatus.INTERNAL_SERVER_ERROR,
      false,
      { originalError: error.message },
      requestId
    );
  }

  // Rate limiting errors
  if (error.message.includes('Too many requests')) {
    return new AppError(
      ErrorCode.RATE_LIMIT_EXCEEDED,
      'Too many requests',
      HttpStatus.TOO_MANY_REQUESTS,
      true,
      undefined,
      requestId
    );
  }

  // Default to internal server error
  return new AppError(
    ErrorCode.INTERNAL_SERVER_ERROR,
    config.nodeEnv === 'production' ? 'Internal server error' : error.message,
    HttpStatus.INTERNAL_SERVER_ERROR,
    false,
    { originalError: error.message, stack: error.stack },
    requestId
  );
}

/**
 * Log error with appropriate level
 */
function logError(error: AppError, req: Request, trustProxy: boolean) {
  const clientIp = trustProxy 
    ? req.headers['x-forwarded-for'] || req.headers['x-real-ip'] || req.connection.remoteAddress
    : req.connection.remoteAddress;

  const logData = {
    error: error.toJSON(),
    request: {
      method: req.method,
      url: req.url,
      headers: sanitizeHeaders(req.headers),
      body: sanitizeBody(req.body),
      query: req.query,
      params: req.params,
      ip: clientIp,
      userAgent: req.headers['user-agent']
    }
  };

  // Log level based on error type
  if (error.statusCode >= 500) {
    logger.error('Server error occurred', logData);
  } else if (error.statusCode >= 400) {
    logger.warn('Client error occurred', logData);
  } else {
    logger.info('Error occurred', logData);
  }
}

/**
 * Create standardized error response
 */
function createErrorResponse(
  error: AppError, 
  options: { includeStack?: boolean; includeContext?: boolean }
): ErrorResponse {
  const response: ErrorResponse = {
    error: {
      code: error.code,
      message: error.message,
      requestId: error.requestId,
      timestamp: new Date().toISOString()
    }
  };

  // Include additional details in development
  if (options.includeContext && error.context) {
    response.error.details = error.context;
  }

  if (options.includeStack && error.stack) {
    (response.error as any).stack = error.stack;
  }

  return response;
}

/**
 * Sanitize headers for logging (remove sensitive data)
 */
function sanitizeHeaders(headers: any): any {
  const sanitized = { ...headers };
  
  // Remove sensitive headers
  delete sanitized.authorization;
  delete sanitized.cookie;
  delete sanitized['x-api-key'];
  
  return sanitized;
}

/**
 * Sanitize request body for logging (remove sensitive data)
 */
function sanitizeBody(body: any): any {
  if (!body || typeof body !== 'object') {
    return body;
  }

  const sanitized = { ...body };

  // Remove sensitive fields
  delete sanitized.password;
  delete sanitized.passwordHash;
  delete sanitized.token;
  delete sanitized.secret;

  return sanitized;
}

/**
 * Async error wrapper for route handlers
 */
export function asyncHandler(fn: Function) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * 404 handler middleware
 */
export function notFoundHandler() {
  return (req: Request, res: Response, next: NextFunction) => {
    const requestId = (req as any).requestId || 'unknown';
    const error = new AppError(
      ErrorCode.RESOURCE_NOT_FOUND,
      `Route ${req.method} ${req.path} not found`,
      HttpStatus.NOT_FOUND,
      true,
      undefined,
      requestId
    );
    next(error);
  };
}
