import { ErrorCode, HttpStatus, AppError as IAppError } from './types';

/**
 * Custom application error class
 */
export class AppError extends Error implements IAppError {
  public readonly code: ErrorCode;
  public readonly statusCode: HttpStatus;
  public readonly isOperational: boolean;
  public readonly context?: Record<string, any>;
  public readonly requestId?: string;

  constructor(
    code: ErrorCode,
    message: string,
    statusCode: HttpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
    isOperational: boolean = true,
    context?: Record<string, any>,
    requestId?: string
  ) {
    super(message);
    
    this.name = this.constructor.name;
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.context = context;
    this.requestId = requestId;

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * Convert error to JSON for logging
   */
  toJSON() {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      statusCode: this.statusCode,
      isOperational: this.isOperational,
      context: this.context,
      requestId: this.requestId,
      stack: this.stack
    };
  }
}

/**
 * Factory functions for common errors
 */
export class ErrorFactory {
  static unauthorized(message = 'Unauthorized', requestId?: string) {
    return new AppError(
      ErrorCode.UNAUTHORIZED,
      message,
      HttpStatus.UNAUTHORIZED,
      true,
      undefined,
      requestId
    );
  }

  static forbidden(message = 'Forbidden', requestId?: string) {
    return new AppError(
      ErrorCode.FORBIDDEN,
      message,
      HttpStatus.FORBIDDEN,
      true,
      undefined,
      requestId
    );
  }

  static notFound(resource = 'Resource', requestId?: string) {
    return new AppError(
      ErrorCode.RESOURCE_NOT_FOUND,
      `${resource} not found`,
      HttpStatus.NOT_FOUND,
      true,
      undefined,
      requestId
    );
  }

  static validation(message: string, details?: any, requestId?: string) {
    return new AppError(
      ErrorCode.VALIDATION_ERROR,
      message,
      HttpStatus.BAD_REQUEST,
      true,
      { details },
      requestId
    );
  }

  static conflict(message: string, requestId?: string) {
    return new AppError(
      ErrorCode.RESOURCE_CONFLICT,
      message,
      HttpStatus.CONFLICT,
      true,
      undefined,
      requestId
    );
  }

  static rateLimit(message = 'Too many requests', requestId?: string) {
    return new AppError(
      ErrorCode.RATE_LIMIT_EXCEEDED,
      message,
      HttpStatus.TOO_MANY_REQUESTS,
      true,
      undefined,
      requestId
    );
  }

  static internal(message = 'Internal server error', context?: Record<string, any>, requestId?: string) {
    return new AppError(
      ErrorCode.INTERNAL_SERVER_ERROR,
      message,
      HttpStatus.INTERNAL_SERVER_ERROR,
      false,
      context,
      requestId
    );
  }

  static database(message = 'Database error', context?: Record<string, any>, requestId?: string) {
    return new AppError(
      ErrorCode.DATABASE_ERROR,
      message,
      HttpStatus.INTERNAL_SERVER_ERROR,
      false,
      context,
      requestId
    );
  }

  static timeout(message = 'Request timeout', requestId?: string) {
    return new AppError(
      ErrorCode.TIMEOUT,
      message,
      HttpStatus.GATEWAY_TIMEOUT,
      true,
      undefined,
      requestId
    );
  }

  static serviceUnavailable(message = 'Service unavailable', requestId?: string) {
    return new AppError(
      ErrorCode.SERVICE_UNAVAILABLE,
      message,
      HttpStatus.SERVICE_UNAVAILABLE,
      true,
      undefined,
      requestId
    );
  }
}
