import { createLogger } from '@a-realm/logger';
import { config } from '@a-realm/config';

const logger = createLogger(config.logLevel);

/**
 * Setup global process error handlers
 */
export function setupProcessHandlers() {
  // Handle uncaught exceptions
  process.on('uncaughtException', (error: Error) => {
    logger.error('Uncaught Exception - shutting down gracefully', {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      }
    });

    // Give time for logs to flush
    setTimeout(() => {
      process.exit(1);
    }, 1000);
  });

  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
    logger.error('Unhandled Promise Rejection - shutting down gracefully', {
      reason: reason?.toString(),
      stack: reason?.stack,
      promise: promise.toString()
    });

    // Give time for logs to flush
    setTimeout(() => {
      process.exit(1);
    }, 1000);
  });

  // Handle SIGTERM (graceful shutdown)
  process.on('SIGTERM', () => {
    logger.info('SIGTERM received - shutting down gracefully');
    
    // Perform cleanup here (close DB connections, etc.)
    gracefulShutdown();
  });

  // Handle SIGINT (Ctrl+C)
  process.on('SIGINT', () => {
    logger.info('SIGINT received - shutting down gracefully');
    
    // Perform cleanup here
    gracefulShutdown();
  });

  logger.info('Process error handlers initialized');
}

/**
 * Graceful shutdown handler
 */
function gracefulShutdown() {
  // Close server connections
  // Close database connections
  // Clean up resources
  
  setTimeout(() => {
    logger.info('Graceful shutdown completed');
    process.exit(0);
  }, 5000); // Give 5 seconds for cleanup
}

/**
 * Health check for error handling system
 */
export function isErrorHandlerHealthy(): boolean {
  try {
    // Check if process handlers are attached
    const uncaughtListeners = process.listenerCount('uncaughtException');
    const unhandledListeners = process.listenerCount('unhandledRejection');
    
    return uncaughtListeners > 0 && unhandledListeners > 0;
  } catch (error) {
    logger.error('Error checking error handler health', { error });
    return false;
  }
}
