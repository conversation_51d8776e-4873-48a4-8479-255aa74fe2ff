{"name": "@a-realm/error-handler", "version": "0.1.0", "private": true, "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc -p tsconfig.json", "dev": "tsx watch src/index.ts", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "typecheck": "tsc -p tsconfig.json"}, "dependencies": {"@a-realm/logger": "workspace:*", "@a-realm/config": "workspace:*"}, "devDependencies": {"tsx": "^4.11.0", "typescript": "^5.4.5", "@types/node": "^20.0.0"}}