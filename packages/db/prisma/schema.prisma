generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_URL")
}

model Org {
  id        String   @id @default(uuid())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  users        User[]
  roles        Role[]
  customers    Customer[]
  vehicles     Vehicle[]
  reservations Reservation[]
  agreements   Agreement[]
  invoices     Invoice[]
  receipts     Receipt[]
  leaseQuotations LeaseQuotation[]
  salikCharges    SalikCharge[]
  trafficFines    TrafficFine[]
  importedFiles   ImportedFile[]
  integrationExports IntegrationExport[]
}

model User {
  id            String   @id @default(uuid())
  orgId         String
  email         String   @unique
  passwordHash  String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  org           Org      @relation(fields: [orgId], references: [id])
  roles         UserRole[]
}

model Role {
  id          String   @id @default(uuid())
  orgId       String
  name        String
  permissions String[]
  org         Org      @relation(fields: [orgId], references: [id])
  users       UserRole[]
  @@unique([orgId, name])
}

model UserRole {
  userId String
  roleId String
  user   User   @relation(fields: [userId], references: [id])
  role   Role   @relation(fields: [roleId], references: [id])
  @@id([userId, roleId])
}

enum CustomerType {
  CASH
  CREDIT
}

model Customer {
  id           String       @id @default(uuid())
  orgId        String
  type         CustomerType
  name         String
  email        String?
  phone        String?
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt
  org          Org          @relation(fields: [orgId], references: [id])
  reservations Reservation[]
  agreements   Agreement[]
  invoices     Invoice[]
  receipts     Receipt[]
  salikCharges SalikCharge[]
  trafficFines TrafficFine[]
  leaseQuotations LeaseQuotation[]
}

enum VehicleStatus {
  NEW
  AVAILABLE
  ON_HIRE
  MAINTENANCE
  SOLD
}

enum BusinessType {
  STR
  LTR
  SCHOOL
  CHAUFFEUR
  IB
}

model Vehicle {
  id                String       @id @default(uuid())
  orgId             String
  vin               String?      @unique
  plateNumber       String?
  status            VehicleStatus @default(NEW)
  registrationNumber String?
  salikTag          String?
  vmdSerial         String?
  businessType      BusinessType?
  location          String?
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @updatedAt
  org               Org          @relation(fields: [orgId], references: [id])
  reservations      Reservation[]
  agreements        Agreement[]
  salikCharges      SalikCharge[]
  trafficFines      TrafficFine[]
  leaseQuotations   LeaseQuotation[]
}

enum ReservationStatus {
  CREATED
  CONFIRMED
  CANCELLED
}

model Reservation {
  id         String             @id @default(uuid())
  orgId      String
  customerId String
  vehicleId  String?
  fromDate   DateTime
  toDate     DateTime
  status     ReservationStatus  @default(CREATED)
  notes      String?
  createdAt  DateTime           @default(now())
  updatedAt  DateTime           @updatedAt
  org        Org                @relation(fields: [orgId], references: [id])
  customer   Customer           @relation(fields: [customerId], references: [id])
  vehicle    Vehicle?           @relation(fields: [vehicleId], references: [id])
  agreements Agreement[]
}

enum AgreementType {
  STR
  LTR
  SCHOOL
  CHAUFFEUR
  IB
}

enum AgreementStatus {
  CREATED
  ON_HIRE
  CLOSED
}

model Agreement {
  id            String           @id @default(uuid())
  orgId         String
  type          AgreementType
  customerId    String
  vehicleId     String
  reservationId String?
  status        AgreementStatus  @default(CREATED)
  startDate     DateTime
  endDate       DateTime?
  total         Decimal?         @db.Decimal(12,2)
  currency      String?          @default("AED")
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
  org           Org              @relation(fields: [orgId], references: [id])
  customer      Customer         @relation(fields: [customerId], references: [id])
  vehicle       Vehicle          @relation(fields: [vehicleId], references: [id])
  reservation   Reservation?     @relation(fields: [reservationId], references: [id])
  odometerReadings OdometerReading[]
  fuelReadings     FuelReading[]
  invoices         Invoice[]
  schedules        AgreementSchedule[]
  salikCharges     SalikCharge[]
  trafficFines     TrafficFine[]
}

enum InvoiceStatus {
  OPEN
  PAID
  VOID
  CREDITED
}

model Invoice {
  id          String        @id @default(uuid())
  orgId       String
  agreementId String?
  customerId  String
  amount      Decimal       @db.Decimal(12,2)
  currency    String        @default("AED")
  status      InvoiceStatus @default(OPEN)
  issueDate   DateTime      @default(now())
  dueDate     DateTime?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  org         Org           @relation(fields: [orgId], references: [id])
  agreement   Agreement?    @relation(fields: [agreementId], references: [id])
  customer    Customer      @relation(fields: [customerId], references: [id])
  receipts    Receipt[]
  schedules   AgreementSchedule[]
}

enum ReceiptType {
  STANDARD
  PDC
}

model Receipt {
  id          String      @id @default(uuid())
  orgId       String
  customerId  String
  invoiceId   String?
  amount      Decimal     @db.Decimal(12,2)
  type        ReceiptType @default(STANDARD)
  applied     Boolean     @default(false)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  org         Org         @relation(fields: [orgId], references: [id])
  customer    Customer    @relation(fields: [customerId], references: [id])
  invoice     Invoice?    @relation(fields: [invoiceId], references: [id])
}

model OdometerReading {
  id          String    @id @default(uuid())
  agreementId String
  value       Int
  recordedAt  DateTime  @default(now())
  agreement   Agreement @relation(fields: [agreementId], references: [id])
}

model FuelReading {
  id          String    @id @default(uuid())
  agreementId String
  levelPct    Int
  recordedAt  DateTime  @default(now())
  agreement   Agreement @relation(fields: [agreementId], references: [id])
}

enum ExportStatus {
  PENDING
  EXPORTED
  FAILED
}

model IntegrationExport {
  id         String       @id @default(uuid())
  orgId      String
  exportType String       // AR_INVOICE, AR_RECEIPT, VEHICLE_ASSET, AGREEMENT_OPEN, AGREEMENT_CLOSE, etc.
  sourceType String
  sourceId   String
  status     ExportStatus @default(PENDING)
  attempts   Int          @default(0)
  lastError  String?
  filePath   String?
  createdAt  DateTime     @default(now())
  updatedAt  DateTime     @updatedAt
  org        Org          @relation(fields: [orgId], references: [id])
}

enum QuoteStatus {
  DRAFT
  APPROVED
  CONVERTED
  REJECTED
}

model LeaseQuotation {
  id            String   @id @default(uuid())
  orgId         String
  customerId    String
  vehicleId     String?
  startDate     DateTime
  endDate       DateTime?
  months        Int?
  monthlyAmount Decimal @db.Decimal(12,2)
  currency      String  @default("AED")
  status        QuoteStatus @default(DRAFT)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  org           Org      @relation(fields: [orgId], references: [id])
  customer      Customer @relation(fields: [customerId], references: [id])
  vehicle       Vehicle? @relation(fields: [vehicleId], references: [id])
}

enum ScheduleStatus {
  DUE
  INVOICED
  PAID
  CANCELLED
}

model AgreementSchedule {
  id          String         @id @default(uuid())
  agreementId String
  dueDate     DateTime
  amount      Decimal        @db.Decimal(12,2)
  currency    String         @default("AED")
  status      ScheduleStatus @default(DUE)
  invoiceId   String?
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  agreement   Agreement      @relation(fields: [agreementId], references: [id])
  invoice     Invoice?       @relation(fields: [invoiceId], references: [id])
}

/// Salik (toll) charge imported and optionally allocated to agreement/customer
model SalikCharge {
  id           String   @id @default(uuid())
  orgId        String
  vehicleId    String?
  agreementId  String?
  customerId   String?
  tag          String?
  plateNumber  String?
  amount       Decimal  @db.Decimal(12,2)
  incurredAt   DateTime
  sourceFileId String?
  status       String   @default("UNALLOCATED") // UNALLOCATED | ALLOCATED | INVOICED | WRITEOFF
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  org          Org      @relation(fields: [orgId], references: [id])
  vehicle      Vehicle? @relation(fields: [vehicleId], references: [id])
  agreement    Agreement? @relation(fields: [agreementId], references: [id])
  customer     Customer? @relation(fields: [customerId], references: [id])
}

/// Imported file metadata (for salik, fines, etc.)
model ImportedFile {
  id         String   @id @default(uuid())
  orgId      String
  type       String   // SALIK | FINES | OTHER
  name       String
  sizeBytes  Int
  checksum   String?
  createdAt  DateTime @default(now())
  org        Org      @relation(fields: [orgId], references: [id])
}

/// Traffic fine imported and optionally allocated to agreement/customer
model TrafficFine {
  id           String   @id @default(uuid())
  orgId        String
  vehicleId    String?
  agreementId  String?
  customerId   String?
  plateNumber  String?
  violationCode String?
  amount       Decimal  @db.Decimal(12,2)
  occurredAt   DateTime
  status       String   @default("UNALLOCATED") // UNALLOCATED | ALLOCATED | INVOICED | DISPUTED | WRITEOFF
  sourceFileId String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  org          Org      @relation(fields: [orgId], references: [id])
  vehicle      Vehicle? @relation(fields: [vehicleId], references: [id])
  agreement    Agreement? @relation(fields: [agreementId], references: [id])
  customer     Customer? @relation(fields: [customerId], references: [id])
}
