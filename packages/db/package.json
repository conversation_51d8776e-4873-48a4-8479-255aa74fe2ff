{"name": "@a-realm/db", "version": "0.1.0", "private": true, "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc -p tsconfig.json && prisma generate", "db:push": "prisma db push", "dev": "tsc -w -p tsconfig.json", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "migrate:dev": "prisma migrate dev --name init", "prisma:generate": "prisma generate", "seed": "tsx src/seed.ts"}, "dependencies": {"@prisma/client": "^5.15.0", "prisma": "^5.15.0", "bcryptjs": "^2.4.3", "@a-realm/logger": "workspace:*"}, "devDependencies": {"typescript": "^5.4.5"}}