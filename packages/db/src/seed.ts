import { getPrisma } from './index';
const bcrypt = require('bcryptjs');

async function main() {
  const prisma = getPrisma();
  const org = await prisma.org.upsert({
    where: { id: '00000000-0000-0000-0000-000000000001' },
    update: {},
    create: { id: '00000000-0000-0000-0000-000000000001', name: 'Demo Org' }
  });

  const adminRole = await prisma.role.upsert({
    where: { orgId_name: { orgId: org.id, name: 'admin' } },
    update: {},
    create: { orgId: org.id, name: 'admin', permissions: ['*'] }
  });

  const userRole = await prisma.role.upsert({
    where: { orgId_name: { orgId: org.id, name: 'user' } },
    update: {},
    create: { orgId: org.id, name: 'user', permissions: ['agreements:read', 'vehicles:read'] }
  });

  const adminPasswordHash = await bcrypt.hash('admin123', 10);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: { orgId: org.id, email: '<EMAIL>', passwordHash: adminPasswordHash }
  });

  await prisma.userRole.upsert({
    where: { userId_roleId: { userId: admin.id, roleId: adminRole.id } },
    update: {},
    create: { userId: admin.id, roleId: adminRole.id }
  });

  // Create a regular demo user
  const userPasswordHash = await bcrypt.hash('user123', 10);
  const demoUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: { orgId: org.id, email: '<EMAIL>', passwordHash: userPasswordHash }
  });

  await prisma.userRole.upsert({
    where: { userId_roleId: { userId: demoUser.id, roleId: userRole.id } },
    update: {},
    create: { userId: demoUser.id, roleId: userRole.id }
  });

  console.log('Seed complete:', { org: org.name, admin: admin.email });
}

main().catch((e) => {
  console.error(e);
  process.exit(1);
});
