"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const index_1 = require("./index");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
async function main() {
    const prisma = (0, index_1.getPrisma)();
    const org = await prisma.org.upsert({
        where: { id: '00000000-0000-0000-0000-000000000001' },
        update: {},
        create: { id: '00000000-0000-0000-0000-000000000001', name: 'Demo Org' }
    });
    const adminRole = await prisma.role.upsert({
        where: { orgId_name: { orgId: org.id, name: 'admin' } },
        update: {},
        create: { orgId: org.id, name: 'admin', permissions: ['*'] }
    });
    const userRole = await prisma.role.upsert({
        where: { orgId_name: { orgId: org.id, name: 'user' } },
        update: {},
        create: { orgId: org.id, name: 'user', permissions: ['agreements:read', 'vehicles:read'] }
    });
    const passwordHash = await bcryptjs_1.default.hash('admin123', 10);
    const admin = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: { orgId: org.id, email: '<EMAIL>', passwordHash }
    });
    await prisma.userRole.upsert({
        where: { userId_roleId: { userId: admin.id, roleId: adminRole.id } },
        update: {},
        create: { userId: admin.id, roleId: adminRole.id }
    });
    console.log('Seed complete:', { org: org.name, admin: admin.email });
}
main().catch((e) => {
    console.error(e);
    process.exit(1);
});
//# sourceMappingURL=seed.js.map