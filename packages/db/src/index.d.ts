import { PrismaClient, Prisma, CustomerType, VehicleStatus, BusinessType, ReservationStatus, AgreementType, AgreementStatus, InvoiceStatus, ReceiptType } from '@prisma/client';
export declare function getPrisma(): PrismaClient<Prisma.PrismaClientOptions, never, import("@prisma/client/runtime/library").DefaultArgs>;
/**
 * Initialize database connection with retry logic
 */
export declare function initializeDatabase(): Promise<void>;
/**
 * Check database health
 */
export declare function checkDatabaseHealth(): Promise<{
    healthy: boolean;
    error?: string;
}>;
/**
 * Gracefully disconnect from database
 */
export declare function disconnectDatabase(): Promise<void>;
/**
 * Get database connection status
 */
export declare function isDatabaseConnected(): boolean;
/**
 * Execute database operation with retry logic
 */
export declare function withRetry<T>(operation: () => Promise<T>, maxRetries?: number, delay?: number): Promise<T>;
export { Prisma };
export type { CustomerType, VehicleStatus, BusinessType, ReservationStatus, AgreementType, AgreementStatus, InvoiceStatus, ReceiptType, ExportStatus, QuoteStatus, ScheduleStatus } from '@prisma/client';
export type User = {
    id: string;
    orgId: string;
    email: string;
    passwordHash: string;
    createdAt: Date;
    updatedAt: Date;
};
export type Role = {
    id: string;
    orgId: string;
    name: string;
    permissions: string[];
};
export type Org = {
    id: string;
    name: string;
    createdAt: Date;
    updatedAt: Date;
};
export type Customer = {
    id: string;
    orgId: string;
    type: CustomerType;
    name: string;
    email?: string | null;
    phone?: string | null;
    createdAt: Date;
    updatedAt: Date;
};
export type Vehicle = {
    id: string;
    orgId: string;
    vin?: string | null;
    plateNumber?: string | null;
    status: VehicleStatus;
    registrationNumber?: string | null;
    salikTag?: string | null;
    vmdSerial?: string | null;
    businessType?: BusinessType | null;
    location?: string | null;
    createdAt: Date;
    updatedAt: Date;
};
export type Agreement = {
    id: string;
    orgId: string;
    type: AgreementType;
    customerId: string;
    vehicleId: string;
    reservationId?: string | null;
    status: AgreementStatus;
    startDate: Date;
    endDate?: Date | null;
    total?: any | null;
    currency?: string | null;
    createdAt: Date;
    updatedAt: Date;
};
export type Invoice = {
    id: string;
    orgId: string;
    agreementId?: string | null;
    customerId: string;
    amount: any;
    currency: string;
    status: InvoiceStatus;
    issueDate: Date;
    dueDate?: Date | null;
    createdAt: Date;
    updatedAt: Date;
};
export type Receipt = {
    id: string;
    orgId: string;
    customerId: string;
    invoiceId?: string | null;
    amount: any;
    type: ReceiptType;
    applied: boolean;
    createdAt: Date;
    updatedAt: Date;
};
export type Reservation = {
    id: string;
    orgId: string;
    customerId: string;
    vehicleId?: string | null;
    fromDate: Date;
    toDate: Date;
    status: ReservationStatus;
    notes?: string | null;
    createdAt: Date;
    updatedAt: Date;
};
