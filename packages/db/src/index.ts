import {
  PrismaClient,
  Prisma,
  CustomerType,
  VehicleStatus,
  BusinessType,
  ReservationStatus,
  AgreementType,
  AgreementStatus,
  InvoiceStatus,
  ReceiptType,
  ExportStatus,
  QuoteStatus,
  ScheduleStatus
} from '@prisma/client';
import { createLogger } from '@a-realm/logger';

const logger = createLogger('database');

let prisma: PrismaClient | undefined;
let isConnected = false;
let connectionRetries = 0;
const MAX_RETRIES = 5;
const RETRY_DELAY = 5000; // 5 seconds

export function getPrisma() {
  if (!prisma) {
    prisma = new PrismaClient({
      log: ['error', 'warn'],
      datasources: {
        db: {
          url: process.env.POSTGRES_URL
        }
      }
    });
  }
  return prisma;
}

/**
 * Initialize database connection with retry logic
 */
export async function initializeDatabase(): Promise<void> {
  const client = getPrisma();

  while (connectionRetries < MAX_RETRIES) {
    try {
      await client.$connect();
      await client.$queryRaw`SELECT 1`; // Test connection
      isConnected = true;
      connectionRetries = 0;
      logger.info('Database connected successfully');
      return;
    } catch (error) {
      connectionRetries++;
      isConnected = false;
      logger.error(`Database connection attempt ${connectionRetries} failed`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        retryIn: RETRY_DELAY / 1000
      });

      if (connectionRetries >= MAX_RETRIES) {
        logger.error('Max database connection retries exceeded');
        throw new Error(`Failed to connect to database after ${MAX_RETRIES} attempts`);
      }

      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
    }
  }
}

/**
 * Check database health
 */
export async function checkDatabaseHealth(): Promise<{ healthy: boolean; error?: string }> {
  try {
    const client = getPrisma();
    await client.$queryRaw`SELECT 1`;
    return { healthy: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown database error';
    logger.error('Database health check failed', { error: errorMessage });
    return { healthy: false, error: errorMessage };
  }
}

/**
 * Gracefully disconnect from database
 */
export async function disconnectDatabase(): Promise<void> {
  if (prisma) {
    try {
      await prisma.$disconnect();
      isConnected = false;
      logger.info('Database disconnected gracefully');
    } catch (error) {
      logger.error('Error during database disconnect', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      prisma = undefined;
    }
  }
}

/**
 * Get database connection status
 */
export function isDatabaseConnected(): boolean {
  return isConnected;
}

/**
 * Execute database operation with retry logic
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');

      if (attempt === maxRetries) {
        logger.error(`Database operation failed after ${maxRetries} attempts`, {
          error: lastError.message
        });
        throw lastError;
      }

      logger.warn(`Database operation attempt ${attempt} failed, retrying...`, {
        error: lastError.message,
        retryIn: delay / 1000
      });

      await new Promise(resolve => setTimeout(resolve, delay));
      delay *= 2; // Exponential backoff
    }
  }

  throw lastError!;
}

// Export Prisma namespace and enums
export { Prisma };
export type {
  CustomerType,
  VehicleStatus,
  BusinessType,
  ReservationStatus,
  AgreementType,
  AgreementStatus,
  InvoiceStatus,
  ReceiptType,
  ExportStatus,
  QuoteStatus,
  ScheduleStatus
} from '@prisma/client';

// For now, export basic types that services need
// These can be expanded as needed
export type User = {
  id: string;
  orgId: string;
  email: string;
  passwordHash: string;
  createdAt: Date;
  updatedAt: Date;
};

export type Role = {
  id: string;
  orgId: string;
  name: string;
  permissions: string[];
};

export type Org = {
  id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
};

export type Customer = {
  id: string;
  orgId: string;
  type: CustomerType;
  name: string;
  email?: string | null;
  phone?: string | null;
  createdAt: Date;
  updatedAt: Date;
};

export type Vehicle = {
  id: string;
  orgId: string;
  vin?: string | null;
  plateNumber?: string | null;
  status: VehicleStatus;
  registrationNumber?: string | null;
  salikTag?: string | null;
  vmdSerial?: string | null;
  businessType?: BusinessType | null;
  location?: string | null;
  createdAt: Date;
  updatedAt: Date;
};

export type Agreement = {
  id: string;
  orgId: string;
  type: AgreementType;
  customerId: string;
  vehicleId: string;
  reservationId?: string | null;
  status: AgreementStatus;
  startDate: Date;
  endDate?: Date | null;
  total?: any | null; // Decimal type
  currency?: string | null;
  createdAt: Date;
  updatedAt: Date;
};

export type Invoice = {
  id: string;
  orgId: string;
  agreementId?: string | null;
  customerId: string;
  amount: any; // Decimal type
  currency: string;
  status: InvoiceStatus;
  issueDate: Date;
  dueDate?: Date | null;
  createdAt: Date;
  updatedAt: Date;
};

export type Receipt = {
  id: string;
  orgId: string;
  customerId: string;
  invoiceId?: string | null;
  amount: any; // Decimal type
  type: ReceiptType;
  applied: boolean;
  createdAt: Date;
  updatedAt: Date;
};

export type Reservation = {
  id: string;
  orgId: string;
  customerId: string;
  vehicleId?: string | null;
  fromDate: Date;
  toDate: Date;
  status: ReservationStatus;
  notes?: string | null;
  createdAt: Date;
  updatedAt: Date;
};

