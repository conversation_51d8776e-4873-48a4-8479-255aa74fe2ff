"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Prisma = void 0;
exports.getPrisma = getPrisma;
exports.initializeDatabase = initializeDatabase;
exports.checkDatabaseHealth = checkDatabaseHealth;
exports.disconnectDatabase = disconnectDatabase;
exports.isDatabaseConnected = isDatabaseConnected;
exports.withRetry = withRetry;
const client_1 = require("@prisma/client");
Object.defineProperty(exports, "Prisma", { enumerable: true, get: function () { return client_1.Prisma; } });
const logger_1 = require("@a-realm/logger");
const logger = (0, logger_1.createLogger)('database');
let prisma;
let isConnected = false;
let connectionRetries = 0;
const MAX_RETRIES = 5;
const RETRY_DELAY = 5000; // 5 seconds
function getPrisma() {
    if (!prisma) {
        prisma = new client_1.PrismaClient({
            log: ['error', 'warn'],
            datasources: {
                db: {
                    url: process.env.POSTGRES_URL
                }
            }
        });
    }
    return prisma;
}
/**
 * Initialize database connection with retry logic
 */
async function initializeDatabase() {
    const client = getPrisma();
    while (connectionRetries < MAX_RETRIES) {
        try {
            await client.$connect();
            await client.$queryRaw `SELECT 1`; // Test connection
            isConnected = true;
            connectionRetries = 0;
            logger.info('Database connected successfully');
            return;
        }
        catch (error) {
            connectionRetries++;
            isConnected = false;
            logger.error(`Database connection attempt ${connectionRetries} failed`, {
                error: error instanceof Error ? error.message : 'Unknown error',
                retryIn: RETRY_DELAY / 1000
            });
            if (connectionRetries >= MAX_RETRIES) {
                logger.error('Max database connection retries exceeded');
                throw new Error(`Failed to connect to database after ${MAX_RETRIES} attempts`);
            }
            await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        }
    }
}
/**
 * Check database health
 */
async function checkDatabaseHealth() {
    try {
        const client = getPrisma();
        await client.$queryRaw `SELECT 1`;
        return { healthy: true };
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown database error';
        logger.error('Database health check failed', { error: errorMessage });
        return { healthy: false, error: errorMessage };
    }
}
/**
 * Gracefully disconnect from database
 */
async function disconnectDatabase() {
    if (prisma) {
        try {
            await prisma.$disconnect();
            isConnected = false;
            logger.info('Database disconnected gracefully');
        }
        catch (error) {
            logger.error('Error during database disconnect', {
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
        finally {
            prisma = undefined;
        }
    }
}
/**
 * Get database connection status
 */
function isDatabaseConnected() {
    return isConnected;
}
/**
 * Execute database operation with retry logic
 */
async function withRetry(operation, maxRetries = 3, delay = 1000) {
    let lastError;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return await operation();
        }
        catch (error) {
            lastError = error instanceof Error ? error : new Error('Unknown error');
            if (attempt === maxRetries) {
                logger.error(`Database operation failed after ${maxRetries} attempts`, {
                    error: lastError.message
                });
                throw lastError;
            }
            logger.warn(`Database operation attempt ${attempt} failed, retrying...`, {
                error: lastError.message,
                retryIn: delay / 1000
            });
            await new Promise(resolve => setTimeout(resolve, delay));
            delay *= 2; // Exponential backoff
        }
    }
    throw lastError;
}
//# sourceMappingURL=index.js.map